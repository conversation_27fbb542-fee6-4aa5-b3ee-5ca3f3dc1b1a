import type { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from 'zod';
import { GET_FIGMA_DATA_TOOL } from '../../consts/index.js';
import { getFigmaData } from "../../api/service.js";

export const parameters = {
  fileId: z
    .string()
    .describe(
      "The ID of the Figma file to fetch, often found in a provided URL like figma.com/(file|design)/<fileKey>/...",
    ),
  nodeId: z
    .string()
    .optional()
    .describe(
      "The ID of the node to fetch, often found as URL parameter node-id=<nodeId>, always use if provided",
    ),
  depth: z
    .number()
    .optional()
    .describe(
      "OPTIONAL. Do NOT use unless explicitly requested by the user. Controls how many levels deep to traverse the node tree.",
    ),
};

const parametersSchema = z.object(parameters);
export type FigmaDataParams = z.infer<typeof parametersSchema>;
export const registryTool = (server: McpServer) => {
  server.tool(
    GET_FIGMA_DATA_TOOL.name,
    GET_FIGMA_DATA_TOOL.description,
    parameters,
    async (params) => {
      try {
        const response = await getFigmaData(params);
        if (response.success) {
          const json = JSON.stringify(response.data);
          return {
            content: [
              {
                type: 'text',
                text: json,
              },
              ...GET_FIGMA_DATA_TOOL.extraContent,
            ]
          }
        } else {
          return {
            content: [
              {
                type: 'text',
                text: response.data.message || 'failed to fetch figma data',
              },
            ]
          }
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : JSON.stringify(error);
        return {
          content: [
            {
              type: 'text',
              text: 'something went wrong: ' + message,
            },
          ]
        }
      }
    },
  )
}
