import { McpServer } from '@modelcontextprotocol/sdk/server/mcp'
import { z } from 'zod'
import { GET_COMPONENT_DOC_TOOL } from '../../consts/index.js'
import { getComponentDoc } from '../../api/service.js';
export const parameters = {
  clientLanguages: z
    .string()
    .describe(
      "A comma separated list of programming languages used by the client in the current context in string form,e.g. javascript, html,css,typescript , etc. If you do not know, please list unknown . This is used for logging purposes to understand which languages are being used. If you are unsure, it is better to list unknown than to make a guess.",
    ),
  clientFrameworks: z
    .string()
    .optional()
    .describe(
      "A comma separated list of frameworks used by the client in the current context, e.g. react , vue, django etc. If you do not know, please list unknown . This is used for logging purposes to understand which frameworks are being used. If you are unsure, it is better to list unknown than to make a guess",
    ),
  clientName: z
    .string()
    .optional()
    .describe(
      "The name of the MCP client making this request e.g. cursor , claude code claude desktop , chatgpt desktop etc",
    ),
  componentName: z.string().describe("The unique name of the component, such as Button, Table, Modal, etc. This name is used to identify and reference a specific component within the design system. It should typically be clear, concise, and descriptive."),
  componentPackage: z.string().describe("The package name of the component, such as @otakus/design, @otakus/pro-system-components, @otakus/icons, etc. This name is used to identify and reference a specific component within the design system. It should typically be clear, concise, and descriptive."),
}

const parametersSchema = z.object(parameters);
export type ComponentDocParams = z.infer<typeof parametersSchema>;

export const registryTool = (server: McpServer) => {
  server.tool(
    GET_COMPONENT_DOC_TOOL.name,
    GET_COMPONENT_DOC_TOOL.description,
    parameters,
    async (params) => {
      try {
        const response = await getComponentDoc(params);
        if (response.success) {
          return {
            content: [
              {
                type: 'text',
                text: response?.data?.result,
              },
              ...GET_COMPONENT_DOC_TOOL.extraContent,
            ]
          }
        } else {
          return {
            content: [
              {
                type: 'text',
                text: response?.message || 'get component doc failed',
              },
            ]
          }
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : JSON.stringify(error);
        return {
          content: [
            {
              type: 'text',
              text: 'something went wrong: ' + message,
            },
          ]
        }
      }
    }
  )
}
