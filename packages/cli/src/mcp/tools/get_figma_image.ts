import type { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from 'zod';
import { GET_FIGMA_IMAGE_TOOL } from '../../consts/index.js';
import { getFigmaImage } from "../../api/service.js";

export const parameters = {
  fileId: z
    .string()
    .describe(
      "The ID of the Figma file to fetch, often found in a provided URL like figma.com/(file|design)/<fileKey>/...",
    ),
  nodeId: z
    .string()
    .optional()
    .describe(
      "The ID of the node to fetch, often found as URL parameter node-id=<nodeId>, always use if provided",
    ),
  depth: z
    .number()
    .optional()
    .describe(
      "OPTIONAL. Do NOT use unless explicitly requested by the user. Controls how many levels deep to traverse the node tree.",
    ),
};

const parametersSchema = z.object(parameters);
export type FigmaDataParams = z.infer<typeof parametersSchema>;
export const registryTool = (server: McpServer) => {
  server.tool(
    GET_FIGMA_IMAGE_TOOL.name,
    GET_FIGMA_IMAGE_TOOL.description,
    parameters,
    async (params) => {
      try {
        const response = await getFigmaImage(params);
        if (response.success) {
          const imageUrl = response?.data.images;
          if (!imageUrl) {
            return {
              content: [
                {
                  type: 'text',
                  text: 'get figma image failed',
                },
              ]
            }
          }
          // 下载图片内容
          const imageResponse = await fetch(imageUrl);
          if (!imageResponse.ok) {
            throw new Error(`Failed to fetch image: ${imageResponse.statusText}`);
          }

          // 获取图片的二进制数据
          const imageBuffer = await imageResponse.arrayBuffer();

          // 将二进制数据转换为 Base64 字符串
          const base64String = Buffer.from(imageBuffer).toString('base64');
          return {
            content: [
              {
                type: 'image',
                data: base64String,
                mimeType: "image/png",
              },
            ]
          }
        }
        return {
          content: [
            {
              type: 'text',
              text: 'get figma image failed',
            },
          ]
        }

      } catch (error) {
        const message = error instanceof Error ? error.message : JSON.stringify(error);
        return {
          content: [
            {
              type: 'text',
              text: 'something went wrong: ' + message,
            },
          ]
        }
      }
    },
  )
}
