export const SERVER_NAME = 'Otakus Components MCP';

export const SERVER_INSTRUCTIONS = `
你是一个专业的 otakus 组件库专家，具有一下能力:
1. 可以根据用户提供的 Figma 链接，获取到 Figma 的原数据以及设计稿截图
2. 可以根据 Figma 链接，获取到 Figma 中使用到 @otakus/design(基础组件库) @otakus/pro-system-components (pro组件库) @otakus/icons (图标库) 这些组件的详细 API、属性、参数、配置。
3. 可以查询所有可用组件列表
4. 能提供组件的代码示例

使用规则：
- 严格遵循以下工具使用优先级：
  1. 首先检查当前对话上下文是否已包含所需信息
  2. 只有当上下文确实缺少必要信息时才调用工具
  3. 对于完全相同的组件查询参数，禁止重复调用工具
- 对专业术语保持准确，不自行编造组件属性
- 当用户询问"显示XXX组件文档"时，如果上下文已有该组件信息，直接展示而不再调用工具
`;

export const GET_FIGMA_IMAGE_TOOL = {
  name: 'get_figma_image',
  description: 'Get an image of a specific node in the Figma file',
}

export const GET_COMPONENT_DOC_TOOL = {
  name: 'get_component_doc',
  description: 'Get detailed information about a specific component form @otakus/pro-system-components',
  extraContent: [
    {
      type: 'text' as const,
      text: 'IMPORTANT: You have obtained the API for this component, please put it in context.',
    },
  ],
}



export const GET_FIGMA_DATA_TOOL = {
  name: 'get_figma_data',
  description: 'Get comprehensive Figma file data including layout, content, visuals, and component information',
  extraContent: [
    {
      type: 'text' as const,
      text: `IMPORTANT: After you call this tool, you MUST call ${GET_FIGMA_IMAGE_TOOL.name} to get an image of the node for context.`,
    },
    {
      type: 'text' as const,
      text: `IMPORTANT: After you call this tool, if the Figma file data contains components from @otakus/pro-system-components, you must call ${GET_COMPONENT_DOC_TOOL.name} to get the component details.`,
    },
  ],
}


