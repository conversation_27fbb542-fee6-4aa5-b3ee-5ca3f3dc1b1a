import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import type { RequestConfig, ApiResponse, ApiError } from './types.js';

/**
 * 通用的 HTTP 请求客户端
 */
export class ApiClient {
  private instance: AxiosInstance;
  private baseURL: string;

  constructor(baseURL: string = 'http://127.0.0.1:3000/api') {
    this.baseURL = baseURL;
    this.instance = axios.create({
      baseURL,
      timeout: 100000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 可以在这里添加认证 token 等
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        const apiError: ApiError = {
          message: error.message || 'Request failed',
          status: error.response?.status,
          code: error.code,
          data: error.response?.data,
        };
        return Promise.reject(apiError);
      }
    );
  }

  /**
   * POST 请求
   */
  async post<T = any>(url: string, data?: any, config?: Omit<RequestConfig, 'method' | 'data'>): Promise<ApiResponse<T>> {
    try {
      return this.instance.post(url, data, config);
    } catch (error) {
      const message = error instanceof Error ? error.message : JSON.stringify(error);
      throw error;
    }
  }

}

// 创建默认实例
export const apiClient = new ApiClient();
export const post = apiClient.post.bind(apiClient);