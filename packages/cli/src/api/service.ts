import { post } from './client.js';
import { FigmaDataParams } from '../mcp/tools/get_figma_data.js';
import { ComponentDocParams } from '../mcp/tools/get_component_doc.js';

export const getFigmaData = async (data: FigmaDataParams) => {
  const response = await post('/figma/data', { ...data, apiKey: process.env.FIGMA_API_KEY });
  return response.data;
}

export const getFigmaImage = async (data: FigmaDataParams) => {
  const response = await post('/figma/image', { ...data, apiKey: process.env.FIGMA_API_KEY });
  return response.data;
}

export const getComponentDoc = async (data: ComponentDocParams) => {
  const response = await post('/docs/component', data);
  return response.data;
}