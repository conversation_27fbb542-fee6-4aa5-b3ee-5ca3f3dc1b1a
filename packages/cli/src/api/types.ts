/**
 * API 请求相关的类型定义
 */

export interface RequestConfig {
  method?: 'get' | 'post' | 'put' | 'delete' | 'patch';
  headers?: Record<string, string>;
  params?: Record<string, any>;
  data?: any;
  timeout?: number;
}
export interface Response<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

export interface ApiResponse<T = any> {
  data: Response<T>;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  data?: any;
}