# Button

## API

### 新增

新增 Outlined 按钮类型。

| 属性 | 说明         | 类型                                                                 | 默认值    | 版本  |
| :--- | :----------- | :------------------------------------------------------------------- | :-------- | :---- |
| type | 设置按钮类型 | `primary` \| `outlined` \| `dashed` \| `link` \| `text` \| `default` | `default` |       |
| size | 设置按钮大小 | `large` \| `middle` \| `small` \| `mini`                             | `default` | 0.9.1 |

### Antd

通用属性参考：[通用属性](/components/common-props)

通过设置 Button 的属性来产生不同的按钮样式，推荐顺序为：`type` -> `shape` -> `size` -> `loading` -> `disabled`。

按钮的属性说明如下：

| 属性            | 说明                                                                                                                                 | 类型                                                                 | 默认值    | 版本   |
| --------------- | ------------------------------------------------------------------------------------------------------------------------------------ | -------------------------------------------------------------------- | --------- | ------ |
| autoInsertSpace | 我们默认提供两个汉字之间的空格，可以设置 `autoInsertSpace` 为 `false` 关闭                                                           | boolean                                                              | `true`    | 5.17.0 |
| block           | 将按钮宽度调整为其父宽度的选项                                                                                                       | boolean                                                              | false     |        |
| classNames      | 语义化结构 class                                                                                                                     | [Record<SemanticDOM, string>](#semantic-dom)                         | -         | 5.4.0  |
| danger          | 设置危险按钮                                                                                                                         | boolean                                                              | false     |        |
| disabled        | 设置按钮失效状态                                                                                                                     | boolean                                                              | false     |        |
| ghost           | 幽灵属性，使按钮背景透明                                                                                                             | boolean                                                              | false     |        |
| href            | 点击跳转的地址，指定此属性 button 的行为和 a 链接一致                                                                                | string                                                               | -         |        |
| htmlType        | 设置 `button` 原生的 `type` 值，可选值请参考 [HTML 标准](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/button#attr-type) | string                                                               | `button`  |        |
| icon            | 设置按钮的图标组件                                                                                                                   | ReactNode                                                            | -         |        |
| iconPosition    | 设置按钮图标组件的位置                                                                                                               | `start` \| `end`                                                     | `start`   | 5.17.0 |
| loading         | 设置按钮载入状态                                                                                                                     | boolean \| { delay: number }                                         | false     |        |
| shape           | 设置按钮形状                                                                                                                         | `default` \| `circle` \| `round`                                     | `default` |        |
| size            | 设置按钮大小                                                                                                                         | `large` \| `middle` \| `small`                                       | `middle`  |        |
| styles          | 语义化结构 style                                                                                                                     | [Record<SemanticDOM, CSSProperties>](#semantic-dom)                  | -         | 5.4.0  |
| target          | 相当于 a 链接的 target 属性，href 存在时生效                                                                                         | string                                                               | -         |        |
| type            | 设置按钮类型                                                                                                                         | `primary` \| `outlined` \| `dashed` \| `link` \| `text` \| `default` | `default` |        |
| onClick         | 点击按钮时的回调                                                                                                                     | (event: React.MouseEvent<HTMLElement, MouseEvent>) => void           | -         |        |

支持原生 button 的其他所有属性。

<style>
.site-button-ghost-wrapper {
  padding: 16px;
  background: rgb(190, 200, 200);
}
</style>

## 示例

### 描边按钮

```tsx
/**
 * description: outlined 类型的按钮。
 */
import React from 'react';
import { Button, Flex, ConfigProvider } from '@otakus/design';

export default () => (
  <ConfigProvider>
    <Flex gap="small" wrap="wrap">
      <Button type="outlined" shape="round">
        Round Button
      </Button>
      <Button type="outlined" danger>
        Danger Button
      </Button>
      <Button type="outlined" disabled>
        Disabled Button
      </Button>
      <Button type="outlined" ghost>
        Ghost Button
      </Button>
      <Button type="outlined" loading>
        Loading Button
      </Button>
      <Button type="outlined" size="large">
        Large Button
      </Button>
      <Button type="outlined" size="small">
        Small Button
      </Button>
      <Button type="outlined" size="mini">
        Mini Button
      </Button>
    </Flex>
  </ConfigProvider>
);

```

### 按钮类型

```tsx
/**
 * description: 按钮有五种类型：主按钮、次按钮、虚线按钮、文本按钮和链接按钮。主按钮在同一个操作区域最多出现一次。
 */
import React from 'react';
import { Button, Flex } from '@otakus/design';

const App: React.FC = () => (
  <Flex gap="small" wrap="wrap">
    <Button type="primary">Primary Button</Button>
    <Button>Default Button</Button>
    <Button type="dashed">Dashed Button</Button>
    <Button type="text">Text Button</Button>
    <Button type="link">Link Button</Button>
  </Flex>
);

export default App;

```

### 按钮图标

```tsx
/**
 * description: 可以通过 `icon `属性添加图标，并使用 `iconPosition` 调整图标的位置。
 */
import React from 'react';
import { SearchOutlined } from '@otakus/icons';
import { Button, Flex, Tooltip } from '@otakus/design';

const App: React.FC = () => (
  <Flex gap="small" vertical>
    <Flex wrap="wrap" gap="small">
      <Tooltip title="search">
        <Button type="primary" shape="circle" icon={<SearchOutlined />} />
      </Tooltip>
      <Button type="primary" shape="circle">
        A
      </Button>
      <Button type="primary" icon={<SearchOutlined />}>
        Search
      </Button>
      <Tooltip title="search">
        <Button shape="circle" icon={<SearchOutlined />} />
      </Tooltip>
      <Button icon={<SearchOutlined />}>Search</Button>
    </Flex>
    <Flex wrap="wrap" gap="small">
      <Tooltip title="search">
        <Button shape="circle" icon={<SearchOutlined />} />
      </Tooltip>
      <Button icon={<SearchOutlined />}>Search</Button>
      <Tooltip title="search">
        <Button type="dashed" shape="circle" icon={<SearchOutlined />} />
      </Tooltip>
      <Button type="dashed" icon={<SearchOutlined />}>
        Search
      </Button>
      <Button icon={<SearchOutlined />} href="https://www.google.com" />
    </Flex>
  </Flex>
);

export default App;

```

### 按钮图标位置

```tsx
/**
 * description: 通过设置 `iconPosition` 为 `start` 或 `end` 分别设置按钮图标的位置。
 */
import React, { useState } from 'react';
import { SearchOutlined } from '@otakus/icons';
import { Button, Divider, Flex, Radio, Space, Tooltip } from '@otakus/design';

const App: React.FC = () => {
  const [position, setPosition] = useState<'start' | 'end'>('end');

  return (
    <>
      <Space>
        <Radio.Group value={position} onChange={(e) => setPosition(e.target.value)}>
          <Radio.Button value="start">start</Radio.Button>
          <Radio.Button value="end">end</Radio.Button>
        </Radio.Group>
      </Space>
      <Divider orientation="left" plain>
        Preview
      </Divider>
      <Flex gap="small" vertical>
        <Flex wrap gap="small">
          <Tooltip title="search">
            <Button type="primary" shape="circle" icon={<SearchOutlined />} />
          </Tooltip>
          <Button type="primary" shape="circle">
            A
          </Button>
          <Button type="primary" icon={<SearchOutlined />} iconPosition={position}>
            Search
          </Button>
          <Tooltip title="search">
            <Button shape="circle" icon={<SearchOutlined />} />
          </Tooltip>
          <Button icon={<SearchOutlined />} iconPosition={position}>
            Search
          </Button>
        </Flex>
        <Flex wrap gap="small">
          <Tooltip title="search">
            <Button shape="circle" icon={<SearchOutlined />} />
          </Tooltip>
          <Button icon={<SearchOutlined />} type="text" iconPosition={position}>
            Search
          </Button>
          <Tooltip title="search">
            <Button type="dashed" shape="circle" icon={<SearchOutlined />} />
          </Tooltip>
          <Button type="dashed" icon={<SearchOutlined />} iconPosition={position}>
            Search
          </Button>
          <Button icon={<SearchOutlined />} href="https://www.google.com" iconPosition={position} />
          <Button type="primary" loading iconPosition={position}>
            Loading
          </Button>
        </Flex>
      </Flex>
    </>
  );
};

export default App;

```

### 按钮尺寸

```tsx
/**
 * description: 按钮有大、中、小三种尺寸。通过设置 `size` 为 `large` `small` 分别把按钮设为大、小尺寸。若不设置 `size`，则尺寸默认为中。
 */
import React, { useState } from 'react';
import { DownloadOutlined } from '@otakus/icons';
import { Button, Divider, Flex, Radio } from '@otakus/design';
import type { ConfigProviderProps } from '@otakus/design';

type SizeType = ConfigProviderProps['componentSize'];

const App: React.FC = () => {
  const [size, setSize] = useState<SizeType>('large'); // default is 'middle'
  return (
    <>
      <Radio.Group value={size} onChange={(e) => setSize(e.target.value)}>
        <Radio.Button value="large">Large</Radio.Button>
        <Radio.Button value="default">Default</Radio.Button>
        <Radio.Button value="small">Small</Radio.Button>
        <Radio.Button value="mini">Mini</Radio.Button>
      </Radio.Group>
      <Divider orientation="left" plain>
        Preview
      </Divider>
      <Flex gap="small" align="flex-start" vertical>
        <Flex gap="small" wrap="wrap">
          <Button type="primary" size={size}>
            Primary
          </Button>
          <Button size={size}>Default</Button>
          <Button type="dashed" size={size}>
            Dashed
          </Button>
        </Flex>
        <Button type="link" size={size}>
          Link
        </Button>
        <Flex gap="small" wrap="wrap">
          <Button type="primary" icon={<DownloadOutlined />} size={size} />
          <Button type="primary" shape="circle" icon={<DownloadOutlined />} size={size} />
          <Button type="primary" shape="round" icon={<DownloadOutlined />} size={size} />
          <Button type="primary" shape="round" icon={<DownloadOutlined />} size={size}>
            Download
          </Button>
          <Button type="primary" icon={<DownloadOutlined />} size={size}>
            Download
          </Button>
        </Flex>
      </Flex>
    </>
  );
};

export default App;

```

### 不可用状态

```tsx
/**
 * description: 添加 `disabled` 属性即可让按钮处于不可用状态，同时按钮样式也会改变。
 */
import React from 'react';
import { Button, Flex } from '@otakus/design';

const App: React.FC = () => (
  <Flex gap="small" align="flex-start" vertical>
    <Flex gap="small">
      <Button type="primary">Primary</Button>
      <Button type="primary" disabled>
        Primary(disabled)
      </Button>
    </Flex>
    <Flex gap="small">
      <Button>Default</Button>
      <Button disabled>Default(disabled)</Button>
    </Flex>
    <Flex gap="small">
      <Button type="dashed">Dashed</Button>
      <Button type="dashed" disabled>
        Dashed(disabled)
      </Button>
    </Flex>
    <Flex gap="small">
      <Button type="text">Text</Button>
      <Button type="text" disabled>
        Text(disabled)
      </Button>
    </Flex>
    <Flex gap="small">
      <Button type="link">Link</Button>
      <Button type="link" disabled>
        Link(disabled)
      </Button>
    </Flex>
    <Flex gap="small">
      <Button type="primary" href="https://ant.design/index-cn">
        Href Primary
      </Button>
      <Button type="primary" href="https://ant.design/index-cn" disabled>
        Href Primary(disabled)
      </Button>
    </Flex>
    <Flex gap="small">
      <Button danger>Danger Default</Button>
      <Button danger disabled>
        Danger Default(disabled)
      </Button>
    </Flex>
    <Flex gap="small">
      <Button danger type="text">
        Danger Text
      </Button>
      <Button danger type="text" disabled>
        Danger Text(disabled)
      </Button>
    </Flex>
    <Flex gap="small">
      <Button type="link" danger>
        Danger Link
      </Button>
      <Button type="link" danger disabled>
        Danger Link(disabled)
      </Button>
    </Flex>
    <Flex gap="small" className="site-button-ghost-wrapper">
      <Button ghost>Ghost</Button>
      <Button ghost disabled>
        Ghost(disabled)
      </Button>
    </Flex>
  </Flex>
);

export default App;

```

### 加载中状态

```tsx
/**
 * description: 添加 `loading` 属性即可让按钮处于加载状态，最后三个按钮演示点击后进入加载状态。
 */
import React, { useState } from 'react';
import { PoweroffOutlined } from '@otakus/icons';
import { Button, Flex } from '@otakus/design';

const App: React.FC = () => {
  const [loadings, setLoadings] = useState<boolean[]>([]);

  const enterLoading = (index: number) => {
    setLoadings((prevLoadings) => {
      const newLoadings = [...prevLoadings];
      newLoadings[index] = true;
      return newLoadings;
    });

    setTimeout(() => {
      setLoadings((prevLoadings) => {
        const newLoadings = [...prevLoadings];
        newLoadings[index] = false;
        return newLoadings;
      });
    }, 6000);
  };

  return (
    <Flex gap="small" vertical>
      <Flex gap="small" align="center" wrap="wrap">
        <Button type="primary" loading>
          Loading
        </Button>
        <Button type="primary" size="small" loading>
          Loading
        </Button>
        <Button type="primary" icon={<PoweroffOutlined />} loading />
      </Flex>
      <Flex gap="small" wrap="wrap">
        <Button type="primary" loading={loadings[0]} onClick={() => enterLoading(0)}>
          Click me!
        </Button>
        <Button
          type="primary"
          icon={<PoweroffOutlined />}
          loading={loadings[1]}
          onClick={() => enterLoading(1)}
        >
          Click me!
        </Button>
        <Button
          type="primary"
          icon={<PoweroffOutlined />}
          loading={loadings[2]}
          onClick={() => enterLoading(2)}
        />
      </Flex>
    </Flex>
  );
};

export default App;

```

### 多个按钮组合

```tsx
/**
 * description: 按钮组合使用时，推荐使用 1 个主操作 + n 个次操作，3 个以上操作时把更多操作放到 [Dropdown.Button](/components/dropdown#dropdown-demo-dropdown-button) 中组合使用。
 */
import React from 'react';
import type { MenuProps } from '@otakus/design';
import { Button, Dropdown, Flex } from '@otakus/design';

const onMenuClick: MenuProps['onClick'] = (e) => {
  console.log('click', e);
};

const items = [
  {
    key: '1',
    label: '1st item'
  },
  {
    key: '2',
    label: '2nd item'
  },
  {
    key: '3',
    label: '3rd item'
  }
];

const App: React.FC = () => (
  <Flex align="flex-start" gap="small" vertical>
    <Button type="primary">primary</Button>
    <Button>secondary</Button>
    <Dropdown.Button menu={{ items, onClick: onMenuClick }}>Actions</Dropdown.Button>
  </Flex>
);

export default App;

```

### 幽灵按钮

```tsx
/**
 * description: 幽灵按钮将按钮的内容反色，背景变为透明，常用在有色背景上。
 */
import React from 'react';
import { Button, Flex } from '@otakus/design';

const App: React.FC = () => (
  <Flex wrap="wrap" gap="small" className="site-button-ghost-wrapper">
    <Button type="primary" ghost>
      Primary
    </Button>
    <Button ghost>Default</Button>
    <Button type="dashed" ghost>
      Dashed
    </Button>
    <Button type="primary" danger ghost>
      Danger
    </Button>
  </Flex>
);

export default App;

```

### 危险按钮

```tsx
import React from 'react';
import { Button, Flex } from '@otakus/design';

const App: React.FC = () => (
  <Flex wrap="wrap" gap="small">
    <Button type="primary" danger>
      Primary
    </Button>
    <Button danger>Default</Button>
    <Button type="dashed" danger>
      Dashed
    </Button>
    <Button type="text" danger>
      Text
    </Button>
    <Button type="link" danger>
      Link
    </Button>
  </Flex>
);

export default App;

```

### Block Button

```tsx
/**
 * description: `block` 属性将使按钮适合其父宽度。
 */
import React from 'react';
import { Button, Flex } from '@otakus/design';

const App: React.FC = () => (
  <Flex vertical gap="small" style={{ width: '100%' }}>
    <Button type="primary" block>
      Primary
    </Button>
    <Button block>Default</Button>
    <Button type="dashed" block>
      Dashed
    </Button>
    <Button disabled block>
      disabled
    </Button>
    <Button type="text" block>
      text
    </Button>
    <Button type="link" block>
      Link
    </Button>
  </Flex>
);

export default App;

```

### 废弃的 Block 组

```tsx
import React from 'react';
import { DownloadOutlined } from '@otakus/icons';
import { Button, Tooltip } from '@otakus/design';
import type { GetProps } from '@otakus/design';

type ButtonGroupProps = GetProps<typeof Button.Group>;

const CustomGroup: React.FC<ButtonGroupProps> = (props) => (
  <Button.Group {...props}>
    <Button type="primary">Button 1</Button>
    <Button type="primary">Button 2</Button>
    <Tooltip title="Tooltip">
      <Button type="primary" icon={<DownloadOutlined />} disabled />
    </Tooltip>
    <Tooltip title="Tooltip">
      <Button type="primary" icon={<DownloadOutlined />} />
    </Tooltip>
  </Button.Group>
);

const App: React.FC = () => (
  <>
    <CustomGroup size="small" />
    <br />
    <CustomGroup />
    <br />
    <CustomGroup size="large" />
  </>
);

export default App;

```

### 加载中状态 bug 还原

```tsx
import React from 'react';
import { PoweroffOutlined } from '@otakus/icons';
import { Button, Flex } from '@otakus/design';

const Text1 = () => <>部署</>;
const Text2 = () => <span>部署</span>;
const Text3 = () => <>Submit</>;

const App: React.FC = () => (
  <Flex wrap="wrap" gap="small">
    <Button>
      <span>
        <span>部署</span>
      </span>
    </Button>
    <Button loading>部署</Button>
    <Button loading>
      <Text1 />
    </Button>
    <Button loading>
      <Text2 />
    </Button>
    <Button loading>
      <Text3 />
    </Button>
    <Button loading icon={<PoweroffOutlined />}>
      <Text1 />
    </Button>
    <Button loading>按钮</Button>
  </Flex>
);

export default App;

```

### 组件 Token

```tsx
/**
 * description: 组件 Token，模仿 MUI 风格的 Button。
 */
import React from 'react';
import { Button, ConfigProvider, Flex } from '@otakus/design';

const App: React.FC = () => (
  <ConfigProvider
    theme={{
      components: {
        Button: {
          algorithm: true,
          colorPrimary: '#1976d2',
          controlHeight: 36,
          primaryShadow:
            '0 3px 1px -2px rgba(0,0,0,0.2), 0 2px 2px 0 rgba(0,0,0,0.14), 0 1px 5px 0 rgba(0,0,0,0.12)',
          fontWeight: 500,
          defaultBorderColor: 'rgba(25, 118, 210, 0.5)',
          colorText: '#1976d2',
          defaultColor: '#1976d2',
          borderRadius: 4,
          colorTextDisabled: 'rgba(0, 0, 0, 0.26)',
          colorBgContainerDisabled: 'rgba(0, 0, 0, 0.12)',
          contentFontSizeSM: 12
        }
      }
    }}
  >
    <Flex gap="small" vertical>
      <Flex wrap="wrap" gap="small">
        <Button type="text">TEXT</Button>
        <Button type="primary">CONTAINED</Button>
        <Button>OUTLINED</Button>
      </Flex>
      <Flex wrap="wrap" gap="small">
        <Button type="text" disabled>
          TEXT
        </Button>
        <Button type="primary" disabled>
          CONTAINED
        </Button>
        <Button disabled>OUTLINED</Button>
      </Flex>
      <Flex wrap="wrap" gap="small">
        <Button type="text" size="small">
          TEXT
        </Button>
        <Button type="primary" size="small">
          CONTAINED
        </Button>
        <Button size="small">OUTLINED</Button>
      </Flex>
    </Flex>
  </ConfigProvider>
);

export default App;

```

### 渐变按钮

```tsx
/**
 * description: 自定义为渐变背景按钮。
 */
import React from 'react';
import { Button, ConfigProvider, Space } from '@otakus/design';

const App: React.FC = () => (
  <ConfigProvider
    theme={{
      components: {
        Button: {
          colorPrimary: 'linear-gradient(90deg, #FF4E50, #F9D423) !important',
          primaryShadow: 'none',
          defaultBg: 'linear-gradient(90deg, #aea4e3, #d3ffe8) !important',
          defaultShadow: 'none',
          defaultColor: '#fff !important',
          lineWidth: 0
        }
      }
    }}
  >
    <Space>
      <Button type="primary">Primary Button</Button>
      <Button>Default Button</Button>
    </Space>
  </ConfigProvider>
);

export default App;

```

### 移除两个汉字之间的空格

```tsx
/**
 * description: 我们默认在两个汉字之间添加空格，可以通过设置 `autoInsertSpace` 为 `false` 关闭。
 */
import React from 'react';
import { Button, Flex } from '@otakus/design';

const App: React.FC = () => (
  <Flex gap="middle" wrap>
    <Button type="primary" autoInsertSpace={false}>
      确定
    </Button>
    <Button type="primary" autoInsertSpace>
      确定
    </Button>
  </Flex>
);

export default App;

```

