# Affix

## API

通用属性参考：[通用属性](/components/common-props)

| 参数         | 说明                                                                   | 类型                        | 默认值       |
| ------------ | ---------------------------------------------------------------------- | --------------------------- | ------------ |
| offsetBottom | 距离窗口底部达到指定偏移量后触发                                       | number                      | -            |
| offsetTop    | 距离窗口顶部达到指定偏移量后触发                                       | number                      | 0            |
| target       | 设置 `Affix` 需要监听其滚动事件的元素，值为一个返回对应 DOM 元素的函数 | () => HTMLElement           | () => window |
| onChange     | 固定状态改变时触发的回调函数                                           | (affixed?: boolean) => void | -            |

**注意：**`Affix` 内的元素不要使用绝对定位，如需要绝对定位的效果，可以直接设置 `Affix` 为绝对定位：

```js
<Affix style={{ position: 'absolute', top: y, left: x }}>...</Affix>
```

## 示例

### Basic

```tsx
import React from 'react';
import { Affix, Button } from '@otakus/design';

const App: React.FC = () => {
  const [top, setTop] = React.useState<number>(100);
  const [bottom, setBottom] = React.useState<number>(100);
  return (
    <>
      <Affix offsetTop={top}>
        <Button type="primary" onClick={() => setTop(top + 10)}>
          Affix top
        </Button>
      </Affix>
      <br />
      <Affix offsetBottom={bottom}>
        <Button type="primary" onClick={() => setBottom(bottom + 10)}>
          Affix bottom
        </Button>
      </Affix>
    </>
  );
};

export default App;

```

### Callback

```tsx
import React from 'react';
import { Affix, Button } from '@otakus/design';

const App: React.FC = () => (
  <Affix offsetTop={120} onChange={(affixed) => console.log(affixed)}>
    <Button>120px to affix top</Button>
  </Affix>
);

export default App;

```

### Container to scroll.

```tsx
import React from 'react';
import { Affix, Button } from '@otakus/design';

const containerStyle: React.CSSProperties = {
  width: '100%',
  height: 100,
  overflow: 'auto',
  border: '1px solid #40a9ff'
};

const style: React.CSSProperties = {
  width: '100%',
  height: 1000
};

const App: React.FC = () => {
  const [container, setContainer] = React.useState<HTMLDivElement | null>(null);
  return (
    <div style={containerStyle} ref={setContainer}>
      <div style={style}>
        <Affix target={() => container}>
          <Button type="primary">Fixed at the top of container</Button>
        </Affix>
      </div>
    </div>
  );
};

export default App;

```

### debug

```tsx
import React, { useState } from 'react';
import { Affix, Button } from '@otakus/design';

const App: React.FC = () => {
  const [top, setTop] = useState(10);

  return (
    <div style={{ height: 10000 }}>
      <div>Top</div>
      <Affix offsetTop={top}>
        <div style={{ background: 'red' }}>
          <Button type="primary" onClick={() => setTop(top + 10)}>
            Affix top
          </Button>
        </div>
      </Affix>
      <div>Bottom</div>
    </div>
  );
};

export default App;

```

