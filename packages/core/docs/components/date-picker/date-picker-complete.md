# DatePicker

## API

通用属性参考：[通用属性](/components/common-props)

日期类组件包括以下五种形式。

- DatePicker
- DatePicker\[picker="month"]
- DatePicker\[picker="week"]
- DatePicker\[picker="year"]
- DatePicker\[picker="quarter"] (4.1.0 新增)
- RangePicker

### 国际化配置

默认配置为 en-US，如果你需要设置其他语言，推荐在入口处使用我们提供的国际化组件，详见：[ConfigProvider 国际化](https://ant.design/components/config-provider-cn/)。

如有特殊需求（仅修改单一组件的语言），请使用 locale 参数，参考：[默认配置](https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json)。

```js
// 默认语言为 en-US，如果你需要设置其他语言，推荐在入口文件全局设置 locale
// 确保还导入相关的 dayjs 文件，否则所有文本的区域设置都不会更改（例如范围选择器月份）
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';

import 'dayjs/locale/zh-cn';

dayjs.locale('zh-cn');

<ConfigProvider locale={locale}>
  <DatePicker defaultValue={dayjs('2015-01-01', 'YYYY-MM-DD')} />
</ConfigProvider>;
```


:::warning
在搭配 Next.js 的 App Router 使用时，注意在引入 dayjs 的 locale 文件时加上 `'use client'`。这是由于 Ant Design 的组件都是客户端组件，在 RSC 中引入 dayjs 的 locale 文件将不会在客户端生效。
:::

### 共同的 API

以下 API 为 DatePicker、 RangePicker 共享的 API。

| 参数                  | 说明                                                                                                                                                                                                                                                                            | 类型                                                                                                                                                                                                              | 默认值                                                                                                                             | 版本                |
| --------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------- | ------------------- |
| allowClear            | 自定义清除按钮                                                                                                                                                                                                                                                                  | boolean \| { clearIcon?: ReactNode }                                                                                                                                                                              | true                                                                                                                               | 5.8.0: 支持对象类型 |
| autoFocus             | 自动获取焦点                                                                                                                                                                                                                                                                    | boolean                                                                                                                                                                                                           | false                                                                                                                              |                     |
| className             | 选择器 className                                                                                                                                                                                                                                                                | string                                                                                                                                                                                                            | -                                                                                                                                  |                     |
| dateRender            | 自定义日期单元格的内容，5.4.0 起用 `cellRender` 代替                                                                                                                                                                                                                            | function(currentDate: dayjs, today: dayjs) => React.ReactNode                                                                                                                                                     | -                                                                                                                                  | < 5.4.0             |
| cellRender            | 自定义单元格的内容                                                                                                                                                                                                                                                              | (current: dayjs, info: { originNode: React.ReactElement,today: DateType, range?: 'start' \| 'end', type: PanelMode, locale?: Locale, subType?: 'hour' \| 'minute' \| 'second' \| 'meridiem' }) => React.ReactNode | -                                                                                                                                  | 5.4.0               |
| components            | 自定义面板                                                                                                                                                                                                                                                                      | Record<Panel \| 'input', React.ComponentType>                                                                                                                                                                     | -                                                                                                                                  | 5.14.0              |
| disabled              | 禁用                                                                                                                                                                                                                                                                            | boolean                                                                                                                                                                                                           | false                                                                                                                              |                     |
| disabledDate          | 不可选择的日期                                                                                                                                                                                                                                                                  | (currentDate: dayjs, info: { from?: dayjs }) => boolean                                                                                                                                                           | -                                                                                                                                  | `info`: 5.14.0      |
| format                | 设置日期格式，为数组时支持多格式匹配，展示以第一个为准。配置参考 [dayjs#format](https://day.js.org/docs/zh-CN/display/format#%E6%94%AF%E6%8C%81%E7%9A%84%E6%A0%BC%E5%BC%8F%E5%8C%96%E5%8D%A0%E4%BD%8D%E7%AC%A6%E5%88%97%E8%A1%A8)。示例：[自定义格式](#date-picker-demo-format) | [formatType](#formattype)                                                                                                                                                                                         | [rc-picker](https://github.com/react-component/picker/blob/f512f18ed59d6791280d1c3d7d37abbb9867eb0b/src/utils/uiUtil.ts#L155-L177) |                     |
| order                 | 多选、范围时是否自动排序                                                                                                                                                                                                                                                        | boolean                                                                                                                                                                                                           | true                                                                                                                               | 5.14.0              |
| preserveInvalidOnBlur | 失去焦点是否要清空输入框内无效内容                                                                                                                                                                                                                                              | boolean                                                                                                                                                                                                           | false                                                                                                                              | 5.14.0              |
| popupClassName        | 额外的弹出日历 className                                                                                                                                                                                                                                                        | string                                                                                                                                                                                                            | -                                                                                                                                  | 4.23.0              |
| getPopupContainer     | 定义浮层的容器，默认为 body 上新建 div                                                                                                                                                                                                                                          | function(trigger)                                                                                                                                                                                                 | -                                                                                                                                  |                     |
| inputReadOnly         | 设置输入框为只读（避免在移动设备上打开虚拟键盘）                                                                                                                                                                                                                                | boolean                                                                                                                                                                                                           | false                                                                                                                              |                     |
| locale                | 国际化配置                                                                                                                                                                                                                                                                      | object                                                                                                                                                                                                            | [默认配置](https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json)                        |                     |
| minDate               | 最小日期，同样会限制面板的切换范围                                                                                                                                                                                                                                              | dayjs                                                                                                                                                                                                             | -                                                                                                                                  | 5.14.0              |
| maxDate               | 最大日期，同样会限制面板的切换范围                                                                                                                                                                                                                                              | dayjs                                                                                                                                                                                                             | -                                                                                                                                  | 5.14.0              |
| mode                  | 日期面板的状态                                                                                                                                                                                                                                                                  | `time` \| `date` \| `month` \| `year` \| `decade`                                                                                                                                                                 | -                                                                                                                                  |                     |
| needConfirm           | 是否需要确认按钮，为 `false` 时失去焦点即代表选择。当设置 `multiple` 时默认为 `false`                                                                                                                                                                                           | boolean                                                                                                                                                                                                           | -                                                                                                                                  | 5.14.0              |
| nextIcon              | 自定义下一个图标                                                                                                                                                                                                                                                                | ReactNode                                                                                                                                                                                                         | -                                                                                                                                  | 4.17.0              |
| open                  | 控制弹层是否展开                                                                                                                                                                                                                                                                | boolean                                                                                                                                                                                                           | -                                                                                                                                  |                     |
| panelRender           | 自定义渲染面板                                                                                                                                                                                                                                                                  | (panelNode) => ReactNode                                                                                                                                                                                          | -                                                                                                                                  | 4.5.0               |
| picker                | 设置选择器类型                                                                                                                                                                                                                                                                  | `date` \| `week` \| `month` \| `quarter` \| `year`                                                                                                                                                                | `date`                                                                                                                             | `quarter`: 4.1.0    |
| placeholder           | 输入框提示文字                                                                                                                                                                                                                                                                  | string \| \[string, string]                                                                                                                                                                                       | -                                                                                                                                  |                     |
| placement             | 选择框弹出的位置                                                                                                                                                                                                                                                                | `bottomLeft` `bottomRight` `topLeft` `topRight`                                                                                                                                                                   | bottomLeft                                                                                                                         |                     |
| popupStyle            | 额外的弹出日历样式                                                                                                                                                                                                                                                              | CSSProperties                                                                                                                                                                                                     | {}                                                                                                                                 |                     |
| prevIcon              | 自定义上一个图标                                                                                                                                                                                                                                                                | ReactNode                                                                                                                                                                                                         | -                                                                                                                                  | 4.17.0              |
| presets               | 预设时间范围快捷选择, 自 `5.8.0` 起 value 支持函数返回值                                                                                                                                                                                                                        | { label: React.ReactNode, value: Dayjs \| (() => Dayjs) }\[]                                                                                                                                                      | -                                                                                                                                  |                     |
| size                  | 输入框大小，`large` 高度为 40px，`small` 为 24px，默认是 32px                                                                                                                                                                                                                   | `large` \| `middle` \| `small`                                                                                                                                                                                    | -                                                                                                                                  |                     |
| status                | 设置校验状态                                                                                                                                                                                                                                                                    | 'error' \| 'warning'                                                                                                                                                                                              | -                                                                                                                                  | 4.19.0              |
| style                 | 自定义输入框样式                                                                                                                                                                                                                                                                | CSSProperties                                                                                                                                                                                                     | {}                                                                                                                                 |                     |
| suffixIcon            | 自定义的选择框后缀图标                                                                                                                                                                                                                                                          | ReactNode                                                                                                                                                                                                         | -                                                                                                                                  |                     |
| superNextIcon         | 自定义 `>>` 切换图标                                                                                                                                                                                                                                                            | ReactNode                                                                                                                                                                                                         | -                                                                                                                                  | 4.17.0              |
| superPrevIcon         | 自定义 `<<` 切换图标                                                                                                                                                                                                                                                            | ReactNode                                                                                                                                                                                                         | -                                                                                                                                  | 4.17.0              |
| variant               | 形态变体                                                                                                                                                                                                                                                                        | `outlined` \| `borderless` \| `filled`                                                                                                                                                                            | `outlined`                                                                                                                         | 5.13.0              |
| onOpenChange          | 弹出日历和关闭日历的回调                                                                                                                                                                                                                                                        | function(open)                                                                                                                                                                                                    | -                                                                                                                                  |                     |
| onPanelChange         | 日历面板切换的回调                                                                                                                                                                                                                                                              | function(value, mode)                                                                                                                                                                                             | -                                                                                                                                  |                     |

### 共同的方法

| 名称    | 描述     | 版本 |
| ------- | -------- | ---- |
| blur()  | 移除焦点 |      |
| focus() | 获取焦点 |      |

### DatePicker

| 参数                  | 说明                                                                                                                                                                                        | 类型                                      | 默认值                                            | 版本   |
| --------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------- | ------------------------------------------------- | ------ |
| defaultPickerValue    | 默认面板日期，每次面板打开时会被重置到该日期                                                                                                                                                | [dayjs](https://day.js.org/)              | -                                                 | 5.14.0 |
| defaultValue          | 默认日期，如果开始时间或结束时间为 `null` 或者 `undefined`，日期范围将是一个开区间                                                                                                          | [dayjs](https://day.js.org/)              | -                                                 |        |
| disabledTime          | 不可选择的时间                                                                                                                                                                              | function(date)                            | -                                                 |        |
| format                | 展示的日期格式，配置参考 [dayjs#format](https://day.js.org/docs/zh-CN/display/format#%E6%94%AF%E6%8C%81%E7%9A%84%E6%A0%BC%E5%BC%8F%E5%8C%96%E5%8D%A0%E4%BD%8D%E7%AC%A6%E5%88%97%E8%A1%A8)。 | [formatType](#formattype)                 | `YYYY-MM-DD`                                      |        |
| multiple              | 是否为多选，不支持 `showTime`                                                                                                                                                               | boolean                                   | false                                             | 5.14.0 |
| pickerValue           | 面板日期，可以用于受控切换面板所在日期。配合 `onPanelChange` 使用。                                                                                                                         | [dayjs](https://day.js.org/)              | -                                                 | 5.14.0 |
| renderExtraFooter     | 在面板中添加额外的页脚                                                                                                                                                                      | (mode) => React.ReactNode                 | -                                                 |        |
| showNow               | 显示当前日期时间的快捷选择                                                                                                                                                                  | boolean                                   | -                                                 |        |
| showTime              | 增加时间选择功能                                                                                                                                                                            | Object \| boolean                         | [TimePicker Options](/components/time-picker#api) |        |
| showTime.defaultValue | 设置用户选择日期时默认的时分秒，[例子](#date-picker-demo-disabled-date)                                                                                                                     | [dayjs](https://day.js.org/)              | dayjs()                                           |        |
| showWeek              | DatePicker 下展示当前周                                                                                                                                                                     | boolean                                   | false                                             | 5.14.0 |
| value                 | 日期                                                                                                                                                                                        | [dayjs](https://day.js.org/)              | -                                                 |        |
| onChange              | 时间发生变化的回调                                                                                                                                                                          | function(date: dayjs, dateString: string) | -                                                 |        |
| onOk                  | 点击确定按钮的回调                                                                                                                                                                          | function()                                | -                                                 |        |
| onPanelChange         | 日期面板变化时的回调                                                                                                                                                                        | function(value, mode)                     | -                                                 |        |

### DatePicker\[picker=year]

| 参数              | 说明                                                                                                                                                                                        | 类型                                      | 默认值 | 版本   |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------- | ------ | ------ |
| defaultValue      | 默认日期                                                                                                                                                                                    | [dayjs](https://day.js.org/)              | -      |        |
| format            | 展示的日期格式，配置参考 [dayjs#format](https://day.js.org/docs/zh-CN/display/format#%E6%94%AF%E6%8C%81%E7%9A%84%E6%A0%BC%E5%BC%8F%E5%8C%96%E5%8D%A0%E4%BD%8D%E7%AC%A6%E5%88%97%E8%A1%A8)。 | [formatType](#formattype)                 | `YYYY` |        |
| multiple          | 是否为多选                                                                                                                                                                                  | boolean                                   | false  | 5.14.0 |
| renderExtraFooter | 在面板中添加额外的页脚                                                                                                                                                                      | () => React.ReactNode                     | -      |        |
| value             | 日期                                                                                                                                                                                        | [dayjs](https://day.js.org/)              | -      |        |
| onChange          | 时间发生变化的回调，发生在用户选择时间时                                                                                                                                                    | function(date: dayjs, dateString: string) | -      |        |

### DatePicker\[picker=quarter]

`4.1.0` 新增。

| 参数              | 说明                                                                                                                                                                                        | 类型                                      | 默认值     | 版本   |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------- | ---------- | ------ |
| defaultValue      | 默认日期                                                                                                                                                                                    | [dayjs](https://day.js.org/)              | -          |        |
| format            | 展示的日期格式，配置参考 [dayjs#format](https://day.js.org/docs/zh-CN/display/format#%E6%94%AF%E6%8C%81%E7%9A%84%E6%A0%BC%E5%BC%8F%E5%8C%96%E5%8D%A0%E4%BD%8D%E7%AC%A6%E5%88%97%E8%A1%A8)。 | [formatType](#formattype)                 | `YYYY-\QQ` |        |
| multiple          | 是否为多选                                                                                                                                                                                  | boolean                                   | false      | 5.14.0 |
| renderExtraFooter | 在面板中添加额外的页脚                                                                                                                                                                      | () => React.ReactNode                     | -          |        |
| value             | 日期                                                                                                                                                                                        | [dayjs](https://day.js.org/)              | -          |        |
| onChange          | 时间发生变化的回调，发生在用户选择时间时                                                                                                                                                    | function(date: dayjs, dateString: string) | -          |        |

### DatePicker\[picker=month]

| 参数              | 说明                                                                                                                                                                                        | 类型                                      | 默认值    | 版本   |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------- | --------- | ------ |
| defaultValue      | 默认日期                                                                                                                                                                                    | [dayjs](https://day.js.org/)              | -         |        |
| format            | 展示的日期格式，配置参考 [dayjs#format](https://day.js.org/docs/zh-CN/display/format#%E6%94%AF%E6%8C%81%E7%9A%84%E6%A0%BC%E5%BC%8F%E5%8C%96%E5%8D%A0%E4%BD%8D%E7%AC%A6%E5%88%97%E8%A1%A8)。 | [formatType](#formattype)                 | `YYYY-MM` |        |
| multiple          | 是否为多选                                                                                                                                                                                  | boolean                                   | false     | 5.14.0 |
| renderExtraFooter | 在面板中添加额外的页脚                                                                                                                                                                      | () => React.ReactNode                     | -         |        |
| value             | 日期                                                                                                                                                                                        | [dayjs](https://day.js.org/)              | -         |        |
| onChange          | 时间发生变化的回调，发生在用户选择时间时                                                                                                                                                    | function(date: dayjs, dateString: string) | -         |        |

### DatePicker\[picker=week]

| 参数              | 说明                                                                                                                                                                                        | 类型                                      | 默认值    | 版本   |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------- | --------- | ------ |
| defaultValue      | 默认日期                                                                                                                                                                                    | [dayjs](https://day.js.org/)              | -         |        |
| format            | 展示的日期格式，配置参考 [dayjs#format](https://day.js.org/docs/zh-CN/display/format#%E6%94%AF%E6%8C%81%E7%9A%84%E6%A0%BC%E5%BC%8F%E5%8C%96%E5%8D%A0%E4%BD%8D%E7%AC%A6%E5%88%97%E8%A1%A8)。 | [formatType](#formattype)                 | `YYYY-wo` |        |
| multiple          | 是否为多选                                                                                                                                                                                  | boolean                                   | false     | 5.14.0 |
| renderExtraFooter | 在面板中添加额外的页脚                                                                                                                                                                      | (mode) => React.ReactNode                 | -         |        |
| value             | 日期                                                                                                                                                                                        | [dayjs](https://day.js.org/)              | -         |        |
| onChange          | 时间发生变化的回调，发生在用户选择时间时                                                                                                                                                    | function(date: dayjs, dateString: string) | -         |        |

### RangePicker

| 参数                  | 说明                                                                                                                                                                                        | 类型                                                                                                                                                                                                              | 默认值                                            | 版本                |
| --------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------- | ------------------- |
| allowEmpty            | 允许起始项部分为空                                                                                                                                                                          | \[boolean, boolean]                                                                                                                                                                                               | \[false, false]                                   |                     |
| cellRender            | 自定义单元格的内容。                                                                                                                                                                        | (current: dayjs, info: { originNode: React.ReactElement,today: DateType, range?: 'start' \| 'end', type: PanelMode, locale?: Locale, subType?: 'hour' \| 'minute' \| 'second' \| 'meridiem' }) => React.ReactNode | -                                                 | 5.4.0               |
| dateRender            | 自定义日期单元格的内容，5.4.0 起用 `cellRender` 代替                                                                                                                                        | function(currentDate: dayjs, today: dayjs) => React.ReactNode                                                                                                                                                     | -                                                 | < 5.4.0             |
| defaultPickerValue    | 默认面板日期，每次面板打开时会被重置到该日期                                                                                                                                                | [dayjs](https://day.js.org/)[]                                                                                                                                                                                    | -                                                 | 5.14.0              |
| defaultValue          | 默认日期                                                                                                                                                                                    | [dayjs](https://day.js.org/)\[]                                                                                                                                                                                   | -                                                 |                     |
| disabled              | 禁用起始项                                                                                                                                                                                  | \[boolean, boolean]                                                                                                                                                                                               | -                                                 |                     |
| disabledTime          | 不可选择的时间                                                                                                                                                                              | function(date: dayjs, partial: `start` \| `end`, info: { from?: dayjs })                                                                                                                                          | -                                                 | `info.from`: 5.17.0 |
| format                | 展示的日期格式，配置参考 [dayjs#format](https://day.js.org/docs/zh-CN/display/format#%E6%94%AF%E6%8C%81%E7%9A%84%E6%A0%BC%E5%BC%8F%E5%8C%96%E5%8D%A0%E4%BD%8D%E7%AC%A6%E5%88%97%E8%A1%A8)。 | [formatType](#formattype)                                                                                                                                                                                         | `YYYY-MM-DD HH:mm:ss`                             |                     |
| id                    | 设置输入框 `id` 属性。                                                                                                                                                                      | { start?: string, end?: string }                                                                                                                                                                                  | -                                                 | 5.14.0              |
| pickerValue           | 面板日期，可以用于受控切换面板所在日期。配合 `onPanelChange` 使用。                                                                                                                         | [dayjs](https://day.js.org/)[]                                                                                                                                                                                    | -                                                 | 5.14.0              |
| presets               | 预设时间范围快捷选择，自 `5.8.0` 起 value 支持函数返回值                                                                                                                                    | { label: React.ReactNode, value: (Dayjs \| (() => Dayjs))\[] }\[]                                                                                                                                                 | -                                                 |                     |
| renderExtraFooter     | 在面板中添加额外的页脚                                                                                                                                                                      | () => React.ReactNode                                                                                                                                                                                             | -                                                 |                     |
| separator             | 设置分隔符                                                                                                                                                                                  | React.ReactNode                                                                                                                                                                                                   | `<SwapRightOutlined />`                           |                     |
| showTime              | 增加时间选择功能                                                                                                                                                                            | Object\|boolean                                                                                                                                                                                                   | [TimePicker Options](/components/time-picker#api) |                     |
| showTime.defaultValue | 设置用户选择日期时默认的时分秒，[例子](#date-picker-demo-disabled-date)                                                                                                                     | [dayjs](https://day.js.org/)\[]                                                                                                                                                                                   | \[dayjs(), dayjs()]                               |                     |
| value                 | 日期                                                                                                                                                                                        | [dayjs](https://day.js.org/)\[]                                                                                                                                                                                   | -                                                 |                     |
| onCalendarChange      | 待选日期发生变化的回调。`info` 参数自 4.4.0 添加                                                                                                                                            | function(dates: \[dayjs, dayjs], dateStrings: \[string, string], info: { range:`start`\|`end` })                                                                                                                  | -                                                 |                     |
| onChange              | 日期范围发生变化的回调                                                                                                                                                                      | function(dates: \[dayjs, dayjs], dateStrings: \[string, string])                                                                                                                                                  | -                                                 |                     |
| onFocus               | 聚焦时回调                                                                                                                                                                                  | function(event, { range: 'start' \| 'end' })                                                                                                                                                                      | -                                                 | `range`: 5.14.0     |
| onBlur                | 失焦时回调                                                                                                                                                                                  | function(event, { range: 'start' \| 'end' })                                                                                                                                                                      | -                                                 | `range`: 5.14.0     |

#### formatType

```typescript
import type { Dayjs } from 'dayjs';

type Generic = string;
type GenericFn = (value: Dayjs) => string;

export type FormatType =
  | Generic
  | GenericFn
  | Array<Generic | GenericFn>
  | {
      format: string;
      type?: 'mask';
    };
```

注意：`type` 定义为 `5.14.0` 新增。

## 示例

### Basic

```tsx
import React from 'react';
import type { DatePickerProps } from '@otakus/design';
import { DatePicker, Space } from '@otakus/design';

const onChange: DatePickerProps['onChange'] = (date, dateString) => {
  console.log(date, dateString);
};

const App: React.FC = () => (
  <Space direction="vertical">
    <DatePicker onChange={onChange} />
    <DatePicker onChange={onChange} picker="week" />
    <DatePicker onChange={onChange} picker="month" />
    <DatePicker onChange={onChange} picker="quarter" />
    <DatePicker onChange={onChange} picker="year" />
  </Space>
);

export default App;

```

### Range Picker

```tsx
import React from 'react';
import { DatePicker, Space } from '@otakus/design';

const { RangePicker } = DatePicker;

const App: React.FC = () => (
  <Space direction="vertical" size={12}>
    <RangePicker />
    <RangePicker showTime />
    <RangePicker picker="week" />
    <RangePicker picker="month" />
    <RangePicker picker="quarter" />
    <RangePicker
      picker="year"
      id={{
        start: 'startInput',
        end: 'endInput'
      }}
      onFocus={(_, info) => {
        console.log('Focus:', info.range);
      }}
      onBlur={(_, info) => {
        console.log('Blur:', info.range);
      }}
    />
  </Space>
);

export default App;

```

### Multiple

```tsx
import React from 'react';
import type { DatePickerProps } from '@otakus/design';
import { DatePicker, Flex } from '@otakus/design';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';

const onChange: DatePickerProps<Dayjs[]>['onChange'] = (date, dateString) => {
  console.log(date, dateString);
};

const defaultValue = [dayjs('2000-01-01'), dayjs('2000-01-03'), dayjs('2000-01-05')];

const App: React.FC = () => (
  <Flex vertical gap="small">
    <DatePicker
      multiple
      onChange={onChange}
      maxTagCount="responsive"
      defaultValue={defaultValue}
      size="small"
    />
    <DatePicker multiple onChange={onChange} maxTagCount="responsive" defaultValue={defaultValue} />
    <DatePicker
      multiple
      onChange={onChange}
      maxTagCount="responsive"
      defaultValue={defaultValue}
      size="large"
    />
  </Flex>
);

export default App;

```

### Need Confirm

```tsx
import React from 'react';
import type { DatePickerProps } from '@otakus/design';
import { DatePicker, Space } from '@otakus/design';
import type { Dayjs } from 'dayjs';

const onChange: DatePickerProps<Dayjs[]>['onChange'] = (date, dateString) => {
  console.log(date, dateString);
};

const App: React.FC = () => (
  <Space direction="vertical" size={12}>
    <DatePicker onChange={onChange} needConfirm />
    <DatePicker onChange={onChange} showTime needConfirm={false} />
  </Space>
);

export default App;

```

### Switchable picker

```tsx
import React, { useState } from 'react';
import type { DatePickerProps, TimePickerProps } from '@otakus/design';
import { DatePicker, Select, Space, TimePicker } from '@otakus/design';

const { Option } = Select;

type PickerType = 'time' | 'date';

const PickerWithType = ({
  type,
  onChange
}: {
  type: PickerType;
  onChange: TimePickerProps['onChange'] | DatePickerProps['onChange'];
}) => {
  if (type === 'time') return <TimePicker onChange={onChange} />;
  if (type === 'date') return <DatePicker onChange={onChange} />;
  return <DatePicker picker={type} onChange={onChange} />;
};

const App: React.FC = () => {
  const [type, setType] = useState<PickerType>('time');

  return (
    <Space>
      <Select value={type} onChange={setType}>
        <Option value="time">Time</Option>
        <Option value="date">Date</Option>
        <Option value="week">Week</Option>
        <Option value="month">Month</Option>
        <Option value="quarter">Quarter</Option>
        <Option value="year">Year</Option>
      </Select>
      <PickerWithType type={type} onChange={(value) => console.log(value)} />
    </Space>
  );
};

export default App;

```

### Date Format

```tsx
import React from 'react';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import type { DatePickerProps } from '@otakus/design';
import { DatePicker, Space } from '@otakus/design';

dayjs.extend(customParseFormat);

const { RangePicker } = DatePicker;

const dateFormat = 'YYYY/MM/DD';
const weekFormat = 'MM/DD';
const monthFormat = 'YYYY/MM';

/** Manually entering any of the following formats will perform date parsing */
const dateFormatList = ['DD/MM/YYYY', 'DD/MM/YY', 'DD-MM-YYYY', 'DD-MM-YY'];

const customFormat: DatePickerProps['format'] = (value) =>
  `custom format: ${value.format(dateFormat)}`;

const customWeekStartEndFormat: DatePickerProps['format'] = (value) =>
  `${dayjs(value).startOf('week').format(weekFormat)} ~ ${dayjs(value)
    .endOf('week')
    .format(weekFormat)}`;

const App: React.FC = () => (
  <Space direction="vertical" size={12}>
    <DatePicker defaultValue={dayjs('2015/01/01', dateFormat)} format={dateFormat} />
    <DatePicker defaultValue={dayjs('01/01/2015', dateFormatList[0])} format={dateFormatList} />
    <DatePicker defaultValue={dayjs('2015/01', monthFormat)} format={monthFormat} picker="month" />
    <DatePicker defaultValue={dayjs()} format={customWeekStartEndFormat} picker="week" />
    <RangePicker
      defaultValue={[dayjs('2015/01/01', dateFormat), dayjs('2015/01/01', dateFormat)]}
      format={dateFormat}
    />
    <DatePicker defaultValue={dayjs('2015/01/01', dateFormat)} format={customFormat} />
  </Space>
);

export default App;

```

### Choose Time

```tsx
import React from 'react';
import { DatePicker, Space } from '@otakus/design';
import type { DatePickerProps, GetProps } from '@otakus/design';

type RangePickerProps = GetProps<typeof DatePicker.RangePicker>;

const { RangePicker } = DatePicker;

const onChange = (
  value: DatePickerProps['value'] | RangePickerProps['value'],
  dateString: [string, string] | string
) => {
  console.log('Selected Time: ', value);
  console.log('Formatted Selected Time: ', dateString);
};

const onOk = (value: DatePickerProps['value'] | RangePickerProps['value']) => {
  console.log('onOk: ', value);
};

const App: React.FC = () => (
  <Space direction="vertical" size={12}>
    <DatePicker showTime onChange={onChange} onOk={onOk} />
    <RangePicker
      showTime={{ format: 'HH:mm' }}
      format="YYYY-MM-DD HH:mm"
      onChange={onChange}
      onOk={onOk}
    />
  </Space>
);

export default App;

```

### Mask Format

```tsx
import React from 'react';
import type { DatePickerProps } from '@otakus/design';
import { DatePicker, Space } from '@otakus/design';

const onChange: DatePickerProps['onChange'] = (date, dateString) => {
  console.log(date, dateString);
};

const App: React.FC = () => (
  <Space direction="vertical">
    <DatePicker
      format={{
        format: 'YYYY-MM-DD',
        type: 'mask'
      }}
      onChange={onChange}
    />
    <DatePicker
      format={{
        format: 'YYYY-MM-DD HH:mm:ss',
        type: 'mask'
      }}
      onChange={onChange}
    />
  </Space>
);

export default App;

```

### Limit Date Range

```tsx
import React from 'react';
import { DatePicker } from '@otakus/design';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(customParseFormat);

const dateFormat = 'YYYY-MM-DD';

const App: React.FC = () => (
  <DatePicker
    defaultValue={dayjs('2019-09-03', dateFormat)}
    minDate={dayjs('2019-08-01', dateFormat)}
    maxDate={dayjs('2020-10-31', dateFormat)}
  />
);

export default App;

```

### Disabled

```tsx
import React from 'react';
import { DatePicker, Space } from '@otakus/design';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(customParseFormat);

const { RangePicker } = DatePicker;

const dateFormat = 'YYYY-MM-DD';

const App: React.FC = () => (
  <Space direction="vertical" size={12}>
    <DatePicker defaultValue={dayjs('2015-06-06', dateFormat)} disabled />
    <DatePicker picker="month" defaultValue={dayjs('2015-06', 'YYYY-MM')} disabled />
    <RangePicker
      defaultValue={[dayjs('2015-06-06', dateFormat), dayjs('2015-06-06', dateFormat)]}
      disabled
    />
    <RangePicker
      defaultValue={[dayjs('2019-09-03', dateFormat), dayjs('2019-11-22', dateFormat)]}
      disabled={[false, true]}
    />
    <DatePicker
      defaultValue={dayjs('2019-09-03', dateFormat)}
      minDate={dayjs('2019-06-01', dateFormat)}
      maxDate={dayjs('2020-06-30', dateFormat)}
    />
  </Space>
);

export default App;

```

### Disabled Date & Time

```tsx
import React from 'react';
import { DatePicker, Space } from '@otakus/design';
import type { GetProps } from '@otakus/design';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

type RangePickerProps = GetProps<typeof DatePicker.RangePicker>;

dayjs.extend(customParseFormat);

const { RangePicker } = DatePicker;

const range = (start: number, end: number) => {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
};

// eslint-disable-next-line arrow-body-style
const disabledDate: RangePickerProps['disabledDate'] = (current) => {
  // Can not select days before today and today
  return current && current < dayjs().endOf('day');
};

const disabledDateTime = () => ({
  disabledHours: () => range(0, 24).splice(4, 20),
  disabledMinutes: () => range(30, 60),
  disabledSeconds: () => [55, 56]
});

const disabledRangeTime: RangePickerProps['disabledTime'] = (_, type) => {
  if (type === 'start') {
    return {
      disabledHours: () => range(0, 60).splice(4, 20),
      disabledMinutes: () => range(30, 60),
      disabledSeconds: () => [55, 56]
    };
  }
  return {
    disabledHours: () => range(0, 60).splice(20, 4),
    disabledMinutes: () => range(0, 31),
    disabledSeconds: () => [55, 56]
  };
};

const App: React.FC = () => (
  <Space direction="vertical" size={12}>
    <DatePicker
      format="YYYY-MM-DD HH:mm:ss"
      disabledDate={disabledDate}
      disabledTime={disabledDateTime}
      showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
    />
    <DatePicker picker="month" disabledDate={disabledDate} />
    <RangePicker disabledDate={disabledDate} />
    <RangePicker
      disabledDate={disabledDate}
      disabledTime={disabledRangeTime}
      showTime={{
        hideDisabledOptions: true,
        defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('11:59:59', 'HH:mm:ss')]
      }}
      format="YYYY-MM-DD HH:mm:ss"
    />
  </Space>
);

export default App;

```

### Allow Empty

```tsx
import React from 'react';
import { DatePicker } from '@otakus/design';

const App: React.FC = () => (
  <DatePicker.RangePicker
    placeholder={['', 'Till Now']}
    allowEmpty={[false, true]}
    onChange={(date, dateString) => {
      console.log(date, dateString);
    }}
  />
);

export default App;

```

### Select range dates in 7 days

```tsx
import React, { useState } from 'react';
import { DatePicker } from '@otakus/design';
import type { DatePickerProps } from '@otakus/design';
import type { Dayjs } from 'dayjs';

const { RangePicker } = DatePicker;

type RangeValue = [Dayjs | null, Dayjs | null] | null;

const App: React.FC = () => {
  const [value, setValue] = useState<RangeValue>(null);

  const disabledDate: DatePickerProps['disabledDate'] = (current, { from }) => {
    if (from) {
      return Math.abs(current.diff(from, 'days')) >= 7;
    }

    return false;
  };

  return <RangePicker value={value} disabledDate={disabledDate} onChange={setValue} />;
};

export default App;

```

### Preset Ranges

```tsx
import React from 'react';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import type { TimeRangePickerProps } from '@otakus/design';
import { DatePicker, Space } from '@otakus/design';

const { RangePicker } = DatePicker;

const onChange = (date: Dayjs) => {
  if (date) {
    console.log('Date: ', date);
  } else {
    console.log('Clear');
  }
};
const onRangeChange = (dates: null | (Dayjs | null)[], dateStrings: string[]) => {
  if (dates) {
    console.log('From: ', dates[0], ', to: ', dates[1]);
    console.log('From: ', dateStrings[0], ', to: ', dateStrings[1]);
  } else {
    console.log('Clear');
  }
};

const rangePresets: TimeRangePickerProps['presets'] = [
  { label: 'Last 7 Days', value: [dayjs().add(-7, 'd'), dayjs()] },
  { label: 'Last 14 Days', value: [dayjs().add(-14, 'd'), dayjs()] },
  { label: 'Last 30 Days', value: [dayjs().add(-30, 'd'), dayjs()] },
  { label: 'Last 90 Days', value: [dayjs().add(-90, 'd'), dayjs()] }
];

const App: React.FC = () => (
  <Space direction="vertical" size={12}>
    <DatePicker
      presets={[
        { label: 'Yesterday', value: dayjs().add(-1, 'd') },
        { label: 'Last Week', value: dayjs().add(-7, 'd') },
        { label: 'Last Month', value: dayjs().add(-1, 'month') }
      ]}
      onChange={onChange}
    />
    <RangePicker presets={rangePresets} onChange={onRangeChange} />
    <RangePicker
      presets={[
        {
          label: <span aria-label="Current Time to End of Day">Now ~ EOD</span>,
          value: () => [dayjs(), dayjs().endOf('day')] // 5.8.0+ support function
        },
        ...rangePresets
      ]}
      showTime
      format="YYYY/MM/DD HH:mm:ss"
      onChange={onRangeChange}
    />
  </Space>
);

export default App;

```

### Extra Footer

```tsx
import React from 'react';
import { DatePicker, Space } from '@otakus/design';

const { RangePicker } = DatePicker;

const App: React.FC = () => (
  <Space direction="vertical" size={12}>
    <DatePicker renderExtraFooter={() => 'extra footer'} />
    <DatePicker renderExtraFooter={() => 'extra footer'} showTime />
    <RangePicker renderExtraFooter={() => 'extra footer'} />
    <RangePicker renderExtraFooter={() => 'extra footer'} showTime />
    <DatePicker renderExtraFooter={() => 'extra footer'} picker="month" />
  </Space>
);

export default App;

```

### Three Sizes

```tsx
import React, { useState } from 'react';
import type { ConfigProviderProps, RadioChangeEvent } from '@otakus/design';
import { DatePicker, Radio, Space } from '@otakus/design';

type SizeType = ConfigProviderProps['componentSize'];

const { RangePicker } = DatePicker;

const App: React.FC = () => {
  const [size, setSize] = useState<SizeType>('middle');

  const handleSizeChange = (e: RadioChangeEvent) => {
    setSize(e.target.value);
  };

  return (
    <Space direction="vertical" size={12}>
      <Radio.Group value={size} onChange={handleSizeChange}>
        <Radio.Button value="large">Large</Radio.Button>
        <Radio.Button value="middle">middle</Radio.Button>
        <Radio.Button value="small">Small</Radio.Button>
      </Radio.Group>
      <DatePicker size={size} />
      <DatePicker size={size} picker="month" />
      <RangePicker size={size} />
      <DatePicker size={size} picker="week" />
    </Space>
  );
};

export default App;

```

### Customized Cell Rendering

```tsx
import React from 'react';
import { DatePicker, Space, theme } from '@otakus/design';
import type { Dayjs } from 'dayjs';

const App: React.FC = () => {
  const { token } = theme.useToken();
  const style: React.CSSProperties = {
    border: `1px solid ${token.colorPrimary}`,
    borderRadius: '50%'
  };
  const cellRender = React.useCallback((current: number | Dayjs, info) => {
    if (info.type !== 'date') {
      return info.originNode;
    }
    if (typeof current === 'number') {
      return <div className="otakus-picker-cell-inner">{current}</div>;
    }
    return (
      <div className="otakus-picker-cell-inner" style={current.date() === 1 ? style : {}}>
        {current.date()}
      </div>
    );
  }, []);
  return (
    <Space size={12} direction="vertical">
      <DatePicker cellRender={cellRender} />
      <DatePicker.RangePicker cellRender={cellRender} />
    </Space>
  );
};

export default App;

```

### Customize Panel

```tsx
import React from 'react';
import type { DatePickerProps } from '@otakus/design';
import { Button, DatePicker, Flex, Slider, Space, Typography } from '@otakus/design';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';

const onChange: DatePickerProps['onChange'] = (date, dateString) => {
  console.log(date, dateString);
};

type DateComponent = Required<NonNullable<DatePickerProps<Dayjs>['components']>>['date'];
type GetProps<T> = T extends React.ComponentType<infer P> ? P : never;

const MyDatePanel = (props: GetProps<DateComponent>) => {
  const { value, onSelect, onHover } = props;

  // Value
  const startDate = React.useMemo(() => dayjs().date(1).month(0), []);
  const [innerValue, setInnerValue] = React.useState(value || startDate);

  React.useEffect(() => {
    if (value) {
      setInnerValue(value);
    }
  }, [value]);

  // Range
  const dateCount = React.useMemo(() => {
    const endDate = startDate.add(1, 'year').add(-1, 'day');
    return endDate.diff(startDate, 'day');
  }, [startDate]);

  const sliderValue = Math.min(Math.max(0, innerValue.diff(startDate, 'day')), dateCount);

  // Render
  return (
    <Flex vertical gap="small" style={{ padding: 16 }}>
      <Typography.Title level={4} style={{ margin: 0 }} title="no, it's not">
        The BEST Picker Panel
      </Typography.Title>
      <Slider
        min={0}
        max={dateCount}
        value={sliderValue}
        onChange={(nextValue) => {
          const nextDate = startDate.add(nextValue, 'day');
          setInnerValue(nextDate);
          onHover?.(nextDate);
        }}
        tooltip={{
          formatter: (nextValue) => startDate.add(nextValue || 0, 'day').format('YYYY-MM-DD')
        }}
      />
      <Button
        type="primary"
        onClick={() => {
          onSelect(innerValue);
        }}
      >{`That's It!`}</Button>
    </Flex>
  );
};

const App: React.FC = () => (
  <Space direction="vertical">
    <DatePicker
      showNow={false}
      onChange={onChange}
      components={{
        date: MyDatePanel
      }}
    />
  </Space>
);

export default App;

```

### Buddhist Era

```tsx
import React from 'react';
import { ConfigProvider, DatePicker, Space, Typography } from '@otakus/design';
import type { DatePickerProps } from '@otakus/design';
import en from 'antd/es/date-picker/locale/en_US';
import enUS from 'antd/es/locale/en_US';
import dayjs from 'dayjs';
import buddhistEra from 'dayjs/plugin/buddhistEra';

dayjs.extend(buddhistEra);

const { Title } = Typography;

// Component level locale
const buddhistLocale: typeof en = {
  ...en,
  lang: {
    ...en.lang,
    fieldDateFormat: 'BBBB-MM-DD',
    fieldDateTimeFormat: 'BBBB-MM-DD HH:mm:ss',
    yearFormat: 'BBBB',
    cellYearFormat: 'BBBB'
  }
};

// ConfigProvider level locale
const globalBuddhistLocale: typeof enUS = {
  ...enUS,
  DatePicker: {
    ...enUS.DatePicker!,
    lang: buddhistLocale.lang
  }
};

const defaultValue = dayjs('2024-01-01');

const App: React.FC = () => {
  const onChange: DatePickerProps['onChange'] = (_, dateStr) => {
    console.log('onChange:', dateStr);
  };

  return (
    <Space direction="vertical">
      <Title level={4}>By locale props</Title>
      <DatePicker defaultValue={defaultValue} locale={buddhistLocale} onChange={onChange} />
      <DatePicker
        defaultValue={defaultValue}
        showTime
        locale={buddhistLocale}
        onChange={onChange}
      />

      <Title level={4}>By ConfigProvider</Title>
      <ConfigProvider locale={globalBuddhistLocale}>
        <Space direction="vertical">
          <DatePicker defaultValue={defaultValue} onChange={onChange} />
          <DatePicker defaultValue={defaultValue} showTime onChange={onChange} />
        </Space>
      </ConfigProvider>
    </Space>
  );
};

export default App;

```

### Status

```tsx
import React from 'react';
import { DatePicker, Space } from '@otakus/design';

const App: React.FC = () => (
  <Space direction="vertical" style={{ width: '100%' }}>
    <DatePicker status="error" style={{ width: '100%' }} />
    <DatePicker status="warning" style={{ width: '100%' }} />
    <DatePicker.RangePicker status="error" style={{ width: '100%' }} />
    <DatePicker.RangePicker status="warning" style={{ width: '100%' }} />
  </Space>
);

export default App;

```

### Variants

```tsx
import React from 'react';
import { DatePicker, Flex } from '@otakus/design';

const { RangePicker } = DatePicker;

const App: React.FC = () => (
  <Flex vertical gap={12}>
    <Flex gap={8}>
      <DatePicker placeholder="Outlined" />
      <RangePicker placeholder={['Outlined', '']} />
    </Flex>
    <Flex gap={8}>
      <DatePicker placeholder="Filled" variant="filled" />
      <RangePicker placeholder={['Filled', '']} variant="filled" />
    </Flex>
    <Flex gap={8}>
      <DatePicker placeholder="Borderless" variant="borderless" />
      <RangePicker placeholder={['Borderless', '']} variant="borderless" />
    </Flex>
  </Flex>
);

export default App;

```

### Filled Debug

```tsx
import React from 'react';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { DatePicker, Space } from '@otakus/design';

dayjs.extend(customParseFormat);

const { RangePicker } = DatePicker;

const dateFormat = 'YYYY-MM-DD';

const App: React.FC = () => (
  <Space direction="vertical" size={12}>
    <DatePicker variant="filled" defaultValue={dayjs('2015-06-06', dateFormat)} disabled />
    <DatePicker
      variant="filled"
      picker="month"
      defaultValue={dayjs('2015-06', 'YYYY-MM')}
      disabled
    />
    <RangePicker
      variant="filled"
      defaultValue={[dayjs('2015-06-06', dateFormat), dayjs('2015-06-06', dateFormat)]}
      disabled
    />
    <RangePicker
      variant="filled"
      defaultValue={[dayjs('2019-09-03', dateFormat), dayjs('2019-11-22', dateFormat)]}
      disabled={[false, true]}
    />
    <DatePicker
      defaultValue={dayjs('2023-12-25')}
      variant="filled"
      status="error"
      style={{ width: '100%' }}
    />
    <DatePicker variant="filled" status="warning" style={{ width: '100%' }} />
    <RangePicker variant="filled" status="error" style={{ width: '100%' }} />
    <RangePicker variant="filled" status="warning" style={{ width: '100%' }} />
  </Space>
);

export default App;

```

### Placement

```tsx
import React, { useState } from 'react';
import type { DatePickerProps, RadioChangeEvent } from '@otakus/design';
import { DatePicker, Radio } from '@otakus/design';

const { RangePicker } = DatePicker;

const App: React.FC = () => {
  const [placement, SetPlacement] = useState<DatePickerProps['placement']>('topLeft');

  const placementChange = (e: RadioChangeEvent) => {
    SetPlacement(e.target.value);
  };

  return (
    <>
      <Radio.Group value={placement} onChange={placementChange}>
        <Radio.Button value="topLeft">topLeft</Radio.Button>
        <Radio.Button value="topRight">topRight</Radio.Button>
        <Radio.Button value="bottomLeft">bottomLeft</Radio.Button>
        <Radio.Button value="bottomRight">bottomRight</Radio.Button>
      </Radio.Group>
      <br />
      <br />
      <DatePicker placement={placement} />
      <br />
      <br />
      <RangePicker placement={placement} />
    </>
  );
};

export default App;

```

### Controlled Panels

```tsx
import React, { useState } from 'react';
import { DatePicker, Space } from '@otakus/design';
import type { DatePickerProps, GetProps } from '@otakus/design';
import type { Dayjs } from 'dayjs';

type RangePickerProps = GetProps<typeof DatePicker.RangePicker>;

const { RangePicker } = DatePicker;

type RangeValue = [Dayjs | null | undefined, Dayjs | null | undefined] | null;

const ControlledDatePicker = () => {
  const [mode, setMode] = useState<DatePickerProps['mode']>('time');

  const handleOpenChange = (open: boolean) => {
    if (open) {
      setMode('time');
    }
  };

  const handlePanelChange: DatePickerProps['onPanelChange'] = (_, newMode) => {
    setMode(newMode);
  };

  return (
    <DatePicker
      mode={mode}
      showTime
      onOpenChange={handleOpenChange}
      onPanelChange={handlePanelChange}
    />
  );
};

const ControlledRangePicker = () => {
  const [mode, setMode] = useState<RangePickerProps['mode']>(['month', 'month']);
  const [value, setValue] = useState<RangeValue>([null, null]);

  const handlePanelChange: RangePickerProps['onPanelChange'] = (newValue, newModes) => {
    setValue(newValue);
    setMode([
      newModes[0] === 'date' ? 'month' : newModes[0],
      newModes[1] === 'date' ? 'month' : newModes[1]
    ]);
  };

  return (
    <RangePicker
      placeholder={['Start month', 'End month']}
      format="YYYY-MM"
      value={value}
      mode={mode}
      onChange={setValue}
      onPanelChange={handlePanelChange}
    />
  );
};

const App: React.FC = () => (
  <Space direction="vertical" size={12}>
    <ControlledDatePicker />
    <ControlledRangePicker />
  </Space>
);

export default App;

```

### Customized Range Picker

```tsx
import React, { useState } from 'react';
import type { Dayjs } from 'dayjs';
import { DatePicker, Space } from '@otakus/design';

const App: React.FC = () => {
  const [startValue, setStartValue] = useState<Dayjs | null>(null);
  const [endValue, setEndValue] = useState<Dayjs | null>(null);
  const [endOpen, setEndOpen] = useState(false);

  const disabledStartDate = (startDate: Dayjs) => {
    if (!startDate || !endValue) {
      return false;
    }
    return startDate.valueOf() > endValue.valueOf();
  };

  const disabledEndDate = (endDate: Dayjs) => {
    if (!endDate || !startValue) {
      return false;
    }
    return endDate.valueOf() <= startValue.valueOf();
  };

  const handleStartOpenChange = (open: boolean) => {
    if (!open) {
      setEndOpen(true);
    }
  };

  const handleEndOpenChange = (open: boolean) => {
    setEndOpen(open);
  };

  return (
    <Space>
      <DatePicker
        disabledDate={disabledStartDate}
        showTime
        format="YYYY-MM-DD HH:mm:ss"
        value={startValue}
        placeholder="Start"
        onChange={setStartValue}
        onOpenChange={handleStartOpenChange}
      />
      <DatePicker
        disabledDate={disabledEndDate}
        showTime
        format="YYYY-MM-DD HH:mm:ss"
        value={endValue}
        placeholder="End"
        onChange={setEndValue}
        open={endOpen}
        onOpenChange={handleEndOpenChange}
      />
    </Space>
  );
};

export default App;

```

### Suffix

```tsx
import React from 'react';
import { SettingOutlined } from '@otakus/icons';
import { DatePicker, Space } from '@otakus/design';
import type { Dayjs } from 'dayjs';

const icon = <SettingOutlined />;
const { RangePicker } = DatePicker;

const onChange = (date: Dayjs | (Dayjs | null)[] | null, dateString: string | string[]) => {
  console.log(date, dateString);
};

const App: React.FC = () => (
  <Space direction="vertical" size={12}>
    <DatePicker suffixIcon={icon} onChange={onChange} />
    <DatePicker suffixIcon={icon} onChange={onChange} picker="month" />
    <RangePicker suffixIcon={icon} onChange={onChange} />
    <DatePicker suffixIcon={icon} onChange={onChange} picker="week" />
    <DatePicker suffixIcon="ab" onChange={onChange} />
    <DatePicker suffixIcon="ab" onChange={onChange} picker="month" />
    <RangePicker suffixIcon="ab" onChange={onChange} />
    <DatePicker suffixIcon="ab" onChange={onChange} picker="week" />
  </Space>
);

export default App;

```

### \_InternalPanelDoNotUseOrYouWillBeFired

```tsx
import React from 'react';
import { DatePicker } from '@otakus/design';

const { _InternalPanelDoNotUseOrYouWillBeFired: InternalDatePicker } = DatePicker;

const App: React.FC = () => <InternalDatePicker />;

export default App;

```

### Component Token

```tsx
import React from 'react';
import type { DatePickerProps } from '@otakus/design';
import { ConfigProvider, DatePicker, Space, TimePicker } from '@otakus/design';
import dayjs from 'dayjs';

/** Test usage. Do not use in your production. */

const { RangePicker } = DatePicker;

const onChange: DatePickerProps['onChange'] = (date, dateString) => {
  console.log(date, dateString);
};

const App: React.FC = () => (
  <ConfigProvider
    theme={{
      components: {
        DatePicker: {
          presetsWidth: 160,
          zIndexPopup: 888,
          cellHoverWithRangeBg: '#f0f0f0',
          cellActiveWithRangeBg: '#e6bbff',
          cellRangeBorderColor: 'green',
          timeColumnWidth: 80,
          timeColumnHeight: 250,
          timeCellHeight: 30,
          cellWidth: 64,
          cellHeight: 40,
          textHeight: 45,
          withoutTimeCellHeight: 70
        }
      }
    }}
  >
    <Space direction="vertical">
      <DatePicker
        presets={[
          { label: 'Yesterday', value: dayjs().add(-1, 'd') },
          { label: 'Last Week', value: dayjs().add(-7, 'd') },
          { label: 'Last Month', value: dayjs().add(-1, 'month') }
        ]}
        onChange={onChange}
      />
      <RangePicker />
      <TimePicker onChange={onChange} />
      <DatePicker onChange={onChange} picker="month" />
    </Space>
  </ConfigProvider>
);

export default App;

```

