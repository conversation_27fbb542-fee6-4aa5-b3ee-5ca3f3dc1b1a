# Qrcode

## API

通用属性参考：[通用属性](/components/common-props)

> 自 `antd@5.1.0` 版本开始提供该组件。

| 参数       | 说明                                     | 类型                                      | 默认值        | 版本            |
| :--------- | :--------------------------------------- | :---------------------------------------- | :------------ | :-------------- |
| value      | 扫描后的文本                             | string                                    | -             |
| type       | 渲染类型                                 | `canvas \| svg `                          | `canvas`      | 5.6.0           |
| icon       | 二维码中图片的地址（目前只支持图片地址） | string                                    | -             |
| size       | 二维码大小                               | number                                    | 160           |
| iconSize   | 二维码中图片的大小                       | number                                    | 40            |
| color      | 二维码颜色                               | string                                    | `#000`        |
| bgColor    | 二维码背景颜色                           | string                                    | `transparent` | 5.5.0           |
| bordered   | 是否有边框                               | boolean                                   | `true`        |
| errorLevel | 二维码纠错等级                           | `'L' \| 'M' \| 'Q' \| 'H' `               | `M`           |
| status     | 二维码状态                               | `active \| expired \| loading \| scanned` | `active`      | scanned: 5.13.0 |
| onRefresh  | 点击"点击刷新"的回调                     | `() => void`                              | -             |

## 示例

### base

```tsx
import React from 'react';
import { Input, QRCode, Space } from '@otakus/design';

const App: React.FC = () => {
  const [text, setText] = React.useState('https://ant.design/');

  return (
    <Space direction="vertical" align="center">
      <QRCode value={text || '-'} />
      <Input
        placeholder="-"
        maxLength={60}
        value={text}
        onChange={(e) => setText(e.target.value)}
      />
    </Space>
  );
};

export default App;

```

### With Icon

```tsx
import React from 'react';
import { QRCode } from '@otakus/design';

const App: React.FC = () => (
  <QRCode
    errorLevel="H"
    value="https://ant.design/"
    icon="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
  />
);

export default App;

```

### other status

```tsx
import React from 'react';
import { Flex, QRCode } from '@otakus/design';

const value = 'https://ant.design';

const App: React.FC = () => (
  <Flex gap="middle" wrap="wrap">
    <QRCode value={value} status="loading" />
    <QRCode value={value} status="expired" onRefresh={() => console.log('refresh')} />
    <QRCode value={value} status="scanned" />
  </Flex>
);

export default App;

```

### Custom Render Type

```tsx
import React from 'react';
import { QRCode, Space } from '@otakus/design';

const App: React.FC = () => (
  <Space>
    <QRCode type="canvas" value="https://ant.design/" />
    <QRCode type="svg" value="https://ant.design/" />
  </Space>
);

export default App;

```

### Custom Size

```tsx
import React, { useState } from 'react';
import { MinusOutlined, PlusOutlined } from '@otakus/icons';
import { QRCode, Button } from '@otakus/design';

const App: React.FC = () => {
  const [size, setSize] = useState<number>(160);

  const increase = () => {
    setSize((prevSize) => {
      const newSize = prevSize + 10;
      if (newSize > 300) {
        return 300;
      }
      return newSize;
    });
  };

  const decline = () => {
    setSize((prevSize) => {
      const newSize = prevSize - 10;
      if (newSize < 48) {
        return 48;
      }
      return newSize;
    });
  };

  return (
    <>
      <Button.Group style={{ marginBottom: 16 }}>
        <Button onClick={decline} disabled={size <= 48} icon={<MinusOutlined />}>
          Smaller
        </Button>
        <Button onClick={increase} disabled={size >= 300} icon={<PlusOutlined />}>
          Larger
        </Button>
      </Button.Group>
      <QRCode
        errorLevel="H"
        size={size}
        iconSize={size / 4}
        value="https://ant.design/"
        icon="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
      />
    </>
  );
};

export default App;

```

### Custom Color

```tsx
import React from 'react';
import { QRCode, Space, theme } from '@otakus/design';

const { useToken } = theme;

const App: React.FC = () => {
  const { token } = useToken();
  return (
    <Space>
      <QRCode value="https://ant.design/" color={token.colorSuccessText} />
      <QRCode
        value="https://ant.design/"
        color={token.colorInfoText}
        bgColor={token.colorBgLayout}
      />
    </Space>
  );
};

export default App;

```

### Download QRCode

```tsx
import React from 'react';
import { Button, QRCode } from '@otakus/design';

const downloadQRCode = () => {
  const canvas = document.getElementById('myqrcode')?.querySelector<HTMLCanvasElement>('canvas');
  if (canvas) {
    const url = canvas.toDataURL();
    const a = document.createElement('a');
    a.download = 'QRCode.png';
    a.href = url;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }
};

const App: React.FC = () => (
  <div id="myqrcode">
    <QRCode value="https://ant.design/" bgColor="#fff" style={{ marginBottom: 16 }} />
    <Button type="primary" onClick={downloadQRCode}>
      Download
    </Button>
  </div>
);

export default App;

```

### Error Level

```tsx
import React, { useState } from 'react';
import type { QRCodeProps } from '@otakus/design';
import { Segmented, QRCode } from '@otakus/design';

const App: React.FC = () => {
  const [level, setLevel] = useState<string | number>('L');
  return (
    <>
      <QRCode
        style={{ marginBottom: 16 }}
        errorLevel={level as QRCodeProps['errorLevel']}
        value="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
      />
      <Segmented options={['L', 'M', 'Q', 'H']} value={level} onChange={setLevel} />
    </>
  );
};

export default App;

```

### Advanced Usage

```tsx
import React from 'react';
import { QRCode, Popover } from '@otakus/design';

const src = 'https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg';

const App: React.FC = () => (
  <Popover overlayInnerStyle={{ padding: 0 }} content={<QRCode value={src} bordered={false} />}>
    <img width={100} height={100} src={src} alt="icon" />
  </Popover>
);

export default App;

```

