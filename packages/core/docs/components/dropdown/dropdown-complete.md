# Dropdown

## API

通用属性参考：[通用属性](/components/common-props)

### Dropdown

| 参数               | 说明                                                                                                                                                          | 类型                                                           | 默认值              | 版本                  |
| ------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------- | ------------------- | --------------------- |
| arrow              | 下拉框箭头是否显示                                                                                                                                            | boolean \| { pointAtCenter: boolean }                          | false               |                       |
| autoAdjustOverflow | 下拉框被遮挡时自动调整位置                                                                                                                                    | boolean                                                        | true                | 5.2.0                 |
| autoFocus          | 打开后自动聚焦下拉框                                                                                                                                          | boolean                                                        | false               | 4.21.0                |
| disabled           | 菜单是否禁用                                                                                                                                                  | boolean                                                        | -                   |                       |
| destroyPopupOnHide | 关闭后是否销毁 Dropdown                                                                                                                                       | boolean                                                        | false               |                       |
| dropdownRender     | 自定义下拉框内容                                                                                                                                              | (menus: ReactNode) => ReactNode                                | -                   | 4.24.0                |
| getPopupContainer  | 菜单渲染父节点。默认渲染到 body 上，如果你遇到菜单滚动定位问题，试试修改为滚动的区域，并相对其定位。[示例](https://codepen.io/afc163/pen/zEjNOy?editors=0010) | (triggerNode: HTMLElement) => HTMLElement                      | () => document.body |                       |
| menu               | 菜单配置项                                                                                                                                                    | [MenuProps](/components/menu#api)                              | -                   | 4.24.0                |
| overlayClassName   | 下拉根元素的类名称                                                                                                                                            | string                                                         | -                   |                       |
| overlayStyle       | 下拉根元素的样式                                                                                                                                              | CSSProperties                                                  | -                   |                       |
| placement          | 菜单弹出位置：`bottom` `bottomLeft` `bottomRight` `top` `topLeft` `topRight`                                                                                  | string                                                         | `bottomLeft`        |                       |
| trigger            | 触发下拉的行为，移动端不支持 hover                                                                                                                            | Array&lt;`click`\|`hover`\|`contextMenu`>                      | \[`hover`]          |                       |
| open               | 菜单是否显示，小于 4.23.0 使用 `visible`                                                                                                                      | boolean                                                        | -                   | 4.23.0                |
| onOpenChange       | 菜单显示状态改变时调用，点击菜单按钮导致的消失不会触发。小于 4.23.0 使用 `onVisibleChange`                                                                    | (open: boolean, info: { source: 'trigger' \| 'menu' }) => void | -                   | `info.source`: 5.11.0 |

### Dropdown.Button

属性与 Dropdown 的相同。还包含以下属性：

| 参数          | 说明                                                         | 类型                                    | 默认值    | 版本   |
| ------------- | ------------------------------------------------------------ | --------------------------------------- | --------- | ------ |
| buttonsRender | 自定义左右两个按钮                                           | (buttons: ReactNode\[]) => ReactNode\[] | -         |        |
| loading       | 设置按钮载入状态                                             | boolean \| { delay: number }            | false     |        |
| danger        | 设置危险按钮                                                 | boolean                                 | -         | 4.23.0 |
| icon          | 右侧的 icon                                                  | ReactNode                               | -         |        |
| size          | 按钮大小，和 [Button](/components/button#api) 一致           | string                                  | `default` |        |
| type          | 按钮类型，和 [Button](/components/button#api) 一致           | string                                  | `default` |        |
| onClick       | 点击左侧按钮的回调，和 [Button](/components/button#api) 一致 | (event) => void                         | -         |        |

## 示例

### Basic

```tsx
import React from 'react';
import { DownOutlined, SettingOutlined } from '@otakus/icons';
import type { MenuProps } from '@otakus/design';
import { Dropdown, Space } from '@otakus/design';

const items: MenuProps['items'] = [
  {
    key: '1',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.antgroup.com">
        1st menu item
      </a>
    )
  },
  {
    key: '2',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.aliyun.com">
        2nd menu item (disabled)
      </a>
    ),
    icon: <SettingOutlined />,
    disabled: true
  },
  {
    key: '3',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.luohanacademy.com">
        3rd menu item (disabled)
      </a>
    ),
    disabled: true
  },
  {
    key: '4',
    danger: true,
    label: 'a danger item'
  }
];

const App: React.FC = () => (
  <Dropdown menu={{ items }}>
    <a onClick={(e) => e.preventDefault()}>
      <Space style={{ gap: 'var(--otakus-padding-xxs, 4px)' }}>
        Hover me
        <DownOutlined />
      </Space>
    </a>
  </Dropdown>
);

export default App;

```

### Placement

```tsx
import React from 'react';
import type { MenuProps } from '@otakus/design';
import { Button, Dropdown, Space } from '@otakus/design';

const items: MenuProps['items'] = [
  {
    key: '1',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.antgroup.com">
        1st menu item
      </a>
    )
  },
  {
    key: '2',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.aliyun.com">
        2nd menu item
      </a>
    )
  },
  {
    key: '3',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.luohanacademy.com">
        3rd menu item
      </a>
    )
  }
];

const App: React.FC = () => (
  <Space direction="vertical">
    <Space wrap>
      <Dropdown menu={{ items }} placement="bottomLeft">
        <Button>bottomLeft</Button>
      </Dropdown>
      <Dropdown menu={{ items }} placement="bottom">
        <Button>bottom</Button>
      </Dropdown>
      <Dropdown menu={{ items }} placement="bottomRight">
        <Button>bottomRight</Button>
      </Dropdown>
    </Space>
    <Space wrap>
      <Dropdown menu={{ items }} placement="topLeft">
        <Button>topLeft</Button>
      </Dropdown>
      <Dropdown menu={{ items }} placement="top">
        <Button>top</Button>
      </Dropdown>
      <Dropdown menu={{ items }} placement="topRight">
        <Button>topRight</Button>
      </Dropdown>
    </Space>
  </Space>
);

export default App;

```

### Arrow

```tsx
import React from 'react';
import type { MenuProps } from '@otakus/design';
import { Button, Dropdown } from '@otakus/design';

const items: MenuProps['items'] = [
  {
    key: '1',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.antgroup.com">
        1st menu item
      </a>
    )
  },
  {
    key: '2',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.aliyun.com">
        2nd menu item
      </a>
    )
  },
  {
    key: '3',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.luohanacademy.com">
        3rd menu item
      </a>
    )
  }
];

const App: React.FC = () => (
  <>
    <Dropdown menu={{ items }} placement="bottomLeft" arrow>
      <Button>bottomLeft</Button>
    </Dropdown>
    <Dropdown menu={{ items }} placement="bottom" arrow>
      <Button>bottom</Button>
    </Dropdown>
    <Dropdown menu={{ items }} placement="bottomRight" arrow>
      <Button>bottomRight</Button>
    </Dropdown>
    <br />
    <Dropdown menu={{ items }} placement="topLeft" arrow>
      <Button>topLeft</Button>
    </Dropdown>
    <Dropdown menu={{ items }} placement="top" arrow>
      <Button>top</Button>
    </Dropdown>
    <Dropdown menu={{ items }} placement="topRight" arrow>
      <Button>topRight</Button>
    </Dropdown>
  </>
);

export default App;

```

### Other elements

```tsx
import React from 'react';
import { DownOutlined } from '@otakus/icons';
import type { MenuProps } from '@otakus/design';
import { Dropdown, Space } from '@otakus/design';

const items: MenuProps['items'] = [
  {
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.antgroup.com">
        1st menu item
      </a>
    ),
    key: '0'
  },
  {
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.aliyun.com">
        2nd menu item
      </a>
    ),
    key: '1'
  },
  {
    type: 'divider'
  },
  {
    label: '3rd menu item（disabled）',
    key: '3',
    disabled: true
  }
];

const App: React.FC = () => (
  <Dropdown menu={{ items }}>
    <a onClick={(e) => e.preventDefault()}>
      <Space style={{ gap: 'var(--otakus-padding-xxs, 4px)' }}>
        Hover me
        <DownOutlined />
      </Space>
    </a>
  </Dropdown>
);

export default App;

```

### Arrow pointing at the center

```tsx
import React from 'react';
import type { MenuProps } from '@otakus/design';
import { Button, Dropdown } from '@otakus/design';

const items: MenuProps['items'] = [
  {
    key: '1',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.antgroup.com">
        1st menu item
      </a>
    )
  },
  {
    key: '2',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.aliyun.com">
        2nd menu item
      </a>
    )
  },
  {
    key: '3',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.luohanacademy.com">
        3rd menu item
      </a>
    )
  }
];

const App: React.FC = () => (
  <>
    <Dropdown menu={{ items }} placement="bottomLeft" arrow={{ pointAtCenter: true }}>
      <Button>bottomLeft</Button>
    </Dropdown>
    <Dropdown menu={{ items }} placement="bottom" arrow={{ pointAtCenter: true }}>
      <Button>bottom</Button>
    </Dropdown>
    <Dropdown menu={{ items }} placement="bottomRight" arrow={{ pointAtCenter: true }}>
      <Button>bottomRight</Button>
    </Dropdown>
    <br />
    <Dropdown menu={{ items }} placement="topLeft" arrow={{ pointAtCenter: true }}>
      <Button>topLeft</Button>
    </Dropdown>
    <Dropdown menu={{ items }} placement="top" arrow={{ pointAtCenter: true }}>
      <Button>top</Button>
    </Dropdown>
    <Dropdown menu={{ items }} placement="topRight" arrow={{ pointAtCenter: true }}>
      <Button>topRight</Button>
    </Dropdown>
  </>
);

export default App;

```

### Trigger mode

```tsx
import React from 'react';
import { DownOutlined } from '@otakus/icons';
import type { MenuProps } from '@otakus/design';
import { Dropdown, Space } from '@otakus/design';

const items: MenuProps['items'] = [
  {
    label: <a href="https://www.antgroup.com">1st menu item</a>,
    key: '0'
  },
  {
    label: <a href="https://www.aliyun.com">2nd menu item</a>,
    key: '1'
  },
  {
    type: 'divider'
  },
  {
    label: '3rd menu item',
    key: '3'
  }
];

const App: React.FC = () => (
  <Dropdown menu={{ items }} trigger={['click']}>
    <a onClick={(e) => e.preventDefault()}>
      <Space style={{ gap: 'var(--otakus-padding-xxs, 4px)' }}>
        Click me
        <DownOutlined />
      </Space>
    </a>
  </Dropdown>
);

export default App;

```

### Click event

```tsx
import React from 'react';
import { DownOutlined } from '@otakus/icons';
import type { MenuProps } from '@otakus/design';
import { Dropdown, message, Space } from '@otakus/design';

const onClick: MenuProps['onClick'] = ({ key }) => {
  message.info(`Click on item ${key}`);
};

const items: MenuProps['items'] = [
  {
    label: '1st menu item',
    key: '1'
  },
  {
    label: '2nd menu item',
    key: '2'
  },
  {
    label: '3rd menu item',
    key: '3'
  }
];

const App: React.FC = () => (
  <Dropdown menu={{ items, onClick }}>
    <a onClick={(e) => e.preventDefault()}>
      <Space style={{ gap: 'var(--otakus-padding-xxs, 4px)' }}>
        Hover me, Click menu item
        <DownOutlined />
      </Space>
    </a>
  </Dropdown>
);

export default App;

```

### Button with dropdown menu

```tsx
import React from 'react';
import { DownOutlined, UserOutlined } from '@otakus/icons';
import type { MenuProps } from '@otakus/design';
import { Button, Dropdown, message, Space, Tooltip } from '@otakus/design';

const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
  message.info('Click on left button.');
  console.log('click left button', e);
};

const handleMenuClick: MenuProps['onClick'] = (e) => {
  message.info('Click on menu item.');
  console.log('click', e);
};

const items: MenuProps['items'] = [
  {
    label: '1st menu item',
    key: '1',
    icon: <UserOutlined />
  },
  {
    label: '2nd menu item',
    key: '2',
    icon: <UserOutlined />
  },
  {
    label: '3rd menu item',
    key: '3',
    icon: <UserOutlined />,
    danger: true
  },
  {
    label: '4rd menu item',
    key: '4',
    icon: <UserOutlined />,
    danger: true,
    disabled: true
  }
];

const menuProps = {
  items,
  onClick: handleMenuClick
};

const App: React.FC = () => (
  <Space wrap>
    <Dropdown.Button menu={menuProps} onClick={handleButtonClick}>
      Dropdown
    </Dropdown.Button>
    <Dropdown.Button menu={menuProps} placement="bottom" icon={<UserOutlined />}>
      Dropdown
    </Dropdown.Button>
    <Dropdown.Button menu={menuProps} onClick={handleButtonClick} disabled>
      Dropdown
    </Dropdown.Button>
    <Dropdown.Button
      menu={menuProps}
      buttonsRender={([leftButton, rightButton]) => [
        <Tooltip title="tooltip" key="leftButton">
          {leftButton}
        </Tooltip>,
        React.cloneElement(rightButton as React.ReactElement<any, string>, { loading: true })
      ]}
    >
      With Tooltip
    </Dropdown.Button>
    <Dropdown menu={menuProps}>
      <Button>
        <Space>
          Button
          <DownOutlined />
        </Space>
      </Button>
    </Dropdown>
    <Dropdown.Button menu={menuProps} onClick={handleButtonClick} danger>
      Danger
    </Dropdown.Button>
  </Space>
);

export default App;

```

### Custom dropdown

```tsx
import React from 'react';
import { DownOutlined } from '@otakus/icons';
import { Dropdown, Space, Divider, Button, theme } from '@otakus/design';
import type { MenuProps } from '@otakus/design';

const { useToken } = theme;

const items: MenuProps['items'] = [
  {
    key: '1',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.antgroup.com">
        1st menu item
      </a>
    )
  },
  {
    key: '2',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.aliyun.com">
        2nd menu item (disabled)
      </a>
    ),
    disabled: true
  },
  {
    key: '3',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.luohanacademy.com">
        3rd menu item (disabled)
      </a>
    ),
    disabled: true
  }
];

const App: React.FC = () => {
  const { token } = useToken();

  const contentStyle: React.CSSProperties = {
    backgroundColor: token.colorBgElevated,
    borderRadius: token.borderRadiusLG,
    boxShadow: token.boxShadowSecondary
  };

  const menuStyle: React.CSSProperties = {
    boxShadow: 'none'
  };

  return (
    <Dropdown
      menu={{ items }}
      dropdownRender={(menu) => (
        <div style={contentStyle}>
          {React.cloneElement(menu as React.ReactElement, { style: menuStyle })}
          <Divider style={{ margin: 0 }} />
          <Space style={{ padding: 8 }}>
            <Button type="primary">Click me!</Button>
          </Space>
        </div>
      )}
    >
      <a onClick={(e) => e.preventDefault()}>
        <Space style={{ gap: 'var(--otakus-padding-xxs, 4px)' }}>
          Hover me
          <DownOutlined />
        </Space>
      </a>
    </Dropdown>
  );
};

export default App;

```

### Cascading menu

```tsx
import React from 'react';
import { DownOutlined } from '@otakus/icons';
import type { MenuProps } from '@otakus/design';
import { Dropdown, Space } from '@otakus/design';

const items: MenuProps['items'] = [
  {
    key: '1',
    type: 'group',
    label: 'Group title',
    children: [
      {
        key: '1-1',
        label: '1st menu item'
      },
      {
        key: '1-2',
        label: '2nd menu item'
      }
    ]
  },
  {
    key: '2',
    label: 'sub menu',
    children: [
      {
        key: '2-1',
        label: '3rd menu item'
      },
      {
        key: '2-2',
        label: '4th menu item'
      }
    ]
  },
  {
    key: '3',
    label: 'disabled sub menu',
    disabled: true,
    children: [
      {
        key: '3-1',
        label: '5d menu item'
      },
      {
        key: '3-2',
        label: '6th menu item'
      }
    ]
  }
];

const App: React.FC = () => (
  <Dropdown menu={{ items }}>
    <a onClick={(e) => e.preventDefault()}>
      <Space style={{ gap: 'var(--otakus-padding-xxs, 4px)' }}>
        Cascading menu
        <DownOutlined />
      </Space>
    </a>
  </Dropdown>
);

export default App;

```

### Cascading menu

```tsx
import React from 'react';
import { DownOutlined } from '@otakus/icons';
import type { MenuProps } from '@otakus/design';
import { Dropdown, Space } from '@otakus/design';

const items: MenuProps['items'] = [
  {
    key: '1',
    type: 'group',
    label: 'Group title',
    children: [
      {
        key: '1-1',
        label: '1st menu item'
      },
      {
        key: '1-2',
        label: '2nd menu item'
      }
    ]
  },
  {
    key: '2',
    label: 'sub menu',
    children: [
      {
        key: '2-1',
        label: '3rd menu item'
      },
      {
        key: '2-2',
        label: '4th menu item'
      }
    ]
  },
  {
    key: '3',
    label: 'disabled sub menu',
    disabled: true,
    children: [
      {
        key: '3-1',
        label: '5d menu item'
      },
      {
        key: '3-2',
        label: '6th menu item'
      }
    ]
  }
];

const App: React.FC = () => (
  <div style={{ height: 200 }}>
    <Dropdown menu={{ items, openKeys: ['2'] }} open autoAdjustOverflow={false}>
      <a onClick={(e) => e.preventDefault()}>
        <Space style={{ gap: 'var(--otakus-padding-xxs, 4px)' }}>
          Cascading menu
          <DownOutlined />
        </Space>
      </a>
    </Dropdown>
  </div>
);

export default App;

```

### The way of hiding menu.

```tsx
import React, { useState } from 'react';
import { DownOutlined } from '@otakus/icons';
import type { DropdownProps, MenuProps } from '@otakus/design';
import { Dropdown, Space } from '@otakus/design';

const App: React.FC = () => {
  const [open, setOpen] = useState(false);

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    if (e.key === '3') {
      setOpen(false);
    }
  };

  const handleOpenChange: DropdownProps['onOpenChange'] = (nextOpen, info) => {
    if (info.source === 'trigger' || nextOpen) {
      setOpen(nextOpen);
    }
  };

  const items: MenuProps['items'] = [
    {
      label: 'Clicking me will not close the menu.',
      key: '1'
    },
    {
      label: 'Clicking me will not close the menu also.',
      key: '2'
    },
    {
      label: 'Clicking me will close the menu.',
      key: '3'
    }
  ];

  return (
    <Dropdown
      menu={{
        items,
        onClick: handleMenuClick
      }}
      onOpenChange={handleOpenChange}
      open={open}
    >
      <a onClick={(e) => e.preventDefault()}>
        <Space style={{ gap: 'var(--otakus-padding-xxs, 4px)' }}>
          Hover me
          <DownOutlined />
        </Space>
      </a>
    </Dropdown>
  );
};

export default App;

```

### Context Menu

```tsx
import React from 'react';
import type { MenuProps } from '@otakus/design';
import { Dropdown, theme } from '@otakus/design';

const items: MenuProps['items'] = [
  {
    label: '1st menu item',
    key: '1'
  },
  {
    label: '2nd menu item',
    key: '2'
  },
  {
    label: '3rd menu item',
    key: '3'
  }
];

const App: React.FC = () => {
  const {
    token: { colorBgLayout, colorTextTertiary }
  } = theme.useToken();

  return (
    <Dropdown menu={{ items }} trigger={['contextMenu']}>
      <div
        style={{
          color: colorTextTertiary,
          background: colorBgLayout,
          height: 200,
          textAlign: 'center',
          lineHeight: '200px'
        }}
      >
        Right Click on here
      </div>
    </Dropdown>
  );
};

export default App;

```

### Loading

```tsx
import React, { useState } from 'react';
import { DownOutlined } from '@otakus/icons';
import type { MenuProps } from '@otakus/design';
import { Dropdown, Space } from '@otakus/design';

const items: MenuProps['items'] = [
  {
    label: 'Submit and continue',
    key: '1'
  }
];

const App: React.FC = () => {
  const [loadings, setLoadings] = useState<boolean[]>([]);

  const enterLoading = (index: number) => {
    setLoadings((state) => {
      const newLoadings = [...state];
      newLoadings[index] = true;
      return newLoadings;
    });

    setTimeout(() => {
      setLoadings((state) => {
        const newLoadings = [...state];
        newLoadings[index] = false;
        return newLoadings;
      });
    }, 6000);
  };

  return (
    <Space direction="vertical">
      <Dropdown.Button type="primary" loading menu={{ items }}>
        Submit
      </Dropdown.Button>
      <Dropdown.Button type="primary" size="small" loading menu={{ items }}>
        Submit
      </Dropdown.Button>
      <Dropdown.Button
        type="primary"
        loading={loadings[0]}
        menu={{ items }}
        onClick={() => enterLoading(0)}
      >
        Submit
      </Dropdown.Button>
      <Dropdown.Button
        icon={<DownOutlined />}
        loading={loadings[1]}
        menu={{ items }}
        onClick={() => enterLoading(1)}
      >
        Submit
      </Dropdown.Button>
    </Space>
  );
};

export default App;

```

### Selectable Menu

```tsx
import React from 'react';
import { DownOutlined } from '@otakus/icons';
import type { MenuProps } from '@otakus/design';
import { Dropdown, Space, Typography } from '@otakus/design';

const items: MenuProps['items'] = [
  {
    key: '1',
    label: 'Item 1'
  },
  {
    key: '2',
    label: 'Item 2'
  },
  {
    key: '3',
    label: 'Item 3'
  }
];

const App: React.FC = () => (
  <Dropdown
    menu={{
      items,
      selectable: true,
      defaultSelectedKeys: ['3']
    }}
  >
    <Typography.Link>
      <Space style={{ gap: 'var(--otakus-padding-xxs, 4px)' }}>
        Selectable
        <DownOutlined />
      </Space>
    </Typography.Link>
  </Dropdown>
);

export default App;

```

### Menu full styles

```tsx
import React from 'react';
import { AppstoreOutlined, DownOutlined, MailOutlined, SettingOutlined } from '@otakus/icons';
import type { MenuProps } from '@otakus/design';
import { Dropdown, Space } from '@otakus/design';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[],
  type?: 'group'
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
    type
  } as MenuItem;
}

const items: MenuItem[] = [
  getItem(
    'Item Group',
    'group',
    null,
    [getItem('Option 0', '01'), getItem('Option 0', '02')],
    'group'
  ),
  getItem('Navigation One', 'sub1', <MailOutlined />, [
    getItem('Item 1', 'g1', null, [getItem('Option 1', '1'), getItem('Option 2', '2')], 'group'),
    getItem('Item 2', 'g2', null, [getItem('Option 3', '3'), getItem('Option 4', '4')], 'group')
  ]),
  getItem('Navigation Two', 'sub2', <AppstoreOutlined />, [
    getItem('Option 5', '5'),
    getItem('Option 6', '6'),
    getItem('Submenu', 'sub3', null, [getItem('Option 7', '7'), getItem('Option 8', '8')])
  ]),
  getItem('Navigation Three', 'sub4', <SettingOutlined />, [
    getItem('Option 9', '9'),
    getItem('Option 10', '10'),
    getItem('Option 11', '11'),
    getItem('Option 12', '12')
  ]),
  // Not crash
  null as any
];

const App: React.FC = () => (
  <Dropdown
    menu={{
      items,
      selectedKeys: ['1'],
      openKeys: ['sub1']
    }}
  >
    <a onClick={(e) => e.preventDefault()}>
      <Space style={{ gap: 'var(--otakus-padding-xxs, 4px)' }}>
        Hover to check menu style
        <DownOutlined />
      </Space>
    </a>
  </Dropdown>
);

export default App;

```

### \_InternalPanelDoNotUseOrYouWillBeFired

```tsx
import React from 'react';
import { SettingOutlined } from '@otakus/icons';
import { Dropdown } from '@otakus/design';

const { _InternalPanelDoNotUseOrYouWillBeFired: InternalDropdown } = Dropdown;

const menu = [
  {
    key: '1',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.antgroup.com">
        1st menu item
      </a>
    )
  },
  {
    key: '2',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.aliyun.com">
        2nd menu item (disabled)
      </a>
    ),
    icon: <SettingOutlined />,
    disabled: true
  },
  {
    key: '3',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="https://www.luohanacademy.com">
        3rd menu item (disabled)
      </a>
    ),
    disabled: true
  },
  {
    key: '4',
    danger: true,
    label: 'a danger item'
  }
];

const App: React.FC = () => <InternalDropdown menu={{ items: menu }} />;

export default App;

```

### Icon debug

```tsx
import { DownOutlined } from '@otakus/icons';
import React from 'react';
import { Dropdown, Space } from '@otakus/design';

const App: React.FC = () => (
  <Space>
    <Dropdown.Button icon={<DownOutlined />} menu={{ items: [] }}>
      Submit
    </Dropdown.Button>
  </Space>
);

export default App;

```

