# Icon

# Otakus Icons for React

## 安装

```bash
pnpm add @otakus/icons@latest
```

包发布在 info-npm-core：[@otakus/icons](https://info-npm-core.mihoyo.com/-/libraryDetail?name=@otakus/icons)。

## 使用

当你的构建工具开启了 `tree-shaking` 后，以下两种方式引入都可以，未使用到的图标在构建时会被排除。

```ts
import SettingOutlined from '@otakus/icons/SettingOutlined';
import { SettingOutlined } from '@otakus/icons';
```

如果目前版本的图标资源不能满足你的使用需要，请在 [这里](https://km.mihoyo.com/articleBase/19634/740448) 提交新增需求和期望交付日期等信息，正常每周五更新绘制完成的图标资源。

## 图标列表

<IconSearch></IconSearch>

## API

| 属性         | 说明                                   | 类型                  | 默认值    | 版本                         |
| :----------- | :------------------------------------- | :-------------------- | :-------- | :--------------------------- |
| twoToneColor | 仅适用双色图标。设置双色图标的主要颜色 | string (十六进制颜色) | `default` | Otaku 内废弃，不提供双色图标 |

### IconProps Interface

```ts
interface IconProps {
  className?: string;
  onClick?: React.MouseEventHandler<SVGSVGElement>;
  style?: React.CSSProperties;
}
```

其他组件 API 定义见 [antd Icon API](https://ant.design/components/icon-cn#api)

:::warning
HolderOutlined图标已被移除，请改用DragOutlined
:::

## CHANGELOG

### 10.15.0

`2025-06-25`

新增 2 个图标：`SubscribeFilled`, `KmUpFilled`

重绘 3 个图标：`EditFilled`, `CalendarFilled`, `ClockCircleFilled`

### 10.14.0

`2025-06-16`

新增 2 个图标：`WorkflowDefaultColorful`, `WorkflowActiveColorful`

### 10.13.0

`2025-05-19`

新增 5 个图标：`ExternalContactsFilled`, `FirmFilled`, `Group2Filled`, `ItemFilled`, `OrgFilled`

重绘 3 个图标：`PictureFilled`, `FolderFilled`, `GroupFilled`

### 10.12.0

`2025-03-31`

新增 1 个图标：`FileUploadDisableColorful`

### 10.11.0

`2025-03-19`

新增 2 个图标：`BankActiveColorful`, `BankDefaultColorful`

修改 1 个图标：`RequiredColorful`: 去除背景色

### 10.10.0

`2025-03-12`

新增 2 个图标：`RequiredEnhanceColorful`, `RequiredColorful`

### 10.9.0

`2025-03-03`

新增 1 个图标：`GroupFilled`

修复多彩图标在 Safari 下图标渲染错误的 bug

### 10.8.0

`2024-12-26`

新增 1 个线性图标：`PhoneOutlined`

### 10.7.0

`2024-12-18`

新增 1 个线性图标：`BoxOutlined`

### 10.6.0

`2024-11-08`

新增 2 个线性图标：`ConnectionBoxOutlined`、`HeadsetOutlined`。

新增 4 个多色图标：`AvatarCompanyColorful`、`AvatarGroupColorful`、`AvatarOrganizationColorful`、`AvatarPersonColorful`

### 10.5.0

`2024-10-25`

新增 1 个图标，`FileUploadColorful`

### 10.4.0

`2024-10-12`

重绘 2 个图标，`FullscreenOutlined`、`FullscreenExitOutlined`

### 10.3.0

`2024-08-23`

新增 2 个图标，`MoneyDefaultColorful`、`MoneyActiveColorful`

### 10.2.0

`2024-08-23`

重绘 2 个图标，`IssuesDefaultColorful`、`IssuesActiveColorful`

新增 12 个文件类型相关的图标，`FileDocColorful`、`FileMagColorful`、`FileMp3Colorful`、`FileMp4Colorful`、`FileOtherColorful`、`FilePdfColorful`、`FilePicColorful`、`FilePptColorful`、`FileTxtColorful`、`FileWaitingColorful`、`FileXslColorful`、`FileZipColorful`、

### 10.1.0

`2024-07-23`

由 `@ant-design/icons` 内同步图标 1 个图标 `BulbOutlined`。

### 10.0.0

`2024-07-23`

移除 2 个图标，`FileSettingDefaultColorful`、`FileSettingActiveColorful`

新增 2 个图标，`TraceDefaultColorful`、`TraceActiveColorful`

由 `@ant-design/icons` 内同步图标 1 个图标 `VerticalAlignMiddleOutlined`。

重绘 4 个图标，`BriefcaseDefaultColorful`、`BriefcaseActiveColorful`、`StoreDefaultColorful`、`StoreActiveColorful`

### 9.2.0

`2024-07-02`

新增 `Colorful` 多彩风格图标资源。

<details>
<summary>详细日志</summary>

**🆕 新增图标资源**

- **多彩风格 Colorful :**
  - 通用 24 个：`AddedDefault` `AddedActive` `SearchDefault` `SearchActive` `ProcessingDefault` `ProcessingActive` `ChecklistDefault` `ChecklistActive` `FileSettingDefault` `FileSettingActive` `FileCollectDefault` `FileCollectActive` `MultifilesDefault` `MultifilesActive` `WriteDefault` `WriteActive` `ContractSealDefault` `ContractSealActive` `IssuesDefault` `IssuesActive` `BatchDefault` `BatchActive` `TransferDefault` `TransferActive`
  - 数据 20 个：`InsightDefault` `InsightActive` `PieDefault` `PieActive` `CloudStudioDefault` `CloudStudioActive` `CodingDefault` `CodingActive` `InfrastructureDefault` `InfrastructureActive` `OkrDefault` `OkrActive` `DashboardDefault` `DashboardActive` `AutomationDefault` `AutomationActive` `FinancialSharedDefault` `FinancialSharedActive` `Coding1Default` `Coding1Active`
  - 编辑 24 个：`AddedDefault` `AddedActive` `SearchDefault` `SearchActive` `ProcessingDefault` `ProcessingActive` `ChecklistDefault` `ChecklistActive` `FileSettingDefault` `FileSettingActive` `FileCollectDefault` `FileCollectActive` `MultifilesDefault` `MultifilesActive` `WriteDefault` `WriteActive` `ContractSealDefault` `ContractSealActive` `IssuesDefault` `IssuesActive` `BatchDefault` `BatchActive` `TransferDefault` `TransferActive`

</details>

### 9.1.0

`2024-05-12`

新增 1 个图标 `UserFilled`。

由 `infosys-icons-react` 内同步图标 5 个。

<details>
<summary>详细日志</summary>

**🆕 新增图标资源**

- **填充风格 Filled :**

  - 设计重绘 `UserFilled`，由 `infosys-icons-react` 内同步 `BoardFilled` `TeamFilled`

- **线框风格 Outlined :**
  - 由 `infosys-icons-react` 内同步 `LayerOutlined` `DoubleDownOutlined` `DoubleUpOutlined`

</details>

### 9.0.0

`2024-05-11`

新增 7 个图标，`PaperclipOutlined` 更名为 `PaperClipOutlined`，与 antd 原版保持一致。

由 `@ant-design/icons` 内同步图标 Outlined 123 个，Filled 118 个。

<details>
<summary>详细日志</summary>

**🆕 新增图标资源**

- **线框风格 Outlined :**
  - `ClearOutlined` `CloudOutlined` `FlagOutlined` `UserAdminOutlined` `UserApproveOutlined` `UserEditOutlined` `UserProhibitOutlined`

**🔥 图标更名**

- `PaperclipOutlined` 更名为 `PaperClipOutlined`，与 antd 原版保持一致

**🛠 由 @ant-design/icons 内同步**

- 同步图标种类详情见 [507de2f0ecaac111dd80625e398959c10ce20199](https://platgit.mihoyo.com/ee/infra/otaku-design/icons/-/commit/507de2f0ecaac111dd80625e398959c10ce20199)

</details>

### 8.0.0

`2024-04-30`

新增 2 个图标，重绘 3 个图标。

<details>
<summary>详细日志</summary>

**🆕 新增图标资源**

- **线框风格 Outlined :**
  - `HourglassOutlined` `SendOutlined`

**🛠 重绘图标资源**

- `ClockOutlined` 更名为 `ClockCircleOutlined`，`MenuUnfoldOutlined` 和 `MenuFoldOutlined` 两个图标内容替换

</details>

### 7.1.0

`2024-04-19`

新增 7 个图标，重绘 2 个图标。

<details>
<summary>详细日志</summary>

**🆕 新增图标资源**

- **线框风格 Outlined :**

  - `AddFilterOutlined` `PrinterOutlined` `ShareAltOutlined` `SortAmountDownOutlined` `SortAmountUpOutlined` `UserAddOutlined`

- **填充风格 Filled :**
  - `StarFilled`

**🛠 重绘图标资源**

- `SunOutlined` `ClickOutlined`

</details>

### 7.0.0

`2024-04-03`

整体 `viewbox` 调整，引入后整体图标视觉效果增大；新增 16 个图标。

<details>
<summary>详细日志</summary>

**🆕 新增图标资源**

- **线框风格 Outlined :**
  - `Check2Outlined` `Minus2Outlined` `MouseOutlined` `MoonOutlined` `MobileOutlined` `Plus2Outlined` `NumberOutlined` `DataCollectionOutlined` `EmbedChartOutlined` `FaceRecognitionOutlined` `FingerprintOutlined` `IdCardOutlined` `SunOutlined` `ReadOutlined` `QrcodeOutlined` `Desktop`

</details>

### 6.1.0

`2024-03-22`

新增 22 个图标，重绘 2 个图标。

<details>
<summary>详细日志</summary>

**🆕 新增图标资源**

- **线框风格 Outlined :**

  - `ClickOutlined` `Down2Outlined` `Down3Outlined` `FalloutOutlined` `FileAddOutlined` `FunnelOutlined` `Left2Outlined` `Left3Outlined` `LeftCircleOutlined` `LoadingOutlined` `PaperclipOutlined` `Right2Outlined` `Right3Outlined` `RightCircleOutlined` `RiseOutlined` `StopOutlined` `Up2Outlined` `Up3Outlined`

- **填充风格 Filled :**
  - `SettingFilled`

**🛠 重绘图标资源**

- `DownloadCircleOutlined` `UploadCircleOutlined`

</details>

