# Collapse

## API

通用属性参考：[通用属性](/components/common-props)

### Collapse

| 参数                 | 说明                                     | 类型                                                                                                                      | 默认值                                                 | 版本   |
| -------------------- | ---------------------------------------- | ------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------ | ------ |
| accordion            | 手风琴模式                               | boolean                                                                                                                   | false                                                  |        |
| activeKey            | 当前激活 tab 面板的 key                  | string\[] \| string <br/> number\[] \| number                                                                             | [手风琴模式](#collapse-demo-accordion)下默认第一个元素 |        |
| bordered             | 带边框风格的折叠面板                     | boolean                                                                                                                   | true                                                   |        |
| collapsible          | 所有子面板是否可折叠或指定可折叠触发区域 | `header` \| `icon` \| `disabled`                                                                                          | -                                                      | 4.9.0  |
| defaultActiveKey     | 初始化选中面板的 key                     | string\[] \| string<br/> number\[] \| number                                                                              | -                                                      |        |
| destroyInactivePanel | 销毁折叠隐藏的面板                       | boolean                                                                                                                   | false                                                  |        |
| expandIcon           | 自定义切换图标                           | (panelProps) => ReactNode                                                                                                 | -                                                      |        |
| expandIconPosition   | 设置图标位置                             | `start` \| `end`                                                                                                          | -                                                      | 4.21.0 |
| ghost                | 使折叠面板透明且无边框                   | boolean                                                                                                                   | false                                                  | 4.4.0  |
| size                 | 设置折叠面板大小                         | `large` \| `middle` \| `small`                                                                                            | `middle`                                               | 5.2.0  |
| onChange             | 切换面板的回调                           | function                                                                                                                  | -                                                      |        |
| items                | 折叠项目内容                             | [ItemType](https://github.com/react-component/collapse/blob/27250ca5415faab16db412b9bff2c131bb4f32fc/src/interface.ts#L6) | -                                                      | 5.6.0  |

### Collapse.Panel


:::info{title=已废弃}
版本 >= 5.6.0 时请使用 items 方式配置面板。
:::

| 参数        | 说明                                                               | 类型                             | 默认值 | 版本                 |
| ----------- | ------------------------------------------------------------------ | -------------------------------- | ------ | -------------------- |
| collapsible | 是否可折叠或指定可折叠触发区域                                     | `header` \| `icon` \| `disabled` | -      | 4.9.0 (icon: 4.24.0) |
| extra       | 自定义渲染每个面板右上角的内容                                     | ReactNode                        | -      |                      |
| forceRender | 被隐藏时是否渲染 DOM 结构                                          | boolean                          | false  |                      |
| header      | 面板头内容                                                         | ReactNode                        | -      |                      |
| key         | 对应 activeKey                                                     | string \| number                 | -      |                      |
| showArrow   | 是否展示当前面板上的箭头（为 false 时，collapsible 不能置为 icon） | boolean                          | true   |                      |

## 示例

### Collapse

```tsx
import React from 'react';
import type { CollapseProps } from '@otakus/design';
import { Collapse } from '@otakus/design';

const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;

const items: CollapseProps['items'] = [
  {
    key: '1',
    label: 'This is panel header 1',
    children: <p>{text}</p>
  },
  {
    key: '2',
    label: 'This is panel header 2',
    children: <p>{text}</p>
  },
  {
    key: '3',
    label: 'This is panel header 3',
    children: <p>{text}</p>
  }
];

const App: React.FC = () => {
  const onChange = (key: string | string[]) => {
    console.log(key);
  };

  return <Collapse items={items} defaultActiveKey={['1']} onChange={onChange} />;
};

export default App;

```

### Size

```tsx
import React from 'react';
import { Collapse, Divider } from '@otakus/design';

const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;

const App: React.FC = () => (
  <>
    <Divider orientation="left">Default Size</Divider>
    <Collapse
      items={[{ key: '1', label: 'This is default size panel header', children: <p>{text}</p> }]}
    />
    <Divider orientation="left">Small Size</Divider>
    <Collapse
      size="small"
      items={[{ key: '1', label: 'This is small size panel header', children: <p>{text}</p> }]}
    />
    <Divider orientation="left">Large Size</Divider>
    <Collapse
      size="large"
      items={[{ key: '1', label: 'This is large size panel header', children: <p>{text}</p> }]}
    />
  </>
);

export default App;

```

### Accordion

```tsx
import React from 'react';
import type { CollapseProps } from '@otakus/design';
import { Collapse } from '@otakus/design';

const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;

const items: CollapseProps['items'] = [
  {
    key: '1',
    label: 'This is panel header 1',
    children: <p>{text}</p>
  },
  {
    key: '2',
    label: 'This is panel header 2',
    children: <p>{text}</p>
  },
  {
    key: '3',
    label: 'This is panel header 3',
    children: <p>{text}</p>
  }
];

const App: React.FC = () => <Collapse accordion items={items} />;

export default App;

```

### Nested panel

```tsx
import React from 'react';
import type { CollapseProps } from '@otakus/design';
import { Collapse } from '@otakus/design';

const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;

const itemsNest: CollapseProps['items'] = [
  {
    key: '1',
    label: 'This is panel nest panel',
    children: <p>{text}</p>
  }
];

const items: CollapseProps['items'] = [
  {
    key: '1',
    label: 'This is panel header 1',
    children: <Collapse defaultActiveKey="1" items={itemsNest} />
  },
  {
    key: '2',
    label: 'This is panel header 2',
    children: <p>{text}</p>
  },
  {
    key: '3',
    label: 'This is panel header 3',
    children: <p>{text}</p>
  }
];

const App: React.FC = () => {
  const onChange = (key: string | string[]) => {
    console.log(key);
  };

  return <Collapse onChange={onChange} items={items} />;
};

export default App;

```

### Borderless

```tsx
import React from 'react';
import type { CollapseProps } from '@otakus/design';
import { Collapse } from '@otakus/design';

const text = (
  <p style={{ paddingLeft: 24 }}>
    A dog is a type of domesticated animal. Known for its loyalty and faithfulness, it can be found
    as a welcome guest in many households across the world.
  </p>
);

const items: CollapseProps['items'] = [
  {
    key: '1',
    label: 'This is panel header 1',
    children: text
  },
  {
    key: '2',
    label: 'This is panel header 2',
    children: text
  },
  {
    key: '3',
    label: 'This is panel header 3',
    children: text
  }
];

const App: React.FC = () => <Collapse items={items} bordered={false} defaultActiveKey={['1']} />;

export default App;

```

### Custom Panel

```tsx
import { CaretRightOutlined } from '@otakus/icons';
import type { CSSProperties } from 'react';
import React from 'react';
import type { CollapseProps } from '@otakus/design';
import { Collapse, theme } from '@otakus/design';

const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;

const getItems: (panelStyle: CSSProperties) => CollapseProps['items'] = (panelStyle) => [
  {
    key: '1',
    label: 'This is panel header 1',
    children: <p>{text}</p>,
    style: panelStyle
  },
  {
    key: '2',
    label: 'This is panel header 2',
    children: <p>{text}</p>,
    style: panelStyle
  },
  {
    key: '3',
    label: 'This is panel header 3',
    children: <p>{text}</p>,
    style: panelStyle
  }
];

const App: React.FC = () => {
  const { token } = theme.useToken();

  const panelStyle: React.CSSProperties = {
    marginBottom: 24,
    background: token.colorFillAlter,
    borderRadius: token.borderRadiusLG,
    border: 'none'
  };

  return (
    <Collapse
      bordered={false}
      defaultActiveKey={['1']}
      expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
      style={{ background: token.colorBgContainer }}
      items={getItems(panelStyle)}
    />
  );
};

export default App;

```

### No arrow

```tsx
import React from 'react';
import type { CollapseProps } from '@otakus/design';
import { Collapse } from '@otakus/design';

const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;

const items: CollapseProps['items'] = [
  {
    key: '1',
    label: 'This is panel header with arrow icon',
    children: <p>{text}</p>
  },
  {
    key: '2',
    label: 'This is panel header with no arrow icon',
    children: <p>{text}</p>,
    showArrow: false
  }
];

const App: React.FC = () => {
  const onChange = (key: string | string[]) => {
    console.log(key);
  };

  return <Collapse defaultActiveKey={['1']} onChange={onChange} items={items} />;
};

export default App;

```

### Extra node

```tsx
import { SettingOutlined } from '@otakus/icons';
import React, { useState } from 'react';
import type { CollapseProps } from '@otakus/design';
import { Collapse, Select } from '@otakus/design';

const { Option } = Select;

const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;

type ExpandIconPosition = 'start' | 'end';

const App: React.FC = () => {
  const [expandIconPosition, setExpandIconPosition] = useState<ExpandIconPosition>('start');

  const onPositionChange = (newExpandIconPosition: ExpandIconPosition) => {
    setExpandIconPosition(newExpandIconPosition);
  };

  const onChange = (key: string | string[]) => {
    console.log(key);
  };

  const genExtra = () => (
    <SettingOutlined
      onClick={(event) => {
        // If you don't want click extra trigger collapse, you can prevent this:
        event.stopPropagation();
      }}
    />
  );

  const items: CollapseProps['items'] = [
    {
      key: '1',
      label: 'This is panel header 1',
      children: <div>{text}</div>,
      extra: genExtra()
    },
    {
      key: '2',
      label: 'This is panel header 2',
      children: <div>{text}</div>,
      extra: genExtra()
    },
    {
      key: '3',
      label: 'This is panel header 3',
      children: <div>{text}</div>,
      extra: genExtra()
    }
  ];

  return (
    <>
      <Collapse
        defaultActiveKey={['1']}
        onChange={onChange}
        expandIconPosition={expandIconPosition}
        items={items}
      />
      <br />
      <span>Expand Icon Position: </span>
      <Select value={expandIconPosition} style={{ margin: '0 8px' }} onChange={onPositionChange}>
        <Option value="start">start</Option>
        <Option value="end">end</Option>
      </Select>
    </>
  );
};

export default App;

```

### Ghost Collapse

```tsx
import React from 'react';
import type { CollapseProps } from '@otakus/design';
import { Collapse } from '@otakus/design';

const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;

const items: CollapseProps['items'] = [
  {
    key: '1',
    label: 'This is panel header 1',
    children: <p>{text}</p>
  },
  {
    key: '2',
    label: 'This is panel header 2',
    children: <p>{text}</p>
  },
  {
    key: '3',
    label: 'This is panel header 3',
    children: <p>{text}</p>
  }
];

const App: React.FC = () => <Collapse defaultActiveKey={['1']} ghost items={items} />;

export default App;

```

### Collapsible

```tsx
import React from 'react';
import { Collapse, Space } from '@otakus/design';

const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;

const App: React.FC = () => (
  <Space direction="vertical">
    <Collapse
      collapsible="header"
      defaultActiveKey={['1']}
      items={[
        {
          key: '1',
          label: 'This panel can only be collapsed by clicking text',
          children: <p>{text}</p>
        }
      ]}
    />
    <Collapse
      collapsible="icon"
      defaultActiveKey={['1']}
      items={[
        {
          key: '1',
          label: 'This panel can only be collapsed by clicking icon',
          children: <p>{text}</p>
        }
      ]}
    />
    <Collapse
      collapsible="disabled"
      items={[
        {
          key: '1',
          label: "This panel can't be collapsed",
          children: <p>{text}</p>
        }
      ]}
    />
  </Space>
);

export default App;

```

### Component Token

```tsx
import { Collapse, ConfigProvider } from '@otakus/design';
import React from 'react';

/** Test usage. Do not use in your production. */
import type { CollapseProps } from '@otakus/design';

const text = `Ant Design! `.repeat(26);

const items: CollapseProps['items'] = [
  { key: '1', label: `This is panel header 1, (${text})`, children: text },
  { key: '2', label: `This is panel header 2, (${text})`, children: text },
  { key: '3', label: `This is panel header 3, (${text})`, children: text }
];

export default () => (
  <ConfigProvider
    theme={{
      components: {
        Collapse: {
          headerPadding: '0px 10px 20px 30px',
          headerBg: '#eaeeff',
          contentPadding: '0px 10px 20px 30px',
          contentBg: '#e6f7ff'
        }
      }
    }}
  >
    <Collapse items={items} />
  </ConfigProvider>
);

```

