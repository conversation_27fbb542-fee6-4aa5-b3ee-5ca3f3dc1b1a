# Image

## API

通用属性参考：[通用属性](/components/common-props)

### Image

| 参数                   | 说明                                        | 类型                                                     | 默认值        | 版本                                    |
| ---------------------- | ------------------------------------------- | -------------------------------------------------------- | ------------- | --------------------------------------- |
| alt                    | 图像描述                                    | string                                                   | -             | 4.6.0                                   |
| fallback               | 加载失败容错地址                            | string                                                   | -             | 4.6.0                                   |
| height                 | 图像高度                                    | string \| number                                         | -             | 4.6.0                                   |
| placeholder            | 加载占位，为 `true` 时使用默认占位          | ReactNode                                                | -             | 4.6.0                                   |
| preview                | 预览参数，为 `false` 时禁用                 | boolean \| [PreviewType](#previewtype)                   | true          | 4.6.0 [PreviewType](#previewyype):4.7.0 |
| src                    | 图片地址                                    | string                                                   | -             | 4.6.0                                   |
| width                  | 图像宽度                                    | string \| number                                         | -             | 4.6.0                                   |
| onError                | 加载错误回调                                | (event: Event) => void                                   | -             | 4.12.0                                  |
| enlargedWidth          | 放大图片宽度                                | number \| 'auto'                                         | 'auto'        | 0.9.0                                   |
| enlargedHeight         | 放大图片高度                                | number \| 'auto'                                         | 'auto'        | 0.9.0                                   |
| enlargedOffset         | 放大图片与原图之间的距离                    | number                                                   | 4             | 0.9.0                                   |
| enlargedPosition       | 放大图片位置                                | 'leftTop' \| 'leftBottom' \| 'rightTop' \| 'rightBottom' | 'rightBottom' | 0.9.0                                   |
| enlargedContainerStyle | 自定义放大图片容器样式                      | React.CSSProperties                                      | -             | 0.9.0                                   |
| enableEnlarged         | 是否启用放大功能，开启时，会禁用preview功能 | boolean                                                  | false         | 0.9.0                                   |

其他属性见 [&lt;img>](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#Attributes)

### PreviewType

| 参数            | 说明                                                         | 类型                                                                                                                                     | 默认值 | 版本   |
| --------------- | ------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------- | ------ | ------ |
| visible         | 是否显示                                                     | boolean                                                                                                                                  | -      | -      |
| src             | 自定义预览 src                                               | string                                                                                                                                   | -      | 4.10.0 |
| getContainer    | 指定预览挂载的节点，但依旧为全屏展示，false 为挂载在当前位置 | string \| HTMLElement \| (() => HTMLElement) \| false                                                                                    | -      | 4.8.0  |
| movable         | 是否可移动                                                   | boolean                                                                                                                                  | true   | 5.8.0  |
| mask            | 缩略图遮罩                                                   | ReactNode                                                                                                                                | -      | 4.9.0  |
| maskClassName   | 缩略图遮罩类名                                               | string                                                                                                                                   | -      | 4.11.0 |
| rootClassName   | 预览图的根 DOM 类名                                          | string                                                                                                                                   | -      | 5.4.0  |
| scaleStep       | `1 + scaleStep` 为缩放放大的每步倍数                         | number                                                                                                                                   | 0.5    | -      |
| minScale        | 最小缩放倍数                                                 | number                                                                                                                                   | 1      | 5.7.0  |
| maxScale        | 最大放大倍数                                                 | number                                                                                                                                   | 50     | 5.7.0  |
| closeIcon       | 自定义关闭 Icon                                              | React.ReactNode                                                                                                                          | -      | 5.7.0  |
| forceRender     | 强制渲染预览图                                               | boolean                                                                                                                                  | -      | -      |
| toolbarRender   | 自定义工具栏                                                 | (originalNode: React.ReactElement, info: Omit<[ToolbarRenderInfoType](#toolbarrenderinfotype), 'current' \| 'total'>) => React.ReactNode | -      | 5.7.0  |
| imageRender     | 自定义预览内容                                               | (originalNode: React.ReactElement, info: { transform: [TransformType](#transformtype) }) => React.ReactNode                              | -      | 5.7.0  |
| destroyOnClose  | 关闭预览时销毁子元素                                         | boolean                                                                                                                                  | false  |        |
| onTransform     | 预览图 transform 变化的回调                                  | { transform: [TransformType](#transformtype), action: [TransformAction](#transformaction) }                                              | -      | 5.7.0  |
| onVisibleChange | 当 `visible` 发生改变时的回调                                | (visible: boolean, prevVisible: boolean) => void                                                                                         | -      | -      |

## PreviewGroup

| 参数     | 说明                        | 类型                                                    | 默认值 | 版本                                              |
| -------- | --------------------------- | ------------------------------------------------------- | ------ | ------------------------------------------------- |
| preview  | 预览参数，为 `false` 时禁用 | boolean \| [PreviewGroupType](#previewgrouptype)        | true   | 4.6.0 [PreviewGroupType](#previewgrouptype):4.7.0 |
| items    | 预览数组                    | string[] \| { src: string, crossOrigin: string, ... }[] | -      | 5.7.0                                             |
| fallback | 加载失败容错地址            | string                                                  | -      | 5.7.0                                             |

### PreviewGroupType

| 参数            | 说明                                                         | 类型                                                                                                                         | 默认值 | 版本               |
| --------------- | ------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------- | ------ | ------------------ |
| visible         | 是否显示                                                     | boolean                                                                                                                      | -      | -                  |
| getContainer    | 指定预览挂载的节点，但依旧为全屏展示，false 为挂载在当前位置 | string \| HTMLElement \| (() => HTMLElement) \| false                                                                        | -      | 4.8.0              |
| movable         | 是否可移动                                                   | boolean                                                                                                                      | true   | 5.8.0              |
| current         | 当前预览图的 index                                           | number                                                                                                                       | -      | 4.12.0             |
| mask            | 缩略图遮罩                                                   | ReactNode                                                                                                                    | -      | 4.9.0              |
| maskClassName   | 缩略图遮罩类名                                               | string                                                                                                                       | -      | 4.11.0             |
| rootClassName   | 预览图的根 DOM 类名                                          | string                                                                                                                       | -      | 5.4.0              |
| scaleStep       | `1 + scaleStep` 为缩放放大的每步倍数                         | number                                                                                                                       | 0.5    | -                  |
| minScale        | 最小缩放倍数                                                 | number                                                                                                                       | 1      | 5.7.0              |
| maxScale        | 最大放大倍数                                                 | number                                                                                                                       | 50     | 5.7.0              |
| closeIcon       | 自定义关闭 Icon                                              | React.ReactNode                                                                                                              | -      | 5.7.0              |
| forceRender     | 强制渲染预览图                                               | boolean                                                                                                                      | -      | -                  |
| countRender     | 自定义预览计数内容                                           | (current: number, total: number) => React.ReactNode                                                                          | -      | 4.20.0             |
| toolbarRender   | 自定义工具栏                                                 | (originalNode: React.ReactElement, info: [ToolbarRenderInfoType](#toolbarrenderinfotype)) => React.ReactNode                 | -      | 5.7.0              |
| imageRender     | 自定义预览内容                                               | (originalNode: React.ReactElement, info: { transform: [TransformType](#transformtype), current: number }) => React.ReactNode | -      | 5.7.0              |
| onTransform     | 预览图 transform 变化的回调                                  | { transform: [TransformType](#transformtype), action: [TransformAction](#transformaction) }                                  | -      | 5.7.0              |
| onChange        | 切换预览图的回调                                             | (current: number, prevCurrent: number) => void                                                                               | -      | 5.3.0              |
| onVisibleChange | 当 `visible` 发生改变时的回调                                | (visible: boolean, prevVisible: boolean, current: number) => void                                                            | -      | current 参数 5.3.0 |

## Interface

### TransformType

```typescript
{
  x: number;
  y: number;
  rotate: number;
  scale: number;
  flipX: boolean;
  flipY: boolean;
}
```

### TransformAction

```typescript
type TransformAction =
  | 'flipY'
  | 'flipX'
  | 'rotateLeft'
  | 'rotateRight'
  | 'zoomIn'
  | 'zoomOut'
  | 'close'
  | 'prev'
  | 'next'
  | 'wheel'
  | 'doubleClick'
  | 'move'
  | 'dragRebound';
```

### ToolbarRenderInfoType

```typescript
{
  icons: {
    flipYIcon: React.ReactNode;
    flipXIcon: React.ReactNode;
    rotateLeftIcon: React.ReactNode;
    rotateRightIcon: React.ReactNode;
    zoomOutIcon: React.ReactNode;
    zoomInIcon: React.ReactNode;
  };
  actions: {
    onFlipY: () => void;
    onFlipX: () => void;
    onRotateLeft: () => void;
    onRotateRight: () => void;
    onZoomOut: () => void;
    onZoomIn: () => void;
  };
  transform: TransformType,
  current: number;
  total: number;
}
```

## 示例

### Basic Usage

```tsx
import React from 'react';
import { Image } from '@otakus/design';

const App: React.FC = () => (
  <Image
    width={200}
    src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
  />
);

export default App;

```

### enLarge image

```tsx
/**
 * title: 放大图片
 * description: 将鼠标悬停在图片上查看右侧放大效果，如果空间不足，会自动适配，开启时，会禁用preview功能
 */
import React, { useState } from 'react';
import { Radio, Typography, Image } from '@otakus/design';

const { Text } = Typography;

const Demo: React.FC = () => {
  const [enlargedPosition, setEnlargedPosition] = useState('rightBottom');
  const [fixedRight, setFixedRight] = useState(false);

  // 使用占位图片
  const imageSrc = 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png';

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 20 }}>
      <div>
        <Text strong>放大位置：</Text>
        <Radio.Group
          value={enlargedPosition}
          onChange={(e) => setEnlargedPosition(e.target.value)}
          className="ml-2"
        >
          <Radio.Button value="leftTop">左上</Radio.Button>
          <Radio.Button value="leftBottom">左下</Radio.Button>
          <Radio.Button value="rightTop">右上</Radio.Button>
          <Radio.Button value="rightBottom">右下</Radio.Button>
        </Radio.Group>
      </div>
      <div>
        <Text strong>图片位置：</Text>
        <Radio.Group
          value={fixedRight}
          onChange={(e) => setFixedRight(e.target.value)}
          className="ml-2"
        >
          <Radio.Button value={true}>图片移动到右侧</Radio.Button>
          <Radio.Button value={false}>图片移动到左侧</Radio.Button>
        </Radio.Group>
      </div>
      <div
        style={{
          marginTop: 20,
          display: 'flex',
          justifyContent: fixedRight ? 'flex-end' : 'flex-start'
        }}
      >
        <Image
          src={imageSrc}
          width={200}
          enableEnlarged
          enlargedWidth={400}
          enlargedPosition={
            enlargedPosition as 'leftTop' | 'leftBottom' | 'rightTop' | 'rightBottom'
          }
          alt="示例图片"
        />
      </div>
    </div>
  );
};

export default Demo;

```

### Fault tolerant

```tsx
import React from 'react';
import { Image } from '@otakus/design';

const App: React.FC = () => (
  <Image
    width={200}
    height={200}
    src="error"
    fallback="data:image/png;base64,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"
  />
);

export default App;

```

### Progressive Loading

```tsx
import React, { useState } from 'react';
import { Button, Image, Space } from '@otakus/design';

const App: React.FC = () => {
  const [random, setRandom] = useState<number>();

  return (
    <Space size={12}>
      <Image
        width={200}
        src={`https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png?${random}`}
        placeholder={
          <Image
            preview={false}
            src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png?x-oss-process=image/blur,r_50,s_50/quality,q_1/resize,m_mfit,h_200,w_200"
            width={200}
          />
        }
      />
      <Button
        type="primary"
        onClick={() => {
          setRandom(Date.now());
        }}
      >
        Reload
      </Button>
    </Space>
  );
};

export default App;

```

### Multiple image preview

```tsx
import React from 'react';
import { Image } from '@otakus/design';

const App: React.FC = () => (
  <Image.PreviewGroup
    preview={{
      onChange: (current, prev) => console.log(`current index: ${current}, prev index: ${prev}`)
    }}
  >
    <Image width={200} src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg" />
    <Image
      width={200}
      src="https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg"
    />
  </Image.PreviewGroup>
);

export default App;

```

### Preview from one image

```tsx
import React from 'react';
import { Image } from '@otakus/design';

const App: React.FC = () => (
  <Image.PreviewGroup
    items={[
      'https://gw.alipayobjects.com/zos/antfincdn/LlvErxo8H9/photo-1503185912284-5271ff81b9a8.webp',
      'https://gw.alipayobjects.com/zos/antfincdn/cV16ZqzMjW/photo-1473091540282-9b846e7965e3.webp',
      'https://gw.alipayobjects.com/zos/antfincdn/x43I27A55%26/photo-1438109491414-7198515b166b.webp'
    ]}
  >
    <Image
      width={200}
      src="https://gw.alipayobjects.com/zos/antfincdn/LlvErxo8H9/photo-1503185912284-5271ff81b9a8.webp"
    />
  </Image.PreviewGroup>
);

export default App;

```

### Custom preview image

```tsx
import React from 'react';
import { Image } from '@otakus/design';

const App: React.FC = () => (
  <Image
    width={200}
    src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png?x-oss-process=image/blur,r_50,s_50/quality,q_1/resize,m_mfit,h_200,w_200"
    preview={{
      src: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png'
    }}
  />
);

export default App;

```

### Controlled Preview

```tsx
import React, { useState } from 'react';
import { Button, InputNumber, Image } from '@otakus/design';

const App: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const [scaleStep, setScaleStep] = useState(0.5);

  return (
    <>
      <div>
        scaleStep:{' '}
        <InputNumber
          min={0.1}
          max={5}
          defaultValue={0.5}
          step={0.1}
          onChange={(val) => setScaleStep(val!)}
        />
      </div>
      <br />
      <Button type="primary" onClick={() => setVisible(true)}>
        show image preview
      </Button>
      <Image
        width={200}
        style={{ display: 'none' }}
        src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png?x-oss-process=image/blur,r_50,s_50/quality,q_1/resize,m_mfit,h_200,w_200"
        preview={{
          visible,
          scaleStep,
          src: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
          onVisibleChange: (value) => {
            setVisible(value);
          }
        }}
      />
    </>
  );
};

export default App;

```

### Custom toolbar render

```tsx
import {
  DownloadOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  SwapOutlined,
  ZoomInOutlined,
  ZoomOutOutlined
} from '@otakus/icons';
import React from 'react';
import { Image, Space } from '@otakus/design';

const src = 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png';

const App: React.FC = () => {
  // or you can download flipped and rotated image
  // https://codesandbox.io/s/zi-ding-yi-gong-ju-lan-@otakus/design-5-7-0-forked-c9jvmp
  const onDownload = () => {
    fetch(src)
      .then((response) => response.blob())
      .then((blob) => {
        const url = URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.download = 'image.png';
        document.body.appendChild(link);
        link.click();
        URL.revokeObjectURL(url);
        link.remove();
      });
  };

  return (
    <Image
      width={200}
      src={src}
      preview={{
        toolbarRender: (
          _,
          { actions: { onFlipY, onFlipX, onRotateLeft, onRotateRight, onZoomOut, onZoomIn } }
        ) => (
          <Space size={12} className="toolbar-wrapper">
            <DownloadOutlined onClick={onDownload} />
            <SwapOutlined rotate={90} onClick={onFlipY} />
            <SwapOutlined onClick={onFlipX} />
            <RotateLeftOutlined onClick={onRotateLeft} />
            <RotateRightOutlined onClick={onRotateRight} />
            <ZoomOutOutlined onClick={onZoomOut} />
            <ZoomInOutlined onClick={onZoomIn} />
          </Space>
        )
      }}
    />
  );
};

export default App;

```

### Custom preview render

```tsx
import React from 'react';
import { Image } from '@otakus/design';

const App: React.FC = () => (
  <Image
    width={200}
    preview={{
      imageRender: () => (
        <video
          muted
          width="100%"
          controls
          src="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*uYT7SZwhJnUAAAAAAAAAAAAADgCCAQ"
        />
      ),
      toolbarRender: () => null
    }}
    src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
  />
);

export default App;

```

### Custom preview mask

```tsx
import React from 'react';
import { ZoomInOutlined } from '@otakus/icons';
import { Image, Space } from '@otakus/design';

const App: React.FC = () => (
  <Image
    width={96}
    src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
    preview={{
      maskClassName: 'customize-mask',
      mask: (
        <Space direction="vertical" align="center">
          <ZoomInOutlined />
          示例
        </Space>
      )
    }}
  />
);

export default App;

```

### nested

```tsx
import React, { useState } from 'react';
import { Button, Divider, Image, Modal } from '@otakus/design';

const App: React.FC = () => {
  const [show1, setShow1] = useState(false);
  const [show2, setShow2] = useState(false);
  const [show3, setShow3] = useState(false);
  return (
    <>
      <Button
        onClick={() => {
          setShow1(true);
        }}
      >
        showModal
      </Button>
      <Modal
        open={show1}
        afterOpenChange={(open) => {
          setShow1(open);
        }}
        onCancel={() => {
          setShow1(false);
        }}
      >
        <Button
          onClick={() => {
            setShow2(true);
          }}
        >
          test2
        </Button>
        <Modal
          open={show2}
          afterOpenChange={(open) => {
            setShow2(open);
          }}
          onCancel={() => {
            setShow2(false);
          }}
        >
          <Button
            onClick={() => {
              setShow3(true);
            }}
          >
            test3
          </Button>
          <Modal
            open={show3}
            afterOpenChange={(open) => {
              setShow3(open);
            }}
            onCancel={() => {
              setShow3(false);
            }}
          >
            <Image
              width={200}
              src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
            />
            <Divider />
            <Image.PreviewGroup
              preview={{
                onChange: (current, prev) =>
                  console.log(`current index: ${current}, prev index: ${prev}`)
              }}
            >
              <Image
                width={200}
                src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
              />
              <Image
                width={200}
                src="https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg"
              />
            </Image.PreviewGroup>
          </Modal>
        </Modal>
      </Modal>
    </>
  );
};

export default App;

```

### Top progress customization when previewing multiple images

```tsx
import React from 'react';
import { Image } from '@otakus/design';

const App: React.FC = () => (
  <Image.PreviewGroup
    preview={{ countRender: (current, total) => `当前 ${current} / 总计 ${total}` }}
  >
    <Image width={150} src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg" />
    <Image
      width={150}
      src="https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg"
    />
    <Image
      width={150}
      src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
    />
  </Image.PreviewGroup>
);

export default App;

```

### Custom component token

```tsx
import React from 'react';
import { ConfigProvider, Image } from '@otakus/design';

const App: React.FC = () => (
  <ConfigProvider
    theme={{
      components: {
        Image: {
          previewOperationSize: 20,
          previewOperationColor: '#222',
          previewOperationColorDisabled: '#b20000'
        }
      }
    }}
  >
    <Image.PreviewGroup
      preview={{ countRender: (current, total) => `当前 ${current} / 总计 ${total}` }}
    >
      <Image
        width={150}
        src="https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg"
      />
      <Image
        width={150}
        src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
      />
    </Image.PreviewGroup>
  </ConfigProvider>
);

export default App;

```

