# Card

> 页面容器请使用[ModuleCard](/biz-components/card#modulecard)

## API

通用属性参考：[通用属性](/components/common-props)

### Card

| 参数                | 说明                                                | 类型                                          | 默认值             | 版本   |
| ------------------- | --------------------------------------------------- | --------------------------------------------- | ------------------ | ------ |
| actions             | 卡片操作组，位置在卡片底部                          | Array&lt;ReactNode>                           | -                  |        |
| activeTabKey        | 当前激活页签的 key                                  | string                                        | -                  |        |
| bordered            | 是否有边框                                          | boolean                                       | true               |        |
| cover               | 卡片封面                                            | ReactNode                                     | -                  |        |
| defaultActiveTabKey | 初始化选中页签的 key，如果没有设置 activeTabKey     | string                                        | `第一个页签的 key` |        |
| extra               | 卡片右上角的操作区域                                | ReactNode                                     | -                  |        |
| hoverable           | 鼠标移过时可浮起                                    | boolean                                       | false              |        |
| loading             | 当卡片内容还在加载中时，可以用 loading 展示一个占位 | boolean                                       | false              |        |
| size                | card 的尺寸                                         | `default` \| `small`                          | `default`          |        |
| tabBarExtraContent  | tab bar 上额外的元素                                | ReactNode                                     | -                  |        |
| tabList             | 页签标题列表                                        | [TabItemType](/components/tabs#tabitemtype)[] | -                  |        |
| tabProps            | [Tabs](/components/tabs#tabs)                       | -                                             | -                  |        |
| title               | 卡片标题                                            | ReactNode                                     | -                  |        |
| type                | 卡片类型，可设置为 `inner` 或 不设置                | string                                        | -                  |        |
| classNames          | 配置卡片内置模块的 className                        | Record<SemanticDOM, string>                   | -                  | 5.14.0 |
| styles              | 配置卡片内置模块的 style                            | Record<SemanticDOM, string>                   | -                  | 5.14.0 |
| onTabChange         | 页签切换的回调                                      | (key) => void                                 | -                  |        |

### Card.Grid

| 参数      | 说明                   | 类型          | 默认值 | 版本 |
| --------- | ---------------------- | ------------- | ------ | ---- |
| className | 网格容器类名           | string        | -      |      |
| hoverable | 鼠标移过时可浮起       | boolean       | true   |      |
| style     | 定义网格容器类名的样式 | CSSProperties | -      |      |

### Card.Meta

| 参数        | 说明               | 类型          | 默认值 | 版本 |
| ----------- | ------------------ | ------------- | ------ | ---- |
| avatar      | 头像/图标          | ReactNode     | -      |      |
| className   | 容器类名           | string        | -      |      |
| description | 描述内容           | ReactNode     | -      |      |
| style       | 定义容器类名的样式 | CSSProperties | -      |      |
| title       | 标题内容           | ReactNode     | -      |      |

### `styles` 和 `classNames` 属性

| 名称    | 说明                     | 版本   |
| ------- | ------------------------ | ------ |
| header  | 设置卡片头部区域         | 5.14.0 |
| body    | 设置卡片内容区域         | 5.14.0 |
| extra   | 设置卡片右上角的操作区域 | 5.14.0 |
| title   | 设置卡片标题             | 5.14.0 |
| actions | 设置卡片底部操作组       | 5.14.0 |
| cover   | 设置标题封面             | 5.14.0 |

## 示例

### Basic card

```tsx
import React from 'react';
import { Card, Space } from '@otakus/design';

const App: React.FC = () => (
  <Space direction="vertical" size={16}>
    <Card title="Default size card" extra={<a href="#">More</a>} style={{ width: 300 }}>
      <p>Card content</p>
      <p>Card content</p>
      <p>Card content</p>
    </Card>
    <Card size="small" title="Small size card" extra={<a href="#">More</a>} style={{ width: 300 }}>
      <p>Card content</p>
      <p>Card content</p>
      <p>Card content</p>
    </Card>
  </Space>
);

export default App;

```

### No border

```tsx
import React from 'react';
import { Card } from '@otakus/design';

const App: React.FC = () => (
  <Card title="Card title" bordered={false} style={{ width: 300 }}>
    <p>Card content</p>
    <p>Card content</p>
    <p>Card content</p>
  </Card>
);

export default App;

```

### Simple card

```tsx
import React from 'react';
import { Card } from '@otakus/design';

const App: React.FC = () => (
  <Card style={{ width: 300 }}>
    <p>Card content</p>
    <p>Card content</p>
    <p>Card content</p>
  </Card>
);

export default App;

```

### Customized content

```tsx
import React from 'react';
import { Card } from '@otakus/design';

const { Meta } = Card;

const App: React.FC = () => (
  <Card
    hoverable
    style={{ width: 240 }}
    cover={<img alt="example" src="https://os.alipayobjects.com/rmsportal/QBnOOoLaAfKPirc.png" />}
  >
    <Meta title="Europe Street beat" description="www.instagram.com" />
  </Card>
);

export default App;

```

### Card in column

```tsx
import React from 'react';
import { Card, Col, Row } from '@otakus/design';

const App: React.FC = () => (
  <Row gutter={16}>
    <Col span={8}>
      <Card title="Card title" bordered={false}>
        Card content
      </Card>
    </Col>
    <Col span={8}>
      <Card title="Card title" bordered={false}>
        Card content
      </Card>
    </Col>
    <Col span={8}>
      <Card title="Card title" bordered={false}>
        Card content
      </Card>
    </Col>
  </Row>
);

export default App;

```

### Loading card

```tsx
import { EditOutlined, EllipsisOutlined, SettingOutlined } from '@otakus/icons';
import React, { useState } from 'react';
import { Avatar, Card, Skeleton, Switch } from '@otakus/design';

const { Meta } = Card;

const App: React.FC = () => {
  const [loading, setLoading] = useState(true);

  const onChange = (checked: boolean) => {
    setLoading(!checked);
  };

  return (
    <>
      <Switch checked={!loading} onChange={onChange} />
      <Card style={{ width: 300, marginTop: 16 }} loading={loading}>
        <Meta
          avatar={<Avatar src="https://api.dicebear.com/7.x/miniavs/svg?seed=1" />}
          title="Card title"
          description="This is the description"
        />
      </Card>
      <Card
        style={{ width: 300, marginTop: 16 }}
        actions={[
          <SettingOutlined key="setting" />,
          <EditOutlined key="edit" />,
          <EllipsisOutlined key="ellipsis" />
        ]}
      >
        <Skeleton loading={loading} avatar active>
          <Meta
            avatar={<Avatar src="https://api.dicebear.com/7.x/miniavs/svg?seed=2" />}
            title="Card title"
            description="This is the description"
          />
        </Skeleton>
      </Card>
    </>
  );
};

export default App;

```

### Grid card

```tsx
import React from 'react';
import { Card } from '@otakus/design';

const gridStyle: React.CSSProperties = {
  width: '25%',
  textAlign: 'center'
};

const App: React.FC = () => (
  <Card title="Card Title">
    <Card.Grid style={gridStyle}>Content</Card.Grid>
    <Card.Grid hoverable={false} style={gridStyle}>
      Content
    </Card.Grid>
    <Card.Grid style={gridStyle}>Content</Card.Grid>
    <Card.Grid style={gridStyle}>Content</Card.Grid>
    <Card.Grid style={gridStyle}>Content</Card.Grid>
    <Card.Grid style={gridStyle}>Content</Card.Grid>
    <Card.Grid style={gridStyle}>Content</Card.Grid>
  </Card>
);

export default App;

```

### Inner card

```tsx
import React from 'react';
import { Card } from '@otakus/design';

const App: React.FC = () => (
  <Card title="Card title">
    <Card type="inner" title="Inner Card title" extra={<a href="#">More</a>}>
      Inner Card content
    </Card>
    <Card
      style={{ marginTop: 16 }}
      type="inner"
      title="Inner Card title"
      extra={<a href="#">More</a>}
    >
      Inner Card content
    </Card>
  </Card>
);

export default App;

```

### With tabs

```tsx
import React, { useState } from 'react';
import { Card } from '@otakus/design';

const tabList = [
  {
    key: 'tab1',
    tab: 'tab1'
  },
  {
    key: 'tab2',
    tab: 'tab2'
  }
];

const contentList: Record<string, React.ReactNode> = {
  tab1: <p>content1</p>,
  tab2: <p>content2</p>
};

const tabListNoTitle = [
  {
    key: 'article',
    label: 'article'
  },
  {
    key: 'app',
    label: 'app'
  },
  {
    key: 'project',
    label: 'project'
  }
];

const contentListNoTitle: Record<string, React.ReactNode> = {
  article: <p>article content</p>,
  app: <p>app content</p>,
  project: <p>project content</p>
};

const App: React.FC = () => {
  const [activeTabKey1, setActiveTabKey1] = useState<string>('tab1');
  const [activeTabKey2, setActiveTabKey2] = useState<string>('app');

  const onTab1Change = (key: string) => {
    setActiveTabKey1(key);
  };
  const onTab2Change = (key: string) => {
    setActiveTabKey2(key);
  };

  return (
    <>
      <Card
        style={{ width: '100%' }}
        title="Card title"
        extra={<a href="#">More</a>}
        tabList={tabList}
        activeTabKey={activeTabKey1}
        onTabChange={onTab1Change}
      >
        {contentList[activeTabKey1]}
      </Card>
      <br />
      <br />
      <Card
        style={{ width: '100%' }}
        tabList={tabListNoTitle}
        activeTabKey={activeTabKey2}
        tabBarExtraContent={<a href="#">More</a>}
        onTabChange={onTab2Change}
        tabProps={{
          size: 'middle'
        }}
      >
        {contentListNoTitle[activeTabKey2]}
      </Card>
    </>
  );
};

export default App;

```

### Support more content configuration

```tsx
import React from 'react';
import { EditOutlined, EllipsisOutlined, SettingOutlined } from '@otakus/icons';
import { Avatar, Card } from '@otakus/design';

const { Meta } = Card;

const App: React.FC = () => (
  <Card
    style={{ width: 300 }}
    cover={
      <img
        alt="example"
        src="https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png"
      />
    }
    actions={[
      <SettingOutlined key="setting" />,
      <EditOutlined key="edit" />,
      <EllipsisOutlined key="ellipsis" />
    ]}
  >
    <Meta
      avatar={<Avatar src="https://api.dicebear.com/7.x/miniavs/svg?seed=8" />}
      title="Card title"
      description="This is the description"
    />
  </Card>
);

export default App;

```

### Component Token

```tsx
import { EditOutlined, EllipsisOutlined, SettingOutlined } from '@otakus/icons';
import { Card, ConfigProvider } from '@otakus/design';
import React from 'react';

export default () => (
  <ConfigProvider
    theme={{
      components: {
        Card: {
          headerBg: '#e6f4ff',
          headerFontSize: 20,
          headerFontSizeSM: 20,
          headerHeight: 60,
          headerHeightSM: 60,
          actionsBg: '#e6f4ff',
          actionsLiMargin: `2px 0`,
          tabsMarginBottom: 0,
          extraColor: 'rgba(0,0,0,0.25)'
        }
      }
    }}
  >
    <Card
      title="Card title"
      actions={[
        <SettingOutlined key="setting" />,
        <EditOutlined key="edit" />,
        <EllipsisOutlined key="ellipsis" />
      ]}
      extra="More"
      tabList={[
        {
          key: 'tab1',
          label: 'tab1'
        },
        {
          key: 'tab2',
          label: 'tab2'
        }
      ]}
    >
      <p>Card content</p>
      <p>Card content</p>
      <p>Card content</p>
    </Card>
    <Card size="small" title="Small size card" extra={<a href="#">More</a>} style={{ width: 300 }}>
      <p>Card content</p>
      <p>Card content</p>
      <p>Card content</p>
    </Card>
  </ConfigProvider>
);

```

