# Notification

## API

通用属性参考：[通用属性](/components/common-props)

- `notification.success(config)`
- `notification.error(config)`
- `notification.info(config)`
- `notification.warning(config)`
- `notification.open(config)`
- `notification.destroy(key?: String)`

config 参数如下：

| 参数        | 说明                                                                                                                                                                                                                                                                               | 类型                                                                                                                                          | 默认值     | 版本                                       |
| ----------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------- | ---------- | ------------------------------------------ |
| btn         | 自定义关闭按钮                                                                                                                                                                                                                                                                     | ReactNode                                                                                                                                     | -          | -                                          |
| className   | 自定义 CSS class                                                                                                                                                                                                                                                                   | string                                                                                                                                        | -          | -                                          |
| closeIcon   | 自定义关闭图标                                                                                                                                                                                                                                                                     | ReactNode                                                                                                                                     | true       | 5.7.0：设置为 null 或 false 时隐藏关闭按钮 |
| description | 通知提醒内容，必选                                                                                                                                                                                                                                                                 | ReactNode                                                                                                                                     | -          | -                                          |
| duration    | 默认 4.5 秒后自动关闭，配置为 null 则不自动关闭                                                                                                                                                                                                                                    | number                                                                                                                                        | 4.5        | -                                          |
| icon        | 自定义图标                                                                                                                                                                                                                                                                         | ReactNode                                                                                                                                     | -          | -                                          |
| key         | 当前通知唯一标志                                                                                                                                                                                                                                                                   | string                                                                                                                                        | -          | -                                          |
| message     | 通知提醒标题，必选                                                                                                                                                                                                                                                                 | ReactNode                                                                                                                                     | -          | -                                          |
| placement   | 弹出位置，可选 `top` `topLeft` `topRight` `bottom` `bottomLeft` `bottomRight`                                                                                                                                                                                                      | string                                                                                                                                        | `topRight` | -                                          |
| style       | 自定义内联样式                                                                                                                                                                                                                                                                     | [CSSProperties](https://github.com/DefinitelyTyped/DefinitelyTyped/blob/e434515761b36830c3e58a970abf5186f005adac/types/react/index.d.ts#L794) | -          | -                                          |
| role        | 供屏幕阅读器识别的通知内容语义，默认为 `alert`。此情况下屏幕阅读器会立即打断当前正在阅读的其他内容，转而阅读通知内容                                                                                                                                                               | `alert \| status`                                                                                                                             | `alert`    | 5.6.0                                      |
| onClick     | 点击通知时触发的回调函数                                                                                                                                                                                                                                                           | function                                                                                                                                      | -          | -                                          |
| onClose     | 当通知关闭时触发                                                                                                                                                                                                                                                                   | function                                                                                                                                      | -          | -                                          |
| props       | 透传至通知 `div` 上的 props 对象，支持传入 `data-*` `aria-*` 或 `role` 作为对象的属性。需要注意的是，虽然在 TypeScript 类型中声明的类型支持传入 `data-*` 作为对象的属性，但目前只允许传入 `data-testid` 作为对象的属性。 详见 https://github.com/microsoft/TypeScript/issues/28960 | Object                                                                                                                                        | -          | -                                          |

- `notification.useNotification(config)`

config 参数如下：

| 参数         | 说明                                                                          | 类型                               | 默认值              | 版本                                       |
| ------------ | ----------------------------------------------------------------------------- | ---------------------------------- | ------------------- | ------------------------------------------ |
| bottom       | 消息从底部弹出时，距离底部的位置，单位像素                                    | number                             | 24                  |                                            |
| closeIcon    | 自定义关闭图标                                                                | ReactNode                          | true                | 5.7.0：设置为 null 或 false 时隐藏关闭按钮 |
| getContainer | 配置渲染节点的输出位置                                                        | () => HTMLNode                     | () => document.body |                                            |
| placement    | 弹出位置，可选 `top` `topLeft` `topRight` `bottom` `bottomLeft` `bottomRight` | string                             | `topRight`          |                                            |
| rtl          | 是否开启 RTL 模式                                                             | boolean                            | false               |                                            |
| stack        | 堆叠模式，超过阈值时会将所有消息收起                                          | boolean \| `{ threshold: number }` | `{ threshold: 3 }`  | 5.10.0                                     |
| top          | 消息从顶部弹出时，距离顶部的位置，单位像素                                    | number                             | 24                  |                                            |
| maxCount     | 最大显示数，超过限制时，最早的消息会被自动关闭                                | number                             | -                   | 4.17.0                                     |

### 全局配置

还提供了一个全局配置方法，在调用前提前配置，全局一次生效。

`notification.config(options)`

> 当你使用 `ConfigProvider` 进行全局化配置时，系统会默认自动开启 RTL 模式。(4.3.0+)
>
> 当你想单独使用，可通过如下设置开启 RTL 模式。

```js
notification.config({
  placement: 'bottomRight',
  bottom: 50,
  duration: 3,
  rtl: true
});
```

#### notification.config

| 参数         | 说明                                                                          | 类型           | 默认值              | 版本                                       |
| ------------ | ----------------------------------------------------------------------------- | -------------- | ------------------- | ------------------------------------------ |
| bottom       | 消息从底部弹出时，距离底部的位置，单位像素                                    | number         | 24                  |                                            |
| closeIcon    | 自定义关闭图标                                                                | ReactNode      | true                | 5.7.0：设置为 null 或 false 时隐藏关闭按钮 |
| duration     | 默认自动关闭延时，单位秒                                                      | number         | 4.5                 |                                            |
| getContainer | 配置渲染节点的输出位置，但依旧为全屏展示                                      | () => HTMLNode | () => document.body |                                            |
| placement    | 弹出位置，可选 `top` `topLeft` `topRight` `bottom` `bottomLeft` `bottomRight` | string         | `topRight`          |                                            |
| rtl          | 是否开启 RTL 模式                                                             | boolean        | false               |                                            |
| top          | 消息从顶部弹出时，距离顶部的位置，单位像素                                    | number         | 24                  |                                            |
| maxCount     | 最大显示数，超过限制时，最早的消息会被自动关闭                                | number         | -                   | 4.17.0                                     |

## 示例

### Hooks usage (recommended)

```tsx
import React, { useMemo } from 'react';
import { Button, Divider, notification, Space } from '@otakus/design';
import type { NotificationArgsProps } from '@otakus/design';

type NotificationPlacement = NotificationArgsProps['placement'];

const Context = React.createContext({ name: 'Default' });

const App: React.FC = () => {
  const [api, contextHolder] = notification.useNotification();

  const openNotification = (placement: NotificationPlacement) => {
    api.info({
      message: `Notification ${placement}`,
      description: <Context.Consumer>{({ name }) => `Hello, ${name}!`}</Context.Consumer>,
      placement
    });
  };

  const contextValue = useMemo(() => ({ name: 'Ant Design' }), []);

  return (
    <Context.Provider value={contextValue}>
      {contextHolder}
      <Space>
        <Button type="primary" onClick={() => openNotification('topLeft')}>
          topLeft
        </Button>
        <Button type="primary" onClick={() => openNotification('topRight')}>
          topRight
        </Button>
      </Space>
      <Divider />
      <Space>
        <Button type="primary" onClick={() => openNotification('bottomLeft')}>
          bottomLeft
        </Button>
        <Button type="primary" onClick={() => openNotification('bottomRight')}>
          bottomRight
        </Button>
      </Space>
    </Context.Provider>
  );
};

export default App;

```

### Duration after which the notification box is closed

```tsx
import React from 'react';
import { Button, notification } from '@otakus/design';

const App: React.FC = () => {
  const [api, contextHolder] = notification.useNotification();

  const openNotification = () => {
    api.open({
      message: 'Notification Title',
      description:
        'I will never close automatically. This is a purposely very very long description that has many many characters and words.',
      duration: 0
    });
  };

  return (
    <>
      {contextHolder}
      <Button type="primary" onClick={openNotification}>
        Open the notification box
      </Button>
    </>
  );
};

export default App;

```

### Notification with icon

```tsx
import React from 'react';
import { Button, notification, Space } from '@otakus/design';

type NotificationType = 'success' | 'info' | 'warning' | 'error';

const App: React.FC = () => {
  const [api, contextHolder] = notification.useNotification();

  const openNotificationWithIcon = (type: NotificationType) => {
    api[type]({
      message: 'Notification Title',
      description:
        'This is the content of the notification. This is the content of the notification. This is the content of the notification.'
    });
  };

  return (
    <>
      {contextHolder}
      <Space>
        <Button onClick={() => openNotificationWithIcon('success')}>Success</Button>
        <Button onClick={() => openNotificationWithIcon('info')}>Info</Button>
        <Button onClick={() => openNotificationWithIcon('warning')}>Warning</Button>
        <Button onClick={() => openNotificationWithIcon('error')}>Error</Button>
      </Space>
    </>
  );
};

export default App;

```

### Custom close button

```tsx
import React from 'react';
import { Button, notification, Space } from '@otakus/design';

const close = () => {
  console.log(
    'Notification was closed. Either the close button was clicked or duration time elapsed.'
  );
};

const App: React.FC = () => {
  const [api, contextHolder] = notification.useNotification();

  const openNotification = () => {
    const key = `open${Date.now()}`;
    const btn = (
      <Space>
        <Button type="link" size="small" onClick={() => api.destroy()}>
          Destroy All
        </Button>
        <Button type="primary" size="small" onClick={() => api.destroy(key)}>
          Confirm
        </Button>
      </Space>
    );
    api.open({
      message: 'Notification Title',
      description:
        'A function will be be called after the notification is closed (automatically after the "duration" time of manually).',
      btn,
      key,
      onClose: close
    });
  };

  return (
    <>
      {contextHolder}
      <Button type="primary" onClick={openNotification}>
        Open the notification box
      </Button>
    </>
  );
};

export default App;

```

### Customized Icon

```tsx
import React from 'react';
import { InfoCircleOutlined } from '@otakus/icons';
import { Button, notification } from '@otakus/design';

const App: React.FC = () => {
  const [api, contextHolder] = notification.useNotification();

  const openNotification = () => {
    api.open({
      message: 'Notification Title',
      description:
        'This is the content of the notification. This is the content of the notification. This is the content of the notification.',
      icon: <InfoCircleOutlined style={{ color: '#108ee9' }} />
    });
  };

  return (
    <>
      {contextHolder}
      <Button type="primary" onClick={openNotification}>
        Open the notification box
      </Button>
    </>
  );
};

export default App;

```

### Placement

```tsx
import React from 'react';
import { Button, Divider, notification, Space } from '@otakus/design';
import type { NotificationArgsProps } from '@otakus/design';

type NotificationPlacement = NotificationArgsProps['placement'];

const App: React.FC = () => {
  const [api, contextHolder] = notification.useNotification();

  const openNotification = (placement: NotificationPlacement) => {
    api.info({
      message: `Notification ${placement}`,
      description:
        'This is the content of the notification. This is the content of the notification. This is the content of the notification.',
      placement
    });
  };

  return (
    <>
      {contextHolder}
      <Space>
        <Button type="primary" onClick={() => openNotification('top')}>
          top
        </Button>
        <Button type="primary" onClick={() => openNotification('bottom')}>
          bottom
        </Button>
      </Space>
      <Divider />
      <Space>
        <Button type="primary" onClick={() => openNotification('topLeft')}>
          topLeft
        </Button>
        <Button type="primary" onClick={() => openNotification('topRight')}>
          topRight
        </Button>
      </Space>
      <Divider />
      <Space>
        <Button type="primary" onClick={() => openNotification('bottomLeft')}>
          bottomLeft
        </Button>
        <Button type="primary" onClick={() => openNotification('bottomRight')}>
          bottomRight
        </Button>
      </Space>
    </>
  );
};

export default App;

```

### Customized style

```tsx
import React from 'react';
import { Button, notification } from '@otakus/design';

const App: React.FC = () => {
  const [api, contextHolder] = notification.useNotification();

  const openNotification = () => {
    api.open({
      message: 'Notification Title',
      description:
        'This is the content of the notification. This is the content of the notification. This is the content of the notification.',
      className: 'custom-class',
      style: {
        width: 600
      }
    });
  };

  return (
    <>
      {contextHolder}
      <Button type="primary" onClick={openNotification}>
        Open the notification box
      </Button>
    </>
  );
};
export default App;

```

### Update Message Content

```tsx
import React from 'react';
import { Button, notification } from '@otakus/design';

const key = 'updatable';

const App: React.FC = () => {
  const [api, contextHolder] = notification.useNotification();
  const openNotification = () => {
    api.open({
      key,
      message: 'Notification Title',
      description: 'description.'
    });

    setTimeout(() => {
      api.open({
        key,
        message: 'New Title',
        description: 'New description.'
      });
    }, 1000);
  };

  return (
    <>
      {contextHolder}
      <Button type="primary" onClick={openNotification}>
        Open the notification box
      </Button>
    </>
  );
};

export default App;

```

### Stack

```tsx
import React, { useMemo } from 'react';
import { Button, Divider, InputNumber, notification, Space, Switch } from '@otakus/design';

const Context = React.createContext({ name: 'Default' });

const App: React.FC = () => {
  const [enabled, setEnabled] = React.useState(true);
  const [threshold, setThreshold] = React.useState(3);
  const [api, contextHolder] = notification.useNotification({
    stack: enabled
      ? {
          threshold
        }
      : false
  });

  const openNotification = () => {
    api.open({
      message: 'Notification Title',
      description: `${Array(Math.round(Math.random() * 5) + 1)
        .fill('This is the content of the notification.')
        .join('\n')}`,
      duration: null
    });
  };

  const contextValue = useMemo(() => ({ name: 'Ant Design' }), []);

  return (
    <Context.Provider value={contextValue}>
      {contextHolder}
      <div>
        <Space size="large">
          <Space style={{ width: '100%' }}>
            <span>Enabled: </span>
            <Switch checked={enabled} onChange={(v) => setEnabled(v)} />
          </Space>
          <Space style={{ width: '100%' }}>
            <span>Threshold: </span>
            <InputNumber
              disabled={!enabled}
              value={threshold}
              step={1}
              min={1}
              max={10}
              onChange={(v) => setThreshold(v || 0)}
            />
          </Space>
        </Space>
        <Divider />
        <Button type="primary" onClick={openNotification}>
          Open the notification box
        </Button>
      </div>
    </Context.Provider>
  );
};

export default App;

```

### Static Method (deprecated)

```tsx
import React from 'react';
import { Button, notification } from '@otakus/design';

const openNotification = () => {
  notification.open({
    message: 'Notification Title',
    description:
      'This is the content of the notification. This is the content of the notification. This is the content of the notification.',
    onClick: () => {
      console.log('Notification Clicked!');
    }
  });
};
const App: React.FC = () => (
  <Button type="primary" onClick={openNotification}>
    Open the notification box
  </Button>
);

export default App;

```

### \_InternalPanelDoNotUseOrYouWillBeFired

```tsx
import { Button, notification } from '@otakus/design';

/** Test usage. Do not use in your production. */
const { _InternalPanelDoNotUseOrYouWillBeFired: InternalPanel } = notification;

export default () => (
  <InternalPanel
    message="Hello World!"
    description="Hello World?"
    type="success"
    btn={
      <Button type="primary" size="small">
        My Button
      </Button>
    }
  />
);

```

