# App

## API

通用属性参考：[通用属性](/components/common-props)

> 自 `antd@5.1.0` 版本开始提供该组件。

### App

| 参数         | 说明                                       | 类型                                                              | 默认值 | 版本   |
| ------------ | ------------------------------------------ | ----------------------------------------------------------------- | ------ | ------ |
| component    | 设置渲染元素，为 `false` 则不创建 DOM 节点 | ComponentType \| false                                            | div    | 5.11.0 |
| message      | App 内 Message 的全局配置                  | [MessageConfig](/components/message#messageconfig)                | -      | 5.3.0  |
| notification | App 内 Notification 的全局配置             | [NotificationConfig](/components/notification#notificationconfig) | -      | 5.3.0  |

## 示例

### Basic

```tsx
import { App, Button, Space } from '@otakus/design';
import React from 'react';

// Sub page
const MyPage = () => {
  const { message, modal, notification } = App.useApp();

  const showMessage = () => {
    message.success('Success!');
  };

  const showModal = () => {
    modal.warning({
      title: 'This is a warning message',
      content: 'some messages...some messages...'
    });
  };

  const showNotification = () => {
    notification.info({
      message: `Notification topLeft`,
      description: 'Hello, Ant Design!!',
      placement: 'topLeft'
    });
  };

  return (
    <Space>
      <Button type="primary" onClick={showMessage}>
        Open message
      </Button>
      <Button type="primary" onClick={showModal}>
        Open modal
      </Button>
      <Button type="primary" onClick={showNotification}>
        Open notification
      </Button>
    </Space>
  );
};

// Entry component
export default () => (
  <App>
    <MyPage />
  </App>
);

```

### Hooks config

```tsx
import { App, Button, Space } from '@otakus/design';
import React from 'react';

// Sub page
const MyPage = () => {
  const { message, notification } = App.useApp();

  const showMessage = () => {
    message.success('Success!');
  };

  const showNotification = () => {
    notification.info({
      message: `Notification`,
      description: 'Hello, Ant Design!!'
    });
  };

  return (
    <Space>
      <Button type="primary" onClick={showMessage}>
        Message for only one
      </Button>
      <Button type="primary" onClick={showNotification}>
        Notification for bottomLeft
      </Button>
    </Space>
  );
};

// Entry component
export default () => (
  <App message={{ maxCount: 1 }} notification={{ placement: 'bottomLeft' }}>
    <MyPage />
  </App>
);

```

