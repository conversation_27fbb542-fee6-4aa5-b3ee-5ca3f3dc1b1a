# Autocomplete

## API

通用属性参考：[通用属性](/components/common-props)

| 参数                        | 说明                                                                                                                                            | 类型                                                                               | 默认值              | 版本                |
| --------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------- | ------------------- | ------------------- |
| allowClear                  | 支持清除                                                                                                                                        | boolean \| { clearIcon?: ReactNode }                                               | false               | 5.8.0: 支持对象形式 |
| autoFocus                   | 自动获取焦点                                                                                                                                    | boolean                                                                            | false               |                     |
| backfill                    | 使用键盘选择选项的时候把选中项回填到输入框中                                                                                                    | boolean                                                                            | false               |                     |
| children (自动完成的数据源) | 自动完成的数据源                                                                                                                                | React.ReactElement&lt;OptionProps> \| Array&lt;React.ReactElement&lt;OptionProps>> | -                   |                     |
| children (自定义输入框)     | 自定义输入框                                                                                                                                    | HTMLInputElement \| HTMLTextAreaElement \| React.ReactElement&lt;InputProps>       | &lt;Input />        |                     |
| defaultActiveFirstOption    | 是否默认高亮第一个选项                                                                                                                          | boolean                                                                            | true                |                     |
| defaultOpen                 | 是否默认展开下拉菜单                                                                                                                            | boolean                                                                            | -                   |                     |
| defaultValue                | 指定默认选中的条目                                                                                                                              | string                                                                             | -                   |                     |
| disabled                    | 是否禁用                                                                                                                                        | boolean                                                                            | false               |                     |
| dropdownRender              | 自定义下拉框内容                                                                                                                                | (menus: ReactNode) => ReactNode                                                    | -                   | 4.24.0              |
| popupClassName              | 下拉菜单的 className 属性                                                                                                                       | string                                                                             | -                   | 4.23.0              |
| dropdownMatchSelectWidth    | 下拉菜单和选择器同宽。默认将设置 `min-width`，当值小于选择框宽度时会被忽略。false 时会关闭虚拟滚动                                              | boolean \| number                                                                  | true                |                     |
| filterOption                | 是否根据输入项进行筛选。当其为一个函数时，会接收 `inputValue` `option` 两个参数，当 `option` 符合筛选条件时，应返回 true，反之则返回 false      | boolean \| function(inputValue, option)                                            | true                |                     |
| getPopupContainer           | 菜单渲染父节点。默认渲染到 body 上，如果你遇到菜单滚动定位问题，试试修改为滚动的区域，并相对其定位。[示例](https://codesandbox.io/s/4j168r7jw0) | function(triggerNode)                                                              | () => document.body |                     |
| notFoundContent             | 当下拉列表为空时显示的内容                                                                                                                      | ReactNode                                                                          | -                   |                     |
| open                        | 是否展开下拉菜单                                                                                                                                | boolean                                                                            | -                   |                     |
| options                     | 数据化配置选项内容，相比 jsx 定义会获得更好的渲染性能                                                                                           | { label, value }\[]                                                                | -                   |                     |
| placeholder                 | 输入框提示                                                                                                                                      | string                                                                             | -                   |                     |
| status                      | 设置校验状态                                                                                                                                    | 'error' \| 'warning'                                                               | -                   | 4.19.0              |
| value                       | 指定当前选中的条目                                                                                                                              | string                                                                             | -                   |                     |
| variant                     | 形态变体                                                                                                                                        | `outlined` \| `borderless` \| `filled`                                             | `outlined`          | 5.13.0              |
| onBlur                      | 失去焦点时的回调                                                                                                                                | function()                                                                         | -                   |                     |
| onChange                    | 选中 option，或 input 的 value 变化时，调用此函数                                                                                               | function(value)                                                                    | -                   |                     |
| onDropdownVisibleChange     | 展开下拉菜单的回调                                                                                                                              | function(open)                                                                     | -                   |                     |
| onFocus                     | 获得焦点时的回调                                                                                                                                | function()                                                                         | -                   |                     |
| onSearch                    | 搜索补全项的时候调用                                                                                                                            | function(value)                                                                    | -                   |                     |
| onSelect                    | 被选中时调用，参数为选中项的 value 值                                                                                                           | function(value, option)                                                            | -                   |                     |
| onClear                     | 清除内容时的回调                                                                                                                                | function                                                                           | -                   | 4.6.0               |

## 示例

### Basic Usage

```tsx
import React, { useState } from 'react';
import { AutoComplete } from '@otakus/design';

const mockVal = (str: string, repeat = 1) => ({
  value: str.repeat(repeat)
});

const App: React.FC = () => {
  const [value, setValue] = useState('');
  const [options, setOptions] = useState<{ value: string }[]>([]);
  const [anotherOptions, setAnotherOptions] = useState<{ value: string }[]>([]);

  const getPanelValue = (searchText: string) =>
    !searchText ? [] : [mockVal(searchText), mockVal(searchText, 2), mockVal(searchText, 3)];

  const onSelect = (data: string) => {
    console.log('onSelect', data);
  };

  const onChange = (data: string) => {
    setValue(data);
  };

  return (
    <>
      <AutoComplete
        options={options}
        style={{ width: 200 }}
        onSelect={onSelect}
        onSearch={(text) => setOptions(getPanelValue(text))}
        placeholder="input here"
      />
      <br />
      <br />
      <AutoComplete
        value={value}
        options={anotherOptions}
        style={{ width: 200 }}
        onSelect={onSelect}
        onSearch={(text) => setAnotherOptions(getPanelValue(text))}
        onChange={onChange}
        placeholder="control mode"
      />
    </>
  );
};

export default App;

```

### Customized

```tsx
import React from 'react';
import { AutoComplete } from '@otakus/design';

const App: React.FC = () => {
  const [options, setOptions] = React.useState([]);
  const handleSearch = (value: string) => {
    setOptions(() => {
      if (!value || value.includes('@')) {
        return [];
      }
      return ['gmail.com', '163.com', 'qq.com'].map((domain) => ({
        label: `${value}@${domain}`,
        value: `${value}@${domain}`
      }));
    });
  };
  return (
    <AutoComplete
      style={{ width: 200 }}
      onSearch={handleSearch}
      placeholder="input here"
      options={options}
    />
  );
};

export default App;

```

### Customize Input Component

```tsx
import React, { useState } from 'react';
import { AutoComplete, Input } from '@otakus/design';

const { TextArea } = Input;

const App: React.FC = () => {
  const [options, setOptions] = useState<{ value: string }[]>([]);

  const handleSearch = (value: string) => {
    setOptions(
      !value ? [] : [{ value }, { value: value + value }, { value: value + value + value }]
    );
  };

  const handleKeyPress = (ev: React.KeyboardEvent<HTMLTextAreaElement>) => {
    console.log('handleKeyPress', ev);
  };

  const onSelect = (value: string) => {
    console.log('onSelect', value);
  };

  return (
    <AutoComplete
      options={options}
      style={{ width: 200 }}
      onSelect={onSelect}
      onSearch={handleSearch}
    >
      <TextArea
        placeholder="input here"
        className="custom"
        style={{ height: 50 }}
        onKeyPress={handleKeyPress}
      />
    </AutoComplete>
  );
};

export default App;

```

### Non-case-sensitive AutoComplete

```tsx
import React from 'react';
import { AutoComplete } from '@otakus/design';

const options = [
  { value: 'Burns Bay Road' },
  { value: 'Downing Street' },
  { value: 'Wall Street' }
];

const App: React.FC = () => (
  <AutoComplete
    style={{ width: 200 }}
    options={options}
    placeholder="try to type `b`"
    filterOption={(inputValue, option) =>
      option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
    }
  />
);

export default App;

```

### Lookup-Patterns - Certain Category

```tsx
import React from 'react';
import { UserOutlined } from '@otakus/icons';
import { AutoComplete, Input } from '@otakus/design';

const renderTitle = (title: string) => (
  <span>
    {title}
    <a
      style={{ float: 'right' }}
      href="https://www.google.com/search?q=@otakus/design"
      target="_blank"
      rel="noopener noreferrer"
    >
      more
    </a>
  </span>
);

const renderItem = (title: string, count: number) => ({
  value: title,
  label: (
    <div
      style={{
        display: 'flex',
        justifyContent: 'space-between'
      }}
    >
      {title}
      <span>
        <UserOutlined /> {count}
      </span>
    </div>
  )
});

const options = [
  {
    label: renderTitle('Libraries'),
    options: [renderItem('@otakus/designesign', 10000), renderItem('@otakus/designesign UI', 10600)]
  },
  {
    label: renderTitle('Solutions'),
    options: [
      renderItem('@otakus/designesign UI FAQ', 60100),
      renderItem('@otakus/designesign FAQ', 30010)
    ]
  },
  {
    label: renderTitle('Articles'),
    options: [renderItem('@otakus/designesign design language', 100000)]
  }
];

const App: React.FC = () => (
  <AutoComplete
    popupClassName="certain-category-search-dropdown"
    popupMatchSelectWidth={500}
    style={{ width: 250 }}
    options={options}
    size="large"
  >
    <Input.Search size="large" placeholder="input here" />
  </AutoComplete>
);

export default App;

```

### Lookup-Patterns - Uncertain Category

```tsx
import React, { useState } from 'react';
import { AutoComplete, Input } from '@otakus/design';
import type { SelectProps } from '@otakus/design';

const getRandomInt = (max: number, min = 0) => Math.floor(Math.random() * (max - min + 1)) + min;

const searchResult = (query: string) =>
  new Array(getRandomInt(5))
    .join('.')
    .split('.')
    .map((_, idx) => {
      const category = `${query}${idx}`;
      return {
        value: category,
        label: (
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between'
            }}
          >
            <span>
              Found {query} on{' '}
              <a
                href={`https://s.taobao.com/search?q=${query}`}
                target="_blank"
                rel="noopener noreferrer"
              >
                {category}
              </a>
            </span>
            <span>{getRandomInt(200, 100)} results</span>
          </div>
        )
      };
    });

const App: React.FC = () => {
  const [options, setOptions] = useState<SelectProps<object>['options']>([]);

  const handleSearch = (value: string) => {
    setOptions(value ? searchResult(value) : []);
  };

  const onSelect = (value: string) => {
    console.log('onSelect', value);
  };

  return (
    <AutoComplete
      popupMatchSelectWidth={252}
      style={{ width: 300 }}
      options={options}
      onSelect={onSelect}
      onSearch={handleSearch}
      size="large"
    >
      <Input.Search size="large" placeholder="input here" enterButton />
    </AutoComplete>
  );
};

export default App;

```

### Status

```tsx
import React, { useState } from 'react';
import { AutoComplete, Space } from '@otakus/design';

const mockVal = (str: string, repeat = 1) => ({
  value: str.repeat(repeat)
});

const App: React.FC = () => {
  const [options, setOptions] = useState<{ value: string }[]>([]);
  const [anotherOptions, setAnotherOptions] = useState<{ value: string }[]>([]);

  const getPanelValue = (searchText: string) =>
    !searchText ? [] : [mockVal(searchText), mockVal(searchText, 2), mockVal(searchText, 3)];

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <AutoComplete
        options={options}
        onSearch={(text) => setOptions(getPanelValue(text))}
        status="error"
        style={{ width: 200 }}
      />
      <AutoComplete
        options={anotherOptions}
        onSearch={(text) => setAnotherOptions(getPanelValue(text))}
        status="warning"
        style={{ width: 200 }}
      />
    </Space>
  );
};

export default App;

```

### Variants

```tsx
import React, { useState } from 'react';
import { AutoComplete, Flex } from '@otakus/design';

const mockVal = (str: string, repeat = 1) => ({
  value: str.repeat(repeat)
});

const App: React.FC = () => {
  const [options, setOptions] = useState<{ value: string }[]>([]);

  const getPanelValue = (searchText: string) =>
    !searchText ? [] : [mockVal(searchText), mockVal(searchText, 2), mockVal(searchText, 3)];

  return (
    <Flex vertical gap={12}>
      <AutoComplete
        options={options}
        style={{ width: 200 }}
        placeholder="Outlined"
        onSearch={(text) => setOptions(getPanelValue(text))}
        onSelect={globalThis.console.log}
      />
      <AutoComplete
        options={options}
        style={{ width: 200 }}
        placeholder="Filled"
        onSearch={(text) => setOptions(getPanelValue(text))}
        onSelect={globalThis.console.log}
        variant="filled"
      />
      <AutoComplete
        options={options}
        style={{ width: 200 }}
        placeholder="Borderless"
        onSearch={(text) => setOptions(getPanelValue(text))}
        onSelect={globalThis.console.log}
        variant="borderless"
      />
    </Flex>
  );
};

export default App;

```

### Customize clear button

```tsx
import React, { useState } from 'react';
import { CloseSquareFilled } from '@otakus/icons';
import { AutoComplete } from '@otakus/design';

const mockVal = (str: string, repeat = 1) => ({
  value: str.repeat(repeat)
});

const App: React.FC = () => {
  const [options, setOptions] = useState<{ value: string }[]>([]);

  const getPanelValue = (searchText: string) =>
    !searchText ? [] : [mockVal(searchText), mockVal(searchText, 2), mockVal(searchText, 3)];

  return (
    <>
      <AutoComplete
        options={options}
        style={{ width: 200 }}
        onSearch={(text) => setOptions(getPanelValue(text))}
        placeholder="UnClearable"
        allowClear={false}
      />
      <br />
      <br />
      <AutoComplete
        options={options}
        style={{ width: 200 }}
        onSearch={(text) => setOptions(getPanelValue(text))}
        placeholder="Customized clear icon"
        allowClear={{ clearIcon: <CloseSquareFilled /> }}
      />
    </>
  );
};

export default App;

```

### Debug in Form

```tsx
import React from 'react';
import { SearchOutlined } from '@otakus/icons';
import { AutoComplete, Button, Form, Input, TreeSelect } from '@otakus/design';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 }
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 }
  }
};

const App: React.FC = () => (
  <Form style={{ margin: '0 auto' }} {...formItemLayout}>
    <Form.Item label="单独 AutoComplete">
      <AutoComplete />
    </Form.Item>
    <Form.Item label="单独 TreeSelect">
      <TreeSelect />
    </Form.Item>
    <Form.Item label="添加 Input.Group 正常">
      <Input.Group compact>
        <TreeSelect style={{ width: '30%' }} />
        <AutoComplete />
      </Input.Group>
    </Form.Item>
    <Form.Item label="包含 search 图标正常">
      <AutoComplete>
        <Input suffix={<SearchOutlined />} />
      </AutoComplete>
    </Form.Item>
    <Form.Item label="同时有 Input.Group 和图标发生移位">
      <Input.Group compact>
        <TreeSelect style={{ width: '30%' }} />
        <AutoComplete>
          <Input suffix={<SearchOutlined />} />
        </AutoComplete>
      </Input.Group>
    </Form.Item>
    <Form.Item label="同时有 Input.Group 和 Search 组件发生移位">
      <Input.Group compact>
        <TreeSelect style={{ width: '30%' }} />
        <AutoComplete>
          <Input.Search />
        </AutoComplete>
      </Input.Group>
    </Form.Item>
    <Form.Item label="Input Group 和 Button 结合">
      <Input.Group compact>
        <TreeSelect style={{ width: '20%' }} />
        <AutoComplete>
          <Input.Search />
        </AutoComplete>
        <Button type="primary" icon={<SearchOutlined />}>
          Search
        </Button>
      </Input.Group>
    </Form.Item>
  </Form>
);

export default App;

```

### \_InternalPanelDoNotUseOrYouWillBeFired

```tsx
import React from 'react';
import { AutoComplete, Switch, Space } from '@otakus/design';

const { _InternalPanelDoNotUseOrYouWillBeFired: InternalAutoComplete } = AutoComplete;

const App: React.FC = () => {
  const [open, setOpen] = React.useState(false);

  return (
    <Space direction="vertical" style={{ display: 'flex' }}>
      <Switch checked={open} onChange={() => setOpen(!open)} />
      <InternalAutoComplete
        defaultValue="lucy"
        style={{ width: 120 }}
        open={open}
        options={[
          { label: 'Jack', value: 'jack' },
          { label: 'Lucy', value: 'lucy' },
          { label: 'Disabled', value: 'disabled' },
          { label: 'Bamboo', value: 'bamboo' }
        ]}
      />
    </Space>
  );
};

export default App;

```

