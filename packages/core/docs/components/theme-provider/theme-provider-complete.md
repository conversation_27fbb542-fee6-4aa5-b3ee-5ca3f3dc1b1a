# ThemeProvider

## 简介

`ThemeProvider` 是在 antd-style 的 ThemeProvider 基础上做的二次封装（也即是在 antd ConfigProvider 基础上封装），本质上是一个基于 React Context 的数据容器，与 ConfigProvider 最大的不同是该组件集中于解决“主题设置”相关的实现。

`@otakus/design` 包中的 ThemeProvider 提供了以下能力：

1. 支持 light/dark 主题切换
2. 内部默认引入了 `App` 组件，支持 `message.success` 等静态方法响应主题设置（antd 默认的 ConfigProvider.theme 方式对静态方法不生效）
3. 部分最基础的 reset 及滚动条等全局样式定义，见 [global.ts](https://platgit.mihoyo.com/ee/infra/otaku-design/otaku-design/-/blob/main/packages/components/src/theme-provider/GlobalStyle/global.ts)
4. 自行控制是否包裹 App DOM 节点

## 主题切换

见 [暗色主题](/components/darkmode)

## 自定义配置

App 默认存在一些基础 reset 样式，如果你不需要这些样式可以通过 `hasDefaultApp = false` 的方式去掉内置的 App DOM。

```css
min-height: inherit;
width: inherit;
height: 100%;
```

## 静态方法

antd5 已经不再建议使用静态方法，推荐用 hooks 方式代替，因为静态方法无法响应 ConfigProvider 等基于 Context 进行的全局配置，也无法响应主题配置。见：[为什么 Modal 方法不能获取 context、redux、的内容和 ConfigProvider locale/prefixCls/theme 等配置](https://ant.design/components/modal-cn#%E4%B8%BA%E4%BB%80%E4%B9%88-modal-%E6%96%B9%E6%B3%95%E4%B8%8D%E8%83%BD%E8%8E%B7%E5%8F%96-contextredux%E7%9A%84%E5%86%85%E5%AE%B9%E5%92%8C-configprovider-localeprefixclstheme-%E7%AD%89%E9%85%8D%E7%BD%AE)

ThemeProvider 内部重新处理了 `message.success` 等静态方法，使其也能正常响应主题设置和变化，但也存在某些场景下你不想由 ThemeProvider 帮你“代理”处理的情况，比如子应用的中的弹窗希望能应用最顶层主应用的主题和配置等。此时你可以通过 `hasDefaultApp = false` 禁用 ThemeProvider 对静态方法对处理，你可以自行在通过 hooks 方式提供在你的应用中全局调用的方法，参考：[在非 React 环境下，仍然响应主题](https://ant-design.github.io/antd-style/zh-CN/best-practice/static-message#%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88)。

:::info{title=温馨提示}

大部分情况下你并不需要关心 `hasDefaultApp` API，仅当项目存在微前端、二次封装组件等复杂情况下需要手动处理。

:::

## API

| 属性               | 说明                                                                                                  | 类型                                   | 默认值                                | 版本  |
| :----------------- | :---------------------------------------------------------------------------------------------------- | :------------------------------------- | :------------------------------------ | :---- |
| hasDefaultApp      | 是否默认包裹 App 组件                                                                                 | `boolean`                              | `true`                                | 0.7.1 |
| prefixCls          | 设置统一样式前缀                                                                                      | `string`                               | `otakus`                              | 0.7.1 |
| iconPrefixCls      | 设置图标统一样式前缀                                                                                  | `string`                               | `otakuicon`                           | 0.7.1 |
| cssVarConfig       | css var 前缀及唯一识别 key 相关配置 [antd cssVar](https://ant.design/docs/react/css-variables-cn#api) | `object`                               | `{ prefix: "otakus", key: "otakus" }` | 0.7.1 |
| appearance         | 应用的展示外观主题，内置亮色和暗色两种，可以自行扩展                                                  | `light` \| `dark`                      | `light`                               |       |
| defaultAppearance  | 默认外观主题                                                                                          | `light` \| `dark`                      | `undefined`                           |       |
| onAppearanceChange | 外观主题的回调                                                                                        | `(appearance:ThemeAppearance) => void` | -                                     |       |
| themeMode          | 主题的展示模式，有三种配置：跟随系统、亮色、暗色 默认不开启自动模式，需要手动进行配置                 | `light` \| `dark` \| `auto`            | `light`                               |       |
| defaultThemeMode   | 默认主题展示模式                                                                                      | `light` \| `dark` \| `auto`            | `undefined`                           |       |
| onThemeModeChange  | 主题模式修改后的回调                                                                                  | `(themeMode: ThemeMode) => void`       | -                                     |       |
| customToken        | 自定义 token， 可在 antd v5 token 规范基础上扩展和新增自己需要的 token                                | `{ [key: string]: any }`               | `undefined`                           |       |
| customStylish      | 自定义 Stylish 变量                                                                                   | `{ [key: string]: any }`               | `undefined`                           |       |

其他组件 API 定义见 [antd-style ThemeProvider](https://ant-design.github.io/antd-style/zh-CN/api/theme-provider)

## 示例

### prefix

```tsx
/**
 * title: 自定义前缀
 * description: 通过 `prefixCls` 修改样式类前缀，`iconPrefixCls` 修改引入的 @otakus/icons 图标资源样式类前缀。
 */
import React from 'react';
import { ThemeProvider, Button, Flex } from '@otakus/design';
import { SearchOutlined } from '@otakus/icons';

const App: React.FC = () => (
  <ThemeProvider prefixCls="xxxx" iconPrefixCls="yyyy">
    <Flex gap="small" wrap="wrap">
      <Button type="primary" icon={<SearchOutlined />}>
        Primary Button
      </Button>
      <Button type="outlined">Outlined Button</Button>
      <ThemeProvider>
        <Button icon={<SearchOutlined />}>Default Button</Button>
      </ThemeProvider>
    </Flex>
  </ThemeProvider>
);

export default App;

```

### css-var

```tsx
/**
 * title: 自定义 CSS 变量前缀
 * description: 封装 antd ConfigProvider.theme.cssVar 能力，见 [antd cssVar](https://ant.design/docs/react/css-variables-cn#api)。
 */
import React from 'react';
import { ThemeProvider, Button, Flex } from '@otakus/design';
import { SearchOutlined } from '@otakus/icons';

const App: React.FC = () => (
  <ThemeProvider cssVarConfig={{ key: 'custom-key', prefix: 'custom-prefix' }}>
    <Flex gap="small" wrap="wrap">
      <Button type="primary">Primary Button</Button>
      <Button icon={<SearchOutlined />}>Default Button</Button>
    </Flex>
  </ThemeProvider>
);

export default App;

```

### custom-theme

```tsx
/**
 * title: 与 ConfigProvider 一样，ThemeProvider 也支持 theme 配置，可以自定义主题。
 * description: 使用 `ThemeProvider.theme` 将覆盖 otaku Design 默认主题。
 */
import { ThemeProvider, Flex } from '@otakus/design';
import App from './common';

export default () => {
  return (
    <Flex>
      <Flex flex={1}>
        <ThemeProvider
          theme={{
            token: {
              colorPrimary: '#ff0000'
            },
            components: {
              Button: {
                contentFontSize: 18
              }
            }
          }}
        >
          <App />
        </ThemeProvider>
      </Flex>
    </Flex>
  );
};

```

### no-app

```tsx
/**
 * title: 无内置 App
 * description: 设置 `hasDefaultApp = false` 时，不在内部插入 App 组件和 DOM。
 */
import React from 'react';
import { ThemeProvider } from '@otakus/design';

const App: React.FC = () => (
  <ThemeProvider hasDefaultApp={false}>
    <div
      style={{
        backgroundColor: 'var(--otakus-color-primary-bg)',
        padding: 'var(--otakus-padding)',
        borderRadius: 'var(--otakus-border-radius)',
        color: 'var(--otakus-color-primary-text)',
        fontSize: 'var(--otakus-font-size)',
        width: '200px'
      }}
    >
      Hello Otakus
    </div>
  </ThemeProvider>
);

export default App;

```

### static-methods

```tsx
/**
 * title: hasDefaultApp = false
 * description: 当设置 `hasDefaultApp = false` 时 ThemeProvider 不代理静态方法，上面静态方法将不再响应 theme 中的 token 设置。
 */
import React from 'react';
import { ThemeProvider, Button, Flex, modal, notification } from '@otakus/design';

const showModal = () => {
  modal.warning({
    title: 'This is a warning message',
    content: 'some messages...some messages...',
    centered: true,
    maskClosable: true
  });
};

const showNotification = () => {
  notification.info({
    message: `Notification`,
    description: 'some notification...some notification...'
  });
};

const App: React.FC = () => (
  <ThemeProvider
    theme={{ token: { colorPrimary: '#5bdbe6', colorInfo: '#5bdbe6' } }}
    hasDefaultApp={false}
  >
    <Flex gap="small" wrap="wrap">
      <Button onClick={showModal}>Open modal</Button>
      <Button onClick={showNotification}>Open notification</Button>
    </Flex>
  </ThemeProvider>
);

export default App;

```

