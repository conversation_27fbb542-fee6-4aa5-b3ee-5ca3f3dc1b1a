# CopyText

### 使用示例

### 组件属性

| 属性名         | 类型                      | 必填 | 描述                           | 版本  |
| -------------- | ------------------------- | ---- | ------------------------------ | ----- |
| text           | string                    | 是   | 展示文本内容                   | 0.9.0 |
| successMessage | string                    | 否   | 复制成功时显示的提示文本       | 0.9.0 |
| errorMessage   | string                    | 否   | 复制失败时显示的提示文本       | 0.9.0 |
| hoverMessage   | string                    | 否   | 鼠标悬停时显示的提示文本       | 0.9.0 |
| disabled       | boolean                   | 否   | 是否禁用                       | 0.9.0 |
| className      | string                    | 否   | 自定义类名                     | 0.9.0 |
| style          | React.CSSProperties       | 否   | 自定义样式                     | 0.9.0 |
| onSuccess      | (message: string) => void | 否   | 复制成功时回调                 | 0.9.0 |
| onError        | (message: string) => void | 否   | 复制失败时回调                 | 0.9.0 |
| timeout        | number                    | 否   | 复制成功后，提示文本的显示时间 | 0.9.0 |
| copyContent    | string                    | 否   | 复制到剪贴板的内容, 默认是text | 0.9.0 |

## 示例

### 基本使用

```tsx
import { CopyText } from '@otakus/design';

function App() {
  return <CopyText text="这是要复制的文本" style={{ width: '200px' }} />;
}

export default App;

```

### 国际化

```tsx
import { ConfigProvider, en_US, CopyText } from '@otakus/design';

function App() {
  return (
    <ConfigProvider locale={en_US}>
      <CopyText text="这是要复制的文本" style={{ width: '200px' }} />
    </ConfigProvider>
  );
}

export default App;

```

