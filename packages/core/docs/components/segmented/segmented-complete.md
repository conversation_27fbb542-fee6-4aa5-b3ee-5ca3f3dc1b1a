# Segmented

## API

通用属性参考：[通用属性](/components/common-props)

> 自 `antd@4.20.0` 版本开始提供该组件。

### Segmented

| 参数         | 说明                         | 类型                                                                                                                      | 默认值   | 版本 |
| ------------ | ---------------------------- | ------------------------------------------------------------------------------------------------------------------------- | -------- | ---- |
| block        | 将宽度调整为父元素宽度的选项 | boolean                                                                                                                   | false    |      |
| defaultValue | 默认选中的值                 | string \| number                                                                                                          |          |      |
| disabled     | 是否禁用                     | boolean                                                                                                                   | false    |      |
| onChange     | 选项变化时的回调函数         | function(value: string \| number)                                                                                         |          |      |
| options      | 数据化配置选项内容           | string\[] \| number\[] \| Array<{ label: ReactNode value: string icon? ReactNode disabled?: boolean className?: string }> | []       |      |
| size         | 控件尺寸                     | `large` \| `middle` \| `small`                                                                                            | `middle` |      |
| value        | 当前选中的值                 | string \| number                                                                                                          |          |      |

## 示例

### Basic

```tsx
import React from 'react';
import { Segmented } from '@otakus/design';

const Demo: React.FC = () => (
  <Segmented<string>
    options={['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Yearly']}
    onChange={(value) => {
      console.log(value); // string
    }}
  />
);

export default Demo;

```

### Block Segmented

```tsx
import React from 'react';
import { Segmented } from '@otakus/design';

const Demo: React.FC = () => (
  <Segmented options={[123, 456, 'longtext-longtext-longtext-longtext']} block />
);

export default Demo;

```

### Disabled

```tsx
import React from 'react';
import { Flex, Segmented } from '@otakus/design';

const App: React.FC = () => (
  <Flex gap="small" align="flex-start" vertical>
    <Segmented options={['Map', 'Transit', 'Satellite']} disabled />
    <Segmented
      options={[
        'Daily',
        { label: 'Weekly', value: 'Weekly', disabled: true },
        'Monthly',
        { label: 'Quarterly', value: 'Quarterly', disabled: true },
        'Yearly'
      ]}
    />
  </Flex>
);

export default App;

```

### Controlled mode

```tsx
import React, { useState } from 'react';
import { Segmented } from '@otakus/design';

const Demo: React.FC = () => {
  const [value, setValue] = useState<string | number>('Map');

  return <Segmented options={['Map', 'Transit', 'Satellite']} value={value} onChange={setValue} />;
};

export default Demo;

```

### Custom Render

```tsx
import React from 'react';
import { UserOutlined } from '@otakus/icons';
import { Avatar, Flex, Segmented } from '@otakus/design';

const App: React.FC = () => (
  <Flex gap="small" align="flex-start" vertical>
    <Segmented
      options={[
        {
          label: (
            <div style={{ padding: 4 }}>
              <Avatar src="https://api.dicebear.com/7.x/miniavs/svg?seed=8" />
              <div>User 1</div>
            </div>
          ),
          value: 'user1'
        },
        {
          label: (
            <div style={{ padding: 4 }}>
              <Avatar style={{ backgroundColor: '#f56a00' }}>K</Avatar>
              <div>User 2</div>
            </div>
          ),
          value: 'user2'
        },
        {
          label: (
            <div style={{ padding: 4 }}>
              <Avatar style={{ backgroundColor: '#87d068' }} icon={<UserOutlined />} />
              <div>User 3</div>
            </div>
          ),
          value: 'user3'
        }
      ]}
    />
    <Segmented
      options={[
        {
          label: (
            <div style={{ padding: 4 }}>
              <div>Spring</div>
              <div>Jan-Mar</div>
            </div>
          ),
          value: 'spring'
        },
        {
          label: (
            <div style={{ padding: 4 }}>
              <div>Summer</div>
              <div>Apr-Jun</div>
            </div>
          ),
          value: 'summer'
        },
        {
          label: (
            <div style={{ padding: 4 }}>
              <div>Autumn</div>
              <div>Jul-Sept</div>
            </div>
          ),
          value: 'autumn'
        },
        {
          label: (
            <div style={{ padding: 4 }}>
              <div>Winter</div>
              <div>Oct-Dec</div>
            </div>
          ),
          value: 'winter'
        }
      ]}
    />
  </Flex>
);

export default App;

```

### Dynamic

```tsx
import React, { useState } from 'react';
import { Button, Flex, Segmented } from '@otakus/design';

const Demo: React.FC = () => {
  const [options, setOptions] = useState(['Daily', 'Weekly', 'Monthly']);
  const [moreLoaded, setMoreLoaded] = useState(false);

  const handleLoadOptions = () => {
    setOptions((prev) => [...prev, 'Quarterly', 'Yearly']);
    setMoreLoaded(true);
  };

  return (
    <Flex gap="small" align="flex-start" vertical>
      <Segmented options={options} />
      <Button type="primary" disabled={moreLoaded} onClick={handleLoadOptions}>
        Load more options
      </Button>
    </Flex>
  );
};

export default Demo;

```

### Three sizes of Segmented

```tsx
import React from 'react';
import { Flex, Segmented } from '@otakus/design';

const App: React.FC = () => (
  <Flex gap="small" align="flex-start" vertical>
    <Segmented size="large" options={['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Yearly']} />
    <Segmented options={['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Yearly']} />
    <Segmented size="small" options={['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Yearly']} />
  </Flex>
);

export default App;

```

### With Icon

```tsx
import React from 'react';
import { AppstoreOutlined, BarsOutlined } from '@otakus/icons';
import { Segmented } from '@otakus/design';

const Demo: React.FC = () => (
  <Segmented
    options={[
      { label: 'List', value: 'List', icon: <BarsOutlined /> },
      { label: 'Kanban', value: 'Kanban', icon: <AppstoreOutlined /> }
    ]}
  />
);

export default Demo;

```

### With Icon only

```tsx
import React from 'react';
import { AppstoreOutlined, BarsOutlined } from '@otakus/icons';
import { Segmented } from '@otakus/design';

const Demo: React.FC = () => (
  <Segmented
    options={[
      { value: 'List', icon: <BarsOutlined /> },
      { value: 'Kanban', icon: <AppstoreOutlined /> }
    ]}
  />
);

export default Demo;

```

### Controlled Synced mode

```tsx
import React, { useState } from 'react';
import { Segmented } from '@otakus/design';

const Demo: React.FC = () => {
  const [foo, setFoo] = useState<string | number>('AND');
  return (
    <>
      <Segmented value={foo} options={['AND', 'OR', 'NOT']} onChange={setFoo} />
      &nbsp;&nbsp;
      <Segmented value={foo} options={['AND', 'OR', 'NOT']} onChange={setFoo} />
    </>
  );
};

export default Demo;

```

### Consistent height

```tsx
import React from 'react';
import { Button, Flex, Input, Segmented, Select } from '@otakus/design';

const App: React.FC = () => (
  <Flex gap="small" vertical>
    <div>
      <Segmented style={{ marginRight: 6 }} size="large" options={['Daily', 'Weekly', 'Monthly']} />
      <Button type="primary" size="large">
        Button
      </Button>
    </div>
    <div>
      <Segmented style={{ marginRight: 6 }} options={['Daily', 'Weekly', 'Monthly']} />
      <Input placeholder="default size" style={{ width: 150 }} />
    </div>
    <div>
      <Segmented style={{ marginRight: 6 }} size="small" options={['Daily', 'Weekly', 'Monthly']} />
      <Select size="small" defaultValue="lucy" style={{ width: 150 }}>
        <Select.Option value="lucy">Lucy</Select.Option>
      </Select>
    </div>
  </Flex>
);

export default App;

```

### Custom component token

```tsx
import React from 'react';
import { ConfigProvider, Segmented } from '@otakus/design';

const Demo: React.FC = () => (
  <ConfigProvider
    theme={{
      components: {
        Segmented: {
          itemColor: '#222',
          itemHoverColor: '#333',
          itemHoverBg: 'rgba(0, 0, 0, 0.06)',
          itemSelectedBg: '#aaa',
          itemActiveBg: '#ccc',
          itemSelectedColor: '#fff'
        }
      }
    }}
  >
    <Segmented options={['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Yearly']} />
  </ConfigProvider>
);

export default Demo;

```

