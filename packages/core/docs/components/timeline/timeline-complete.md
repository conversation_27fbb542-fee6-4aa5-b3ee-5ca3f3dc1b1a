# Timeline

## API

通用属性参考：[通用属性](/components/common-props)

### Timeline

| 参数       | 说明                                           | 类型                             | 默认值                    |
| ---------- | ---------------------------------------------- | -------------------------------- | ------------------------- |
| mode       | 通过设置 `mode` 可以改变时间轴和内容的相对位置 | `left` \| `alternate` \| `right` | -                         |
| pending    | 指定最后一个幽灵节点是否存在或内容             | ReactNode                        | false                     |
| pendingDot | 当最后一个幽灵节点存在時，指定其时间图点       | ReactNode                        | &lt;LoadingOutlined /&gt; |
| reverse    | 节点排序                                       | boolean                          | false                     |
| items      | 选项配置                                       | [Items](#items)[]                | 5.2.0                     |

### Items

时间轴的每一个节点。

| 参数     | 说明                                                        | 类型              | 默认值 |
| -------- | ----------------------------------------------------------- | ----------------- | ------ |
| color    | 指定圆圈颜色 `blue`、`red`、`green`、`gray`，或自定义的色值 | string            | `blue` |
| dot      | 自定义时间轴点                                              | ReactNode         | -      |
| label    | 设置标签                                                    | ReactNode         | -      |
| children | 设置内容                                                    | ReactNode         | -      |
| position | 自定义节点位置                                              | `left` \| `right` | -      |

## 示例

### Basic

```tsx
import React from 'react';
import { Timeline } from '@otakus/design';

const App: React.FC = () => (
  <Timeline
    items={[
      {
        children: 'Create a services site 2015-09-01'
      },
      {
        children: 'Solve initial network problems 2015-09-01'
      },
      {
        children: 'Technical testing 2015-09-01'
      },
      {
        children: 'Network problems being solved 2015-09-01'
      }
    ]}
  />
);

export default App;

```

### Color

```tsx
import React from 'react';
import { FileDoneOutlined } from '@otakus/icons';
import { Timeline } from '@otakus/design';

const App: React.FC = () => (
  <Timeline
    items={[
      {
        color: 'green',
        children: 'Create a services site 2015-09-01'
      },
      {
        color: 'green',
        children: 'Create a services site 2015-09-01'
      },
      {
        color: 'red',
        children: (
          <>
            <p>Solve initial network problems 1</p>
            <p>Solve initial network problems 2</p>
            <p>Solve initial network problems 3 2015-09-01</p>
          </>
        )
      },
      {
        children: (
          <>
            <p>Technical testing 1</p>
            <p>Technical testing 2</p>
            <p>Technical testing 3 2015-09-01</p>
          </>
        )
      },
      {
        color: 'gray',
        children: (
          <>
            <p>Technical testing 1</p>
            <p>Technical testing 2</p>
            <p>Technical testing 3 2015-09-01</p>
          </>
        )
      },
      {
        color: 'gray',
        children: (
          <>
            <p>Technical testing 1</p>
            <p>Technical testing 2</p>
            <p>Technical testing 3 2015-09-01</p>
          </>
        )
      },
      {
        color: '#00CCFF',
        dot: <FileDoneOutlined />,
        children: <p>Custom color testing</p>
      }
    ]}
  />
);

export default App;

```

### Last node and Reversing

```tsx
import React, { useState } from 'react';
import { Button, Timeline } from '@otakus/design';

const App: React.FC = () => {
  const [reverse, setReverse] = useState(false);

  const handleClick = () => {
    setReverse(!reverse);
  };

  return (
    <div>
      <Timeline
        pending="Recording..."
        reverse={reverse}
        items={[
          {
            children: 'Create a services site 2015-09-01'
          },
          {
            children: 'Solve initial network problems 2015-09-01'
          },
          {
            children: 'Technical testing 2015-09-01'
          }
        ]}
      />
      <Button type="primary" style={{ marginTop: 16 }} onClick={handleClick}>
        Toggle Reverse
      </Button>
    </div>
  );
};

export default App;

```

### Alternate

```tsx
import React from 'react';
import { ClockCircleOutlined } from '@otakus/icons';
import { Timeline } from '@otakus/design';

const App: React.FC = () => (
  <Timeline
    mode="alternate"
    items={[
      {
        children: 'Create a services site 2015-09-01'
      },
      {
        children: 'Solve initial network problems 2015-09-01',
        color: 'green'
      },
      {
        dot: <ClockCircleOutlined style={{ fontSize: '16px' }} />,
        children: `Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.`
      },
      {
        color: 'red',
        children: 'Network problems being solved 2015-09-01'
      },
      {
        children: 'Create a services site 2015-09-01'
      },
      {
        dot: <ClockCircleOutlined style={{ fontSize: '16px' }} />,
        children: 'Technical testing 2015-09-01'
      }
    ]}
  />
);

export default App;

```

### Custom

```tsx
import React from 'react';
import { ClockCircleOutlined } from '@otakus/icons';
import { Timeline } from '@otakus/design';

const App: React.FC = () => (
  <Timeline
    items={[
      {
        children: 'Create a services site 2015-09-01'
      },
      {
        children: 'Solve initial network problems 2015-09-01'
      },
      {
        dot: <ClockCircleOutlined className="timeline-clock-icon" />,
        color: 'red',
        children: 'Technical testing 2015-09-01'
      },
      {
        children: 'Network problems being solved 2015-09-01'
      }
    ]}
  />
);

export default App;

```

### Right alternate

```tsx
import React from 'react';
import { ClockCircleOutlined } from '@otakus/icons';
import { Timeline } from '@otakus/design';

const App: React.FC = () => (
  <Timeline
    mode="right"
    items={[
      {
        children: 'Create a services site 2015-09-01'
      },
      {
        children: 'Solve initial network problems 2015-09-01'
      },
      {
        dot: <ClockCircleOutlined style={{ fontSize: '16px' }} />,
        color: 'red',
        children: 'Technical testing 2015-09-01'
      },
      {
        children: 'Network problems being solved 2015-09-01'
      }
    ]}
  />
);

export default App;

```

### Label

```tsx
import React, { useState } from 'react';
import type { RadioChangeEvent } from '@otakus/design';
import { Radio, Timeline } from '@otakus/design';

const App: React.FC = () => {
  const [mode, setMode] = useState<'left' | 'alternate' | 'right'>('left');

  const onChange = (e: RadioChangeEvent) => {
    setMode(e.target.value);
  };

  return (
    <>
      <Radio.Group
        onChange={onChange}
        value={mode}
        style={{
          marginBottom: 20
        }}
      >
        <Radio value="left">Left</Radio>
        <Radio value="right">Right</Radio>
        <Radio value="alternate">Alternate</Radio>
      </Radio.Group>
      <Timeline
        mode={mode}
        items={[
          {
            label: '2015-09-01',
            children: 'Create a services'
          },
          {
            label: '2015-09-01 09:12:11',
            children: 'Solve initial network problems'
          },
          {
            children: 'Technical testing'
          },
          {
            label: '2015-09-01 09:12:11',
            children: 'Network problems being solved'
          }
        ]}
      />
    </>
  );
};

export default App;

```

### Wireframe

```tsx
import React from 'react';
import { ConfigProvider, Timeline } from '@otakus/design';

const App: React.FC = () => (
  <ConfigProvider theme={{ token: { wireframe: true } }}>
    <Timeline
      items={[
        {
          children: 'Create a services site 2015-09-01'
        },
        {
          children: 'Solve initial network problems 2015-09-01'
        },
        {
          children: 'Technical testing 2015-09-01'
        },
        {
          children: 'Network problems being solved 2015-09-01'
        }
      ]}
    />
  </ConfigProvider>
);

export default App;

```

### Component Token

```tsx
import React from 'react';
import { ConfigProvider, Timeline } from '@otakus/design';

const App: React.FC = () => (
  <ConfigProvider
    theme={{
      components: {
        Timeline: {
          tailColor: 'red',
          tailWidth: 10,
          dotBorderWidth: 1,
          dotBg: 'green',
          itemPaddingBottom: 10
        }
      }
    }}
  >
    <Timeline
      items={[
        {
          children: 'Create a services site 2015-09-01'
        },
        {
          children: 'Solve initial network problems 2015-09-01'
        },
        {
          children: 'Technical testing 2015-09-01'
        },
        {
          children: 'Network problems being solved 2015-09-01'
        }
      ]}
    />
  </ConfigProvider>
);

export default App;

```

