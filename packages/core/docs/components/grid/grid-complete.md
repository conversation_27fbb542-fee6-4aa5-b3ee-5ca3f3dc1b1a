# Grid

## API

通用属性参考：[通用属性](/components/common-props)

Otakus Design 的布局组件若不能满足你的需求，你也可以直接使用社区的优秀布局组件：

- [react-flexbox-grid](http://roylee0704.github.io/react-flexbox-grid/)
- [react-blocks](https://github.com/whoisandy/react-blocks/)

### Row

| 参数    | 说明                                                                                                                                   | 类型                                                                                                                                                                                                                                   | 默认值  | 版本           |
| ------- | -------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------- | -------------- |
| align   | 垂直对齐方式                                                                                                                           | `top` \| `middle` \| `bottom` \| `stretch` \| `{[key in 'xs' \| 'sm' \| 'md' \| 'lg' \| 'xl' \| 'xxl']: 'top' \| 'middle' \| 'bottom' \| 'stretch'}`                                                                                   | `top`   | object: 4.24.0 |
| gutter  | 栅格间隔，可以写成像素值或支持响应式的对象写法来设置水平间隔 { xs: 8, sm: 16, md: 24}。或者使用数组形式同时设置 `[水平间距, 垂直间距]` | number \| object \| array                                                                                                                                                                                                              | 0       |                |
| justify | 水平排列方式                                                                                                                           | `start` \| `end` \| `center` \| `space-around` \| `space-between` \| `space-evenly` \| `{[key in 'xs' \| 'sm' \| 'md' \| 'lg' \| 'xl' \| 'xxl']: 'start' \| 'end' \| 'center' \| 'space-around' \| 'space-between' \| 'space-evenly'}` | `start` | object: 4.24.0 |
| wrap    | 是否自动换行                                                                                                                           | boolean                                                                                                                                                                                                                                | true    | 4.8.0          |

### Col

| 参数   | 说明                                                           | 类型             | 默认值 | 版本 |
| ------ | -------------------------------------------------------------- | ---------------- | ------ | ---- |
| flex   | flex 布局属性                                                  | string \| number | -      |      |
| offset | 栅格左侧的间隔格数，间隔内不可以有栅格                         | number           | 0      |      |
| order  | 栅格顺序                                                       | number           | 0      |      |
| pull   | 栅格向左移动格数                                               | number           | 0      |      |
| push   | 栅格向右移动格数                                               | number           | 0      |      |
| span   | 栅格占位格数，为 0 时相当于 `display: none`                    | number           | -      |      |
| xs     | `屏幕 < 576px` 响应式栅格，可为栅格数或一个包含其他属性的对象  | number \| object | -      |      |
| sm     | `屏幕 ≥ 576px` 响应式栅格，可为栅格数或一个包含其他属性的对象  | number \| object | -      |      |
| md     | `屏幕 ≥ 768px` 响应式栅格，可为栅格数或一个包含其他属性的对象  | number \| object | -      |      |
| lg     | `屏幕 ≥ 992px` 响应式栅格，可为栅格数或一个包含其他属性的对象  | number \| object | -      |      |
| xl     | `屏幕 ≥ 1200px` 响应式栅格，可为栅格数或一个包含其他属性的对象 | number \| object | -      |      |
| xxl    | `屏幕 ≥ 1600px` 响应式栅格，可为栅格数或一个包含其他属性的对象 | number \| object | -      |      |

<style>
#grid-demo-flex-align [class~='otakus-row'] {
  background: rgba(128, 128, 128, 0.08);
}

#grid-demo-flex-order [class~='otakus-row'] {
  background: rgba(128, 128, 128, 0.08);
}

#grid-demo-flex [class~='otakus-row'] {
  background: rgba(128, 128, 128, 0.08);
}

.gutter-box {
  padding: 8px 0;
  background: var(--otakus-color-primary, #4e5ff6);
}

#grid-demo-playground [class~='otakus-col'] {
  background: transparent;
  border: 0;
}
#grid-demo-playground [class~='otakus-col'] > div {
  height: 120px;
  font-size: 14px;
  line-height: 120px;
  background: var(--otakus-color-primary, #4e5ff6);
  border-radius: 4px;
}

#grid-demo-playground .otakus-col {
  padding: 0;
}

[id^='grid-demo-'] .otakus-row > div,
.dumi-default-previewer-demo .otakus-row > div {
  min-height: 30px;
  margin-top: var(--otakus-margin-xs, 8px);
  margin-bottom: var(--otakus-margin-xs, 8px);
  color: #fff;
  text-align: center;
  border-radius: 0;
}

[id^='grid-demo-'] .dumi-default-previewer-demo .otakus-row > div:not(.gutter-row) {
  padding: var(--otakus-padding, 16px) 0;
  background: var(--otakus-color-primary, #4e5ff6);
}

[id^='grid-demo-']
  .dumi-default-previewer-demo
  .otakus-row
  > div:not(.gutter-row):nth-child(2n + 1) {
  background: var(--otakus-color-link-hover, #667eff);
}

[id^='grid-demo-'] .otakus-row .demo-col,
[id^='grid-demo-'] .dumi-default-previewer-demo .otakus-row .demo-col {
  margin-top: 0;
  margin-bottom: 0;
  padding: 30px 0;
  color: var(--otakus-color-white, #ffffff);
  font-size: 18px;
  text-align: center;
  border: none;
}

[id^='grid-demo-'] .otakus-row .demo-col-1 {
  background: var(--otakus-color-link-hover, #667eff);
}

[id^='grid-demo-'] .otakus-row .demo-col-2,
.dumi-default-previewer-demo .otakus-row .demo-col-2 {
  background: var(--otakus-color-link-hover, #667eff);
}

[id^='grid-demo-'] .otakus-row .demo-col-3,
.dumi-default-previewer-demo .otakus-row .demo-col-3 {
  color: #999;
  background: rgba(255, 255, 255, 0.2);
}

[id^='grid-demo-'] .otakus-row .demo-col-4,
[id^='grid-demo-'] .dumi-default-previewer-demo .otakus-row .demo-col-4 {
  background: var(--otakus-color-link-hover, #667eff);
}

[id^='grid-demo-'] .otakus-row .demo-col-5,
[id^='grid-demo-'] .dumi-default-previewer-demo .otakus-row .demo-col-5 {
  color: #999;
  background: rgba(255, 255, 255, 0.2);
}

[id^='grid-demo-'] .dumi-default-previewer-demo .height-100 {
  height: 100px;
  line-height: 100px;
}

[id^='grid-demo-'] .dumi-default-previewer-demo .height-50 {
  height: 50px;
  line-height: 50px;
}

[id^='grid-demo-'] .dumi-default-previewer-demo .height-120 {
  height: 120px;
  line-height: 120px;
}

[id^='grid-demo-'] .dumi-default-previewer-demo .height-80 {
  height: 80px;
  line-height: 80px;
}

[id='grid-demo-playground'],
[id='grid-demo-gutter'] {
  > .dumi-default-previewer-demo .otakus-row > div {
    margin-top: 0;
    margin-bottom: 0;
  }
}
</style>

## 示例

### Basic Grid

```tsx
import React from 'react';
import { Col, Row } from '@otakus/design';

const App: React.FC = () => (
  <>
    <Row>
      <Col span={24}>col</Col>
    </Row>
    <Row>
      <Col span={12}>col-12</Col>
      <Col span={12}>col-12</Col>
    </Row>
    <Row>
      <Col span={8}>col-8</Col>
      <Col span={8}>col-8</Col>
      <Col span={8}>col-8</Col>
    </Row>
    <Row>
      <Col span={6}>col-6</Col>
      <Col span={6}>col-6</Col>
      <Col span={6}>col-6</Col>
      <Col span={6}>col-6</Col>
    </Row>
  </>
);

export default App;

```

### Grid Gutter

```tsx
import React from 'react';
import { Col, Divider, Row } from '@otakus/design';

const style: React.CSSProperties = {
  background: 'var(--otakus-color-primary, #4e5ff6)',
  padding: '8px 0'
};

const App: React.FC = () => (
  <>
    <Divider orientation="left">Horizontal</Divider>
    <Row gutter={16}>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
    </Row>
    <Divider orientation="left">Responsive</Divider>
    <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
    </Row>
    <Divider orientation="left">Vertical</Divider>
    <Row gutter={[16, 24]}>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
      <Col className="gutter-row" span={6}>
        <div style={style}>col-6</div>
      </Col>
    </Row>
  </>
);

export default App;

```

### Column offset

```tsx
import React from 'react';
import { Col, Row } from '@otakus/design';

const App: React.FC = () => (
  <>
    <Row>
      <Col span={8}>col-8</Col>
      <Col span={8} offset={8}>
        col-8
      </Col>
    </Row>
    <Row>
      <Col span={6} offset={6}>
        col-6 col-offset-6
      </Col>
      <Col span={6} offset={6}>
        col-6 col-offset-6
      </Col>
    </Row>
    <Row>
      <Col span={12} offset={6}>
        col-12 col-offset-6
      </Col>
    </Row>
  </>
);

export default App;

```

### Grid sort

列排序。

通过使用 `push` 和 `pull` 类就可以很容易的改变列（column）的顺序。

```tsx
import React from 'react';
import { Col, Row } from '@otakus/design';

const App: React.FC = () => (
  <Row>
    <Col span={18} push={6}>
      col-18 col-push-6
    </Col>
    <Col span={6} pull={18}>
      col-6 col-pull-18
    </Col>
  </Row>
);

export default App;

```

### Typesetting

```tsx
import React from 'react';
import { Col, Divider, Row } from '@otakus/design';

const App: React.FC = () => (
  <>
    <Divider orientation="left">sub-element align left</Divider>
    <Row justify="start">
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
    </Row>

    <Divider orientation="left">sub-element align center</Divider>
    <Row justify="center">
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
    </Row>

    <Divider orientation="left">sub-element align right</Divider>
    <Row justify="end">
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
    </Row>

    <Divider orientation="left">sub-element monospaced arrangement</Divider>
    <Row justify="space-between">
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
    </Row>

    <Divider orientation="left">sub-element align full</Divider>
    <Row justify="space-around">
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
    </Row>

    <Divider orientation="left">sub-element align evenly</Divider>
    <Row justify="space-evenly">
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
      <Col span={4}>col-4</Col>
    </Row>
  </>
);

export default App;

```

### Alignment

```tsx
import React from 'react';
import { Col, Divider, Row } from '@otakus/design';

const DemoBox: React.FC<{ children: React.ReactNode; value: number }> = (props) => (
  <p className={`height-${props.value}`}>{props.children}</p>
);

const App: React.FC = () => (
  <>
    <Divider orientation="left">Align Top</Divider>
    <Row justify="center" align="top">
      <Col span={4}>
        <DemoBox value={100}>col-4</DemoBox>
      </Col>
      <Col span={4}>
        <DemoBox value={50}>col-4</DemoBox>
      </Col>
      <Col span={4}>
        <DemoBox value={120}>col-4</DemoBox>
      </Col>
      <Col span={4}>
        <DemoBox value={80}>col-4</DemoBox>
      </Col>
    </Row>

    <Divider orientation="left">Align Middle</Divider>
    <Row justify="space-around" align="middle">
      <Col span={4}>
        <DemoBox value={100}>col-4</DemoBox>
      </Col>
      <Col span={4}>
        <DemoBox value={50}>col-4</DemoBox>
      </Col>
      <Col span={4}>
        <DemoBox value={120}>col-4</DemoBox>
      </Col>
      <Col span={4}>
        <DemoBox value={80}>col-4</DemoBox>
      </Col>
    </Row>

    <Divider orientation="left">Align Bottom</Divider>
    <Row justify="space-between" align="bottom">
      <Col span={4}>
        <DemoBox value={100}>col-4</DemoBox>
      </Col>
      <Col span={4}>
        <DemoBox value={50}>col-4</DemoBox>
      </Col>
      <Col span={4}>
        <DemoBox value={120}>col-4</DemoBox>
      </Col>
      <Col span={4}>
        <DemoBox value={80}>col-4</DemoBox>
      </Col>
    </Row>
  </>
);

export default App;

```

### Order

```tsx
import React from 'react';
import { Col, Divider, Row } from '@otakus/design';

const App: React.FC = () => (
  <>
    <Divider orientation="left">Normal</Divider>
    <Row>
      <Col span={6} order={4}>
        1 col-order-4
      </Col>
      <Col span={6} order={3}>
        2 col-order-3
      </Col>
      <Col span={6} order={2}>
        3 col-order-2
      </Col>
      <Col span={6} order={1}>
        4 col-order-1
      </Col>
    </Row>
    <Divider orientation="left">Responsive</Divider>
    <Row>
      <Col span={6} xs={{ order: 1 }} sm={{ order: 2 }} md={{ order: 3 }} lg={{ order: 4 }}>
        1 col-order-responsive
      </Col>
      <Col span={6} xs={{ order: 2 }} sm={{ order: 1 }} md={{ order: 4 }} lg={{ order: 3 }}>
        2 col-order-responsive
      </Col>
      <Col span={6} xs={{ order: 3 }} sm={{ order: 4 }} md={{ order: 2 }} lg={{ order: 1 }}>
        3 col-order-responsive
      </Col>
      <Col span={6} xs={{ order: 4 }} sm={{ order: 3 }} md={{ order: 1 }} lg={{ order: 2 }}>
        4 col-order-responsive
      </Col>
    </Row>
  </>
);

export default App;

```

### Flex Stretch

```tsx
import React from 'react';
import { Col, Divider, Row } from '@otakus/design';

const App: React.FC = () => (
  <>
    <Divider orientation="left">Percentage columns</Divider>
    <Row>
      <Col flex={2}>2 / 5</Col>
      <Col flex={3}>3 / 5</Col>
    </Row>
    <Divider orientation="left">Fill rest</Divider>
    <Row>
      <Col flex="100px">100px</Col>
      <Col flex="auto">Fill Rest</Col>
    </Row>
    <Divider orientation="left">Raw flex style</Divider>
    <Row>
      <Col flex="1 1 200px">1 1 200px</Col>
      <Col flex="0 1 300px">0 1 300px</Col>
    </Row>

    <Row wrap={false}>
      <Col flex="none">
        <div style={{ padding: '0 16px' }}>none</div>
      </Col>
      <Col flex="auto">auto with no-wrap</Col>
    </Row>
  </>
);

export default App;

```

### Responsive

```tsx
import React from 'react';
import { Col, Row } from '@otakus/design';

const App: React.FC = () => (
  <Row>
    <Col xs={2} sm={4} md={6} lg={8} xl={10}>
      Col
    </Col>
    <Col xs={20} sm={16} md={12} lg={8} xl={4}>
      Col
    </Col>
    <Col xs={2} sm={4} md={6} lg={8} xl={10}>
      Col
    </Col>
  </Row>
);

export default App;

```

### Flex Responsive

```tsx
import React from 'react';
import { Col, Row } from '@otakus/design';

const App: React.FC = () => (
  <Row>
    {new Array(10).fill(0).map((_, index) => {
      const key = `col-${index}`;
      return (
        <Col
          key={key}
          xs={{ flex: '100%' }}
          sm={{ flex: '50%' }}
          md={{ flex: '40%' }}
          lg={{ flex: '20%' }}
          xl={{ flex: '10%' }}
        >
          Col
        </Col>
      );
    })}
  </Row>
);

export default App;

```

### More responsive

```tsx
import React from 'react';
import { Col, Row } from '@otakus/design';

const App: React.FC = () => (
  <Row>
    <Col xs={{ span: 5, offset: 1 }} lg={{ span: 6, offset: 2 }}>
      Col
    </Col>
    <Col xs={{ span: 11, offset: 1 }} lg={{ span: 6, offset: 2 }}>
      Col
    </Col>
    <Col xs={{ span: 5, offset: 1 }} lg={{ span: 6, offset: 2 }}>
      Col
    </Col>
  </Row>
);

export default App;

```

### Playground

```tsx
import React, { useState } from 'react';
import { Col, Row, Slider } from '@otakus/design';

const gutters: Record<PropertyKey, number> = {};
const vgutters: Record<PropertyKey, number> = {};
const colCounts: Record<PropertyKey, number> = {};

[8, 16, 24, 32, 40, 48].forEach((value, i) => {
  gutters[i] = value;
});
[8, 16, 24, 32, 40, 48].forEach((value, i) => {
  vgutters[i] = value;
});
[2, 3, 4, 6, 8, 12].forEach((value, i) => {
  colCounts[i] = value;
});

const App: React.FC = () => {
  const [gutterKey, setGutterKey] = useState(1);
  const [vgutterKey, setVgutterKey] = useState(1);
  const [colCountKey, setColCountKey] = useState(2);

  const cols = [];
  const colCount = colCounts[colCountKey];
  let colCode = '';
  for (let i = 0; i < colCount; i++) {
    cols.push(
      <Col key={i.toString()} span={24 / colCount}>
        <div>Column</div>
      </Col>
    );
    colCode += `  <Col span={${24 / colCount}} />\n`;
  }

  return (
    <>
      <span>Horizontal Gutter (px): </span>
      <div style={{ width: '50%' }}>
        <Slider
          min={0}
          max={Object.keys(gutters).length - 1}
          value={gutterKey}
          onChange={setGutterKey}
          marks={gutters}
          step={null}
          tooltip={{ formatter: (value: number) => gutters[value] }}
        />
      </div>
      <span>Vertical Gutter (px): </span>
      <div style={{ width: '50%' }}>
        <Slider
          min={0}
          max={Object.keys(vgutters).length - 1}
          value={vgutterKey}
          onChange={setVgutterKey}
          marks={vgutters}
          step={null}
          tooltip={{ formatter: (value: number) => vgutters[value] }}
        />
      </div>
      <span>Column Count:</span>
      <div style={{ width: '50%', marginBottom: 48 }}>
        <Slider
          min={0}
          max={Object.keys(colCounts).length - 1}
          value={colCountKey}
          onChange={setColCountKey}
          marks={colCounts}
          step={null}
          tooltip={{ formatter: (value: number) => colCounts[value] }}
        />
      </div>
      <Row gutter={[gutters[gutterKey], vgutters[vgutterKey]]}>
        {cols}
        {cols}
      </Row>
      Another Row:
      <Row gutter={[gutters[gutterKey], vgutters[vgutterKey]]}>{cols}</Row>
      <pre className="demo-code">{`<Row gutter={[${gutters[gutterKey]}, ${vgutters[vgutterKey]}]}>\n${colCode}\n${colCode}</Row>`}</pre>
      <pre className="demo-code">{`<Row gutter={[${gutters[gutterKey]}, ${vgutters[vgutterKey]}]}>\n${colCode}</Row>`}</pre>
    </>
  );
};

export default App;

```

### useBreakpoint Hook

使用 `useBreakpoint` Hook 个性化布局。

```tsx
import React from 'react';
import { Grid, Tag } from '@otakus/design';

const { useBreakpoint } = Grid;

const App: React.FC = () => {
  const screens = useBreakpoint();

  return (
    <>
      Current break point:{' '}
      {Object.entries(screens)
        .filter((screen) => !!screen[1])
        .map((screen) => (
          <Tag color="blue" key={screen[0]}>
            {screen[0]}
          </Tag>
        ))}
    </>
  );
};

export default App;

```

