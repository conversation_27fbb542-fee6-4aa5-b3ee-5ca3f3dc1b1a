# Splitter

## API

通用属性参考：[通用属性](/components/common-props)

> 和 antd 的 Splitter 不同，子元素不一定必须是 Splitter.Panel

### Splitter

| 参数          | 说明                                               | 类型                        | 默认值       | 版本 |
| ------------- | -------------------------------------------------- | --------------------------- | ------------ | ---- |
| layout        | 布局方向                                           | `horizontal` \| `vertical`  | `horizontal` | -    |
| onResizeStart | 开始拖拽之前回调                                   | `(sizes: number[]) => void` | -            | -    |
| onResize      | 面板大小变化回调                                   | `(sizes: number[]) => void` | -            | -    |
| onResizeEnd   | 拖拽结束回调                                       | `(sizes: number[]) => void` | -            | -    |
| lazy          | 延迟渲染模式                                       | `boolean`                   | `false`      | -    |
| resizable     | 是否开启拖拽伸缩                                   | `boolean`                   | `true`       | -    |
| sizes         | 受控面板大小，支持数字 px，'百分比%' 类型或 'auto' | `(number \| string)[]`      | -            | -    |
| onMouseDown   | 拖拽开始时的回调函数                               | `(e: MouseEvent) => void`   | -            | -    |
| onMouseUp     | 拖拽结束时的回调函数                               | `(e: MouseEvent) => void`   | -            | -    |

### Panel

| 参数        | 说明                                                                   | 类型                                            | 默认值  | 版本 |
| ----------- | ---------------------------------------------------------------------- | ----------------------------------------------- | ------- | ---- |
| defaultSize | 初始面板大小，支持数字 px 或者文字 '百分比%' 类型                      | `number \| string`                              | -       | -    |
| min         | 最小阈值，支持数字 px 或者文字 '百分比%' 类型                          | `number \| string`                              | -       | -    |
| max         | 最大阈值，支持数字 px 或者文字 '百分比%' 类型                          | `number \| string`                              | -       | -    |
| size        | 受控面板大小，支持数字 px，'百分比%' 类型或 'auto'，优先级比Splitter高 | `number \| string`                              | -       | -    |
| collapsible | 快速折叠                                                               | `boolean \| { start?: boolean; end?: boolean }` | `false` | -    |
| resizable   | 是否开启拖拽伸缩，优先级比Splitter高                                   | `boolean`                                       | `true`  | -    |

## 从 split-pane-react 迁移

### 属性映射

| split-pane-react/SplitPane | Otakus/Splitter                                                                  |
| -------------------------- | -------------------------------------------------------------------------------- |
| split                      | layout，但语意相反（split-pane-react 中的 horizontal 是 Splitter 中的 vertical） |
| sizes                      | sizes                                                                            |
| resizerSize                | 移除                                                                             |
| allowResize                | resizable                                                                        |
| className                  | className                                                                        |
| sashRender                 | 移除                                                                             |
| performanceMode            | lazy                                                                             |
| onChange                   | onResize                                                                         |
| onDragStart                | onMouseDown                                                                      |
| onDragEnd                  | onMouseUp                                                                        |

| split-pane-react/Pane | Otakus/Splitter.Panel |
| --------------------- | --------------------- |
| className             | className             |
| minSize               | min                   |
| maxSize               | max                   |

## 示例

### 基本用法

```tsx
import React from 'react';
import { Flex, Typography, Splitter } from '@otakus/design';

const Desc: React.FC<Readonly<{ text?: string | number }>> = (props) => (
  <Flex justify="center" align="center" style={{ height: '100%' }}>
    <Typography.Title type="secondary" level={5} style={{ whiteSpace: 'nowrap' }}>
      {props.text}
    </Typography.Title>
  </Flex>
);

const App: React.FC = () => (
  <Splitter style={{ height: 200, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}>
    <Splitter.Panel defaultSize="40%" min="20%" max="70%">
      <Desc text="First" />
    </Splitter.Panel>
    <Splitter.Panel>
      <Desc text="Second" />
    </Splitter.Panel>
  </Splitter>
);

export default App;

```

### 受控模式

```tsx
import React from 'react';
import { Button, Flex, Switch, Typography, Splitter } from '@otakus/design';

const Desc: React.FC<Readonly<{ text?: string | number }>> = (props) => (
  <Flex justify="center" align="center" style={{ height: '100%' }}>
    <Typography.Title type="secondary" level={5} style={{ whiteSpace: 'nowrap' }}>
      {props.text}
    </Typography.Title>
  </Flex>
);

const App: React.FC = () => {
  const [sizes, setSizes] = React.useState<(number | string)[]>(['50%', '50%']);
  const [enabled, setEnabled] = React.useState(true);
  return (
    <Flex vertical gap="middle">
      <Splitter
        onResize={setSizes}
        style={{ height: 200, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}
      >
        <Splitter.Panel size={sizes[0]} resizable={enabled}>
          <Desc text="First" />
        </Splitter.Panel>
        <Splitter.Panel size={sizes[1]}>
          <Desc text="Second" />
        </Splitter.Panel>
      </Splitter>
      <Flex gap="middle" justify="space-between">
        <Switch
          value={enabled}
          onChange={() => setEnabled(!enabled)}
          checkedChildren="Enabled"
          unCheckedChildren="Disabled"
        />
        <Button onClick={() => setSizes(['50%', '50%'])}>Reset</Button>
      </Flex>
    </Flex>
  );
};

export default App;

```

### 垂直方向

```tsx
import React from 'react';
import { Flex, Typography, Splitter } from '@otakus/design';

const Desc: React.FC<Readonly<{ text?: string | number }>> = (props) => (
  <Flex justify="center" align="center" style={{ height: '100%' }}>
    <Typography.Title type="secondary" level={5} style={{ whiteSpace: 'nowrap' }}>
      {props.text}
    </Typography.Title>
  </Flex>
);

const App: React.FC = () => (
  <Splitter layout="vertical" style={{ height: 300, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}>
    <Splitter.Panel>
      <Desc text="First" />
    </Splitter.Panel>
    <Splitter.Panel>
      <Desc text="Second" />
    </Splitter.Panel>
  </Splitter>
);

export default App;

```

### 可折叠

```tsx
import React from 'react';
import { Flex, Typography, Splitter, SplitterProps } from '@otakus/design';

const Desc: React.FC<Readonly<{ text?: string | number }>> = (props) => (
  <Flex justify="center" align="center" style={{ height: '100%' }}>
    <Typography.Title type="secondary" level={5} style={{ whiteSpace: 'nowrap' }}>
      {props.text}
    </Typography.Title>
  </Flex>
);

const CustomSplitter: React.FC<Readonly<SplitterProps>> = ({ style, ...restProps }) => (
  <Splitter style={{ boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)', ...style }} {...restProps}>
    <Splitter.Panel collapsible min="20%">
      <Desc text="First" />
    </Splitter.Panel>
    <Splitter.Panel collapsible>
      <Desc text="Second" />
    </Splitter.Panel>
  </Splitter>
);

const App: React.FC = () => (
  <>
    <Flex gap="middle" vertical>
      <CustomSplitter style={{ height: 200 }} />
      <CustomSplitter style={{ height: 300 }} layout="vertical" />
    </Flex>
  </>
);

export default App;

```

### 多面板

```tsx
import React from 'react';
import { Flex, Typography, Splitter } from '@otakus/design';

const Desc: React.FC<Readonly<{ text?: string | number }>> = (props) => (
  <Flex justify="center" align="center" style={{ height: '100%' }}>
    <Typography.Title type="secondary" level={5} style={{ whiteSpace: 'nowrap' }}>
      Panel {props.text}
    </Typography.Title>
  </Flex>
);

const App: React.FC = () => (
  <Splitter style={{ height: 200, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}>
    <Splitter.Panel collapsible>
      <Desc text={1} />
    </Splitter.Panel>
    <Splitter.Panel collapsible={{ start: true }}>
      <Desc text={2} />
    </Splitter.Panel>
    <Splitter.Panel>
      <Desc text={3} />
    </Splitter.Panel>
  </Splitter>
);

export default App;

```

### 复杂组合

```tsx
import React from 'react';
import { Flex, Typography, Splitter } from '@otakus/design';

const Desc: React.FC<Readonly<{ text?: string | number }>> = (props) => (
  <Flex justify="center" align="center" style={{ height: '100%' }}>
    <Typography.Title type="secondary" level={5} style={{ whiteSpace: 'nowrap' }}>
      {props.text}
    </Typography.Title>
  </Flex>
);

const App: React.FC = () => (
  <Splitter style={{ height: 300, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}>
    <Splitter.Panel collapsible>
      <Desc text="Left" />
    </Splitter.Panel>
    <Splitter.Panel>
      <Splitter layout="vertical">
        <Splitter.Panel>
          <Desc text="Top" />
        </Splitter.Panel>
        <Splitter.Panel>
          <Desc text="Bottom" />
        </Splitter.Panel>
      </Splitter>
    </Splitter.Panel>
  </Splitter>
);

export default App;

```

### 标签页中嵌套

```tsx
import React from 'react';
import { Flex, Tabs, Typography, Splitter } from '@otakus/design';

const Desc: React.FC<Readonly<{ text?: string | number }>> = (props) => (
  <Flex justify="center" align="center" style={{ height: '100%' }}>
    <Typography.Title type="secondary" level={5} style={{ whiteSpace: 'nowrap' }}>
      {props.text}
    </Typography.Title>
  </Flex>
);

const App: React.FC = () => {
  const SplitterContent = (
    <Splitter
      style={{
        height: 200,
        boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)'
      }}
    >
      <Splitter.Panel collapsible>
        <Desc text={1} />
      </Splitter.Panel>
      <Splitter.Panel
        collapsible={{
          start: true
        }}
      >
        <Desc text={2} />
      </Splitter.Panel>
      <Splitter.Panel>
        <Desc text={3} />
      </Splitter.Panel>
    </Splitter>
  );
  return (
    <Tabs
      defaultActiveKey="1"
      items={[
        {
          key: '1',
          label: 'General',
          children: 'Content of Tab Pane 1'
        },
        {
          key: '2',
          label: 'Splitter Tab',
          children: SplitterContent
        }
      ]}
    />
  );
};

export default App;

```

### 延迟渲染模式

```tsx
import React from 'react';
import { Flex, Space, Typography, Splitter } from '@otakus/design';

const Desc: React.FC<Readonly<{ text?: string | number }>> = (props) => (
  <Flex justify="center" align="center" style={{ height: '100%' }}>
    <Typography.Title type="secondary" level={5} style={{ whiteSpace: 'nowrap' }}>
      {props.text}
    </Typography.Title>
  </Flex>
);

const App: React.FC = () => (
  <Space direction="vertical" style={{ width: '100%' }}>
    <Splitter
      lazy
      style={{ height: 200, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}
      onResizeStart={(e) => console.log('start', e)}
      onResize={(e) => console.log('resize', e)}
      onResizeEnd={(e) => console.log('end', e)}
    >
      <Splitter.Panel defaultSize="40%" min="20%" max="70%">
        <Desc text="First" />
      </Splitter.Panel>
      <Splitter.Panel>
        <Desc text="Second" />
      </Splitter.Panel>
    </Splitter>
    <Splitter
      lazy
      layout="vertical"
      style={{ height: 200, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}
    >
      <Splitter.Panel defaultSize="40%" min="30%" max="70%">
        <Desc text="First" />
      </Splitter.Panel>
      <Splitter.Panel>
        <Desc text="Second" />
      </Splitter.Panel>
    </Splitter>
  </Space>
);

export default App;

```

### Fluid面板，split-pane-react中的例子

```tsx
import React, { useState } from 'react';
import { Flex, Typography, Splitter } from '@otakus/design';

const Desc: React.FC<Readonly<{ text?: string | number }>> = (props) => (
  <Flex justify="center" align="center" style={{ height: '100%' }}>
    <Typography.Title type="secondary" level={5} style={{ whiteSpace: 'nowrap' }}>
      {props.text}
    </Typography.Title>
  </Flex>
);

const App: React.FC = () => {
  const [sizes, setSizes] = useState<(number | string)[]>(['20%', 'auto']);
  const [sizes1, setSizes1] = useState<(number | string)[]>(['50%', 'auto']);

  return (
    <Splitter
      sizes={sizes}
      onResize={setSizes}
      style={{ height: 200, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}
    >
      <Splitter.Panel min={100} max="40%">
        <Desc text="First" />
      </Splitter.Panel>
      <Splitter sizes={sizes1} onResize={setSizes1}>
        <Desc text="Second" />
        <Desc text="Third" />
      </Splitter>
    </Splitter>
  );
};

export default App;

```

### 调试

```tsx
import React from 'react';
import { Flex, Typography, Splitter } from '@otakus/design';

const Desc: React.FC<Readonly<{ text?: string | number }>> = (props) => (
  <Flex justify="center" align="center" style={{ height: '100%' }}>
    <Typography.Title type="secondary" level={5} style={{ whiteSpace: 'nowrap' }}>
      Panel {props.text}
    </Typography.Title>
  </Flex>
);

const App: React.FC = () => (
  <Flex vertical gap="middle">
    <Typography.Title level={3}>[true, 0, false]</Typography.Title>
    <Splitter style={{ height: 200, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}>
      <Splitter.Panel>
        <Desc text={1} />
      </Splitter.Panel>
      <Splitter.Panel defaultSize={0}>
        <Desc text={2} />
      </Splitter.Panel>
      <Splitter.Panel resizable={false}>
        <Desc text={3} />
      </Splitter.Panel>
    </Splitter>
    <Typography.Title level={3}>[false, 0, true]</Typography.Title>
    <Splitter style={{ height: 200, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}>
      <Splitter.Panel resizable={false}>
        <Desc text={1} />
      </Splitter.Panel>
      <Splitter.Panel defaultSize={0}>
        <Desc text={2} />
      </Splitter.Panel>
      <Splitter.Panel>
        <Desc text={3} />
      </Splitter.Panel>
    </Splitter>
    <Typography.Title level={3}>Start have min & max</Typography.Title>
    <Splitter style={{ height: 200, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}>
      <Splitter.Panel min={50} max={100}>
        <Desc text={1} />
      </Splitter.Panel>
      <Splitter.Panel>
        <Desc text={2} />
      </Splitter.Panel>
    </Splitter>
    <Typography.Title level={3}>End have min & max</Typography.Title>
    <Splitter style={{ height: 200, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}>
      <Splitter.Panel>
        <Desc text={1} />
      </Splitter.Panel>
      <Splitter.Panel min="20%" max="70%">
        <Desc text={2} />
      </Splitter.Panel>
    </Splitter>
  </Flex>
);

export default App;

```

