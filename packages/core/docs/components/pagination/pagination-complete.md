# Pagination

## API

通用属性参考：[通用属性](/components/common-props)

```js
<Pagination onChange={onChange} total={50} />
```

| 参数             | 说明                                                         | 类型                                                                         | 默认值                     | 版本 |
| ---------------- | ------------------------------------------------------------ | ---------------------------------------------------------------------------- | -------------------------- | ---- |
| current          | 当前页数                                                     | number                                                                       | -                          |      |
| defaultCurrent   | 默认的当前页数                                               | number                                                                       | 1                          |      |
| defaultPageSize  | 默认的每页条数                                               | number                                                                       | 10                         |      |
| disabled         | 禁用分页                                                     | boolean                                                                      | -                          |      |
| hideOnSinglePage | 只有一页时是否隐藏分页器                                     | boolean                                                                      | false                      |      |
| itemRender       | 用于自定义页码的结构，可用于优化 SEO                         | (page, type: 'page' \| 'prev' \| 'next', originalElement) => React.ReactNode | -                          |      |
| pageSize         | 每页条数                                                     | number                                                                       | -                          |      |
| pageSizeOptions  | 指定每页可以显示多少条                                       | string\[] \| number\[]                                                       | \[`10`, `20`, `50`, `100`] |      |
| responsive       | 当 size 未指定时，根据屏幕宽度自动调整尺寸                   | boolean                                                                      | -                          |      |
| showLessItems    | 是否显示较少页面内容                                         | boolean                                                                      | false                      |      |
| showQuickJumper  | 是否可以快速跳转至某页                                       | boolean \| { goButton: ReactNode }                                           | false                      |      |
| showSizeChanger  | 是否展示 `pageSize` 切换器，当 `total` 大于 50 时默认为 true | boolean                                                                      | -                          |      |
| showTitle        | 是否显示原生 tooltip 页码提示                                | boolean                                                                      | true                       |      |
| showTotal        | 用于显示数据总量和当前数据顺序                               | function(total, range)                                                       | -                          |      |
| simple           | 当添加该属性时，显示为简单分页                               | boolean                                                                      | -                          |      |
| size             | 当为 `small` 时，是小尺寸分页                                | `default` \| `small`                                                         | `default`                  |      |
| total            | 数据总数                                                     | number                                                                       | 0                          |      |
| onChange         | 页码或 `pageSize` 改变的回调，参数是改变后的页码及每页条数   | function(page, pageSize)                                                     | -                          |      |
| onShowSizeChange | pageSize 变化的回调                                          | function(current, size)                                                      | -                          |      |

<style>
#pagination-demo-more.dumi-default-previewer,
#pagination-demo-changer.dumi-default-previewer,
#pagination-demo-jump.dumi-default-previewer,
#pagination-demo-mini.dumi-default-previewer,
#pagination-demo-total.dumi-default-previewer,
#pagination-demo-all.dumi-default-previewer,
#pagination-demo-itemrender.dumi-default-previewer {
  overflow: visible;
}
#pagination-demo-mini .otakus-pagination:not(:last-child) {
    margin-bottom: 24px;
}
</style>

## 示例

### Basic

```tsx
import React from 'react';
import { Pagination } from '@otakus/design';

const App: React.FC = () => <Pagination defaultCurrent={1} total={50} />;

export default App;

```

### More

```tsx
import React from 'react';
import { Pagination } from '@otakus/design';

const App: React.FC = () => <Pagination defaultCurrent={6} total={500} />;

export default App;

```

### Changer

```tsx
import React from 'react';
import type { PaginationProps } from '@otakus/design';
import { Pagination } from '@otakus/design';

const onShowSizeChange: PaginationProps['onShowSizeChange'] = (current, pageSize) => {
  console.log(current, pageSize);
};

const App: React.FC = () => (
  <>
    <Pagination
      showSizeChanger
      onShowSizeChange={onShowSizeChange}
      defaultCurrent={3}
      total={500}
    />
    <br />
    <Pagination
      showSizeChanger
      onShowSizeChange={onShowSizeChange}
      defaultCurrent={3}
      total={500}
      disabled
    />
  </>
);

export default App;

```

### Jumper

```tsx
import React from 'react';
import type { PaginationProps } from '@otakus/design';
import { Pagination } from '@otakus/design';

const onChange: PaginationProps['onChange'] = (pageNumber) => {
  console.log('Page: ', pageNumber);
};

const App: React.FC = () => (
  <>
    <Pagination showQuickJumper defaultCurrent={2} total={500} onChange={onChange} />
    <br />
    <Pagination showQuickJumper defaultCurrent={2} total={500} onChange={onChange} disabled />
  </>
);

export default App;

```

### Mini size

```tsx
import React from 'react';
import type { PaginationProps } from '@otakus/design';
import { Pagination } from '@otakus/design';

const showTotal: PaginationProps['showTotal'] = (total) => `Total ${total} items`;

const App: React.FC = () => (
  <>
    <Pagination size="small" total={50} />
    <Pagination size="small" total={50} showSizeChanger showQuickJumper />
    <Pagination size="small" total={50} showTotal={showTotal} />
    <Pagination
      size="small"
      total={50}
      disabled
      showTotal={showTotal}
      showSizeChanger
      showQuickJumper
    />
  </>
);

export default App;

```

### Simple mode

```tsx
import React from 'react';
import { Pagination } from '@otakus/design';

const App: React.FC = () => (
  <>
    <Pagination simple defaultCurrent={2} total={50} />
    <br />
    <Pagination disabled simple defaultCurrent={2} total={50} />
  </>
);

export default App;

```

### Controlled

```tsx
import React, { useState } from 'react';
import type { PaginationProps } from '@otakus/design';
import { Pagination } from '@otakus/design';

const App: React.FC = () => {
  const [current, setCurrent] = useState(3);

  const onChange: PaginationProps['onChange'] = (page) => {
    console.log(page);
    setCurrent(page);
  };

  return <Pagination current={current} onChange={onChange} total={50} />;
};

export default App;

```

### Total number

```tsx
import React from 'react';
import { Pagination } from '@otakus/design';

const App: React.FC = () => (
  <>
    <Pagination
      total={85}
      showTotal={(total) => `Total ${total} items`}
      defaultPageSize={20}
      defaultCurrent={1}
    />
    <br />
    <Pagination
      total={85}
      showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} items`}
      defaultPageSize={20}
      defaultCurrent={1}
    />
  </>
);

export default App;

```

### Show All

```tsx
import React from 'react';
import { Pagination } from '@otakus/design';

const App: React.FC = () => (
  <Pagination
    total={85}
    showSizeChanger
    showQuickJumper
    showTotal={(total) => `Total ${total} items`}
  />
);

export default App;

```

### Prev and next

```tsx
import React from 'react';
import type { PaginationProps } from '@otakus/design';
import { Pagination } from '@otakus/design';

const itemRender: PaginationProps['itemRender'] = (_, type, originalElement) => {
  if (type === 'prev') {
    return <a>Previous</a>;
  }
  if (type === 'next') {
    return <a>Next</a>;
  }
  return originalElement;
};

const App: React.FC = () => <Pagination total={500} itemRender={itemRender} />;

export default App;

```

### Wireframe

```tsx
import React from 'react';
import { ConfigProvider, Pagination } from '@otakus/design';

const App: React.FC = () => (
  <ConfigProvider theme={{ token: { wireframe: true } }}>
    <Pagination showSizeChanger defaultCurrent={3} total={500} />
    <br />
    <Pagination showSizeChanger defaultCurrent={3} total={500} disabled />
    <br />
    <Pagination size="small" defaultCurrent={50} total={500} />
    <br />
    <Pagination disabled size="small" defaultCurrent={50} total={500} />
  </ConfigProvider>
);

export default App;

```

### component Token

```tsx
import React from 'react';
import type { PaginationProps } from '@otakus/design';
import { ConfigProvider, Pagination } from '@otakus/design';

const itemRender: PaginationProps['itemRender'] = (_, type, originalElement) => {
  if (type === 'prev') {
    return <a>Previous</a>;
  }
  if (type === 'next') {
    return <a>Next</a>;
  }
  return originalElement;
};
const App: React.FC = () => (
  <ConfigProvider
    theme={{
      components: {
        Pagination: {
          itemSize: 20,
          itemSizeSM: 12,
          itemActiveBg: '#e7cc87',
          itemLinkBg: '#344324',
          itemActiveBgDisabled: '#9c1515',
          itemInputBg: '#9c1515',
          miniOptionsSizeChangerTop: 0,
          itemBg: '#333'
        }
      }
    }}
  >
    <Pagination
      showSizeChanger
      defaultCurrent={3}
      total={500}
      itemRender={itemRender}
      showQuickJumper
      showTotal={(total) => `Total ${total} items`}
    />
    <br />
    <Pagination showSizeChanger defaultCurrent={3} total={500} disabled />
  </ConfigProvider>
);

export default App;

```

