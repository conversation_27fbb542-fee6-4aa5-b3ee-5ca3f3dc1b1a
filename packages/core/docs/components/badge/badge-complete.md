# Badge

## API

通用属性参考：[通用属性](/components/common-props)

### Badge

| 参数          | 说明                                                                     | 类型                                                           | 默认值 | 版本  |
| ------------- | ------------------------------------------------------------------------ | -------------------------------------------------------------- | ------ | ----- |
| color         | 自定义小圆点的颜色                                                       | string                                                         | -      |       |
| count         | 展示的数字，大于 overflowCount 时显示为 `${overflowCount}+`，为 0 时隐藏 | ReactNode                                                      | -      |       |
| classNames    | 语义化结构 class                                                         | [Record<SemanticDOM, string>](#semantic-dom)                   | -      | 5.7.0 |
| dot           | 不展示数字，只有一个小红点                                               | boolean                                                        | false  |       |
| offset        | 设置状态点的位置偏移                                                     | \[number, number]                                              | -      |       |
| overflowCount | 展示封顶的数字值                                                         | number                                                         | 99     |       |
| showZero      | 当数值为 0 时，是否展示 Badge                                            | boolean                                                        | false  |       |
| size          | 在设置了 `count` 的前提下有效，设置小圆点的大小                          | `default` \| `small`                                           | -      | -     |
| status        | 设置 Badge 为状态点                                                      | `success` \| `processing` \| `default` \| `error` \| `warning` | -      |       |
| styles        | 语义化结构 style                                                         | [Record<SemanticDOM, CSSProperties>](#semantic-dom)            | -      | 5.7.0 |
| text          | 在设置了 `status` 的前提下有效，设置状态点的文本                         | ReactNode                                                      | -      |       |
| title         | 设置鼠标放在状态点上时显示的文字                                         | string                                                         | -      |       |

### Badge.Ribbon

| 参数      | 说明                                                      | 类型             | 默认值 | 版本 |
| --------- | --------------------------------------------------------- | ---------------- | ------ | ---- |
| color     | 自定义缎带的颜色                                          | string           | -      |      |
| placement | 缎带的位置，`start` 和 `end` 随文字方向（RTL 或 LTR）变动 | `start` \| `end` | `end`  |      |
| text      | 缎带中填入的内容                                          | ReactNode        | -      |      |

## 示例

### Basic

```tsx
import React from 'react';
import { ClockCircleOutlined } from '@otakus/icons';
import { Avatar, Badge, Space } from '@otakus/design';

const App: React.FC = () => (
  <Space size="middle">
    <Badge count={5}>
      <Avatar shape="square" size="large" />
    </Badge>
    <Badge count={0} showZero>
      <Avatar shape="square" size="large" />
    </Badge>
    <Badge count={<ClockCircleOutlined style={{ color: '#f5222d' }} />}>
      <Avatar shape="square" size="large" />
    </Badge>
  </Space>
);

export default App;

```

### Standalone

```tsx
import React, { useState } from 'react';
import { ClockCircleOutlined } from '@otakus/icons';
import { Badge, Space, Switch } from '@otakus/design';

const App: React.FC = () => {
  const [show, setShow] = useState(true);

  return (
    <Space>
      <Switch checked={show} onChange={() => setShow(!show)} />
      <Badge count={show ? 11 : 0} showZero color="#faad14" />
      <Badge count={show ? 25 : 0} />
      <Badge count={show ? <ClockCircleOutlined style={{ color: '#f5222d' }} /> : 0} />
      <Badge
        className="site-badge-count-109"
        count={show ? 109 : 0}
        style={{ backgroundColor: '#52c41a' }}
      />
    </Space>
  );
};

export default App;

```

### Overflow Count

```tsx
import React from 'react';
import { Avatar, Badge, Space } from '@otakus/design';

const App: React.FC = () => (
  <Space size="large">
    <Badge count={99}>
      <Avatar shape="square" size="large" />
    </Badge>
    <Badge count={100}>
      <Avatar shape="square" size="large" />
    </Badge>
    <Badge count={99} overflowCount={10}>
      <Avatar shape="square" size="large" />
    </Badge>
    <Badge count={1000} overflowCount={999}>
      <Avatar shape="square" size="large" />
    </Badge>
  </Space>
);

export default App;

```

### Red badge

```tsx
import React from 'react';
import { NotificationOutlined } from '@otakus/icons';
import { Badge, Space } from '@otakus/design';

const App: React.FC = () => (
  <Space>
    <Badge dot>
      <NotificationOutlined style={{ fontSize: 16 }} />
    </Badge>
    <Badge dot>
      <a href="#">Link something</a>
    </Badge>
  </Space>
);

export default App;

```

### Dynamic

```tsx
import React, { useState } from 'react';
import { MinusOutlined, PlusOutlined, QuestionOutlined } from '@otakus/icons';
import { Avatar, Badge, Button, Switch, Space } from '@otakus/design';

const ButtonGroup = Button.Group;

const App: React.FC = () => {
  const [count, setCount] = useState(5);
  const [show, setShow] = useState(true);

  const increase = () => {
    setCount(count + 1);
  };

  const decline = () => {
    let newCount = count - 1;
    if (newCount < 0) {
      newCount = 0;
    }
    setCount(newCount);
  };

  const random = () => {
    const newCount = Math.floor(Math.random() * 100);
    setCount(newCount);
  };

  const onChange = (checked: boolean) => {
    setShow(checked);
  };

  return (
    <Space direction="vertical">
      <Space size="large">
        <Badge count={count}>
          <Avatar shape="square" size="large" />
        </Badge>
        <ButtonGroup>
          <Button onClick={decline} icon={<MinusOutlined />} />
          <Button onClick={increase} icon={<PlusOutlined />} />
          <Button onClick={random} icon={<QuestionOutlined />} />
        </ButtonGroup>
      </Space>
      <Space size="large">
        <Badge dot={show}>
          <Avatar shape="square" size="large" />
        </Badge>
        <Switch onChange={onChange} checked={show} />
      </Space>
    </Space>
  );
};

export default App;

```

### Clickable

```tsx
import React from 'react';
import { Avatar, Badge } from '@otakus/design';

const App: React.FC = () => (
  <a href="#">
    <Badge count={5}>
      <Avatar shape="square" size="large" />
    </Badge>
  </a>
);

export default App;

```

### Offset

```tsx
import React from 'react';
import { Avatar, Badge } from '@otakus/design';

const App: React.FC = () => (
  <Badge count={5} offset={[10, 10]}>
    <Avatar shape="square" size="large" />
  </Badge>
);

export default App;

```

### Size

```tsx
import React from 'react';
import { Avatar, Badge, Space } from '@otakus/design';

const App: React.FC = () => (
  <Space size="middle">
    <Badge size="default" count={5}>
      <Avatar shape="square" size="large" />
    </Badge>
    <Badge size="small" count={5}>
      <Avatar shape="square" size="large" />
    </Badge>
  </Space>
);

export default App;

```

### Status

```tsx
import React from 'react';
import { Badge, Space } from '@otakus/design';

const App: React.FC = () => (
  <>
    <Space>
      <Badge status="success" />
      <Badge status="error" />
      <Badge status="default" />
      <Badge status="processing" />
      <Badge status="warning" />
    </Space>
    <br />
    <Space direction="vertical">
      <Badge status="success" text="Success" />
      <Badge status="error" text="Error" />
      <Badge status="default" text="Default" />
      <Badge status="processing" text="Processing" />
      <Badge status="warning" text="Warning" />
    </Space>
  </>
);

export default App;

```

### Colorful Badge

```tsx
import React from 'react';
import { Badge, Divider, Space } from '@otakus/design';

const colors = [
  'pink',
  'red',
  'yellow',
  'orange',
  'cyan',
  'green',
  'blue',
  'purple',
  'geekblue',
  'magenta',
  'volcano',
  'gold',
  'lime'
];

const App: React.FC = () => (
  <>
    <Divider orientation="left">Presets</Divider>
    <Space direction="vertical">
      {colors.map((color) => (
        <Badge key={color} color={color} text={color} />
      ))}
    </Space>
    <Divider orientation="left">Custom</Divider>
    <Space direction="vertical">
      <Badge color="#f50" text="#f50" />
      <Badge color="rgb(45, 183, 245)" text="rgb(45, 183, 245)" />
      <Badge color="hsl(102, 53%, 61%)" text="hsl(102, 53%, 61%)" />
      <Badge color="hwb(205 6% 9%)" text="hwb(205 6% 9%)" />
    </Space>
  </>
);

export default App;

```

### Ribbon

```tsx
import React from 'react';
import { Badge, Card, Space } from '@otakus/design';

const App: React.FC = () => (
  <Space direction="vertical" size="middle" style={{ width: '100%' }}>
    <Badge.Ribbon text="Hippies">
      <Card title="Pushes open the window" size="small">
        and raises the spyglass.
      </Card>
    </Badge.Ribbon>
    <Badge.Ribbon text="Hippies" color="pink">
      <Card title="Pushes open the window" size="small">
        and raises the spyglass.
      </Card>
    </Badge.Ribbon>
    <Badge.Ribbon text="Hippies" color="red">
      <Card title="Pushes open the window" size="small">
        and raises the spyglass.
      </Card>
    </Badge.Ribbon>
    <Badge.Ribbon text="Hippies" color="cyan">
      <Card title="Pushes open the window" size="small">
        and raises the spyglass.
      </Card>
    </Badge.Ribbon>
    <Badge.Ribbon text="Hippies" color="green">
      <Card title="Pushes open the window" size="small">
        and raises the spyglass.
      </Card>
    </Badge.Ribbon>
    <Badge.Ribbon text="Hippies" color="purple">
      <Card title="Pushes open the window" size="small">
        and raises the spyglass.
      </Card>
    </Badge.Ribbon>
    <Badge.Ribbon text="Hippies" color="volcano">
      <Card title="Pushes open the window" size="small">
        and raises the spyglass.
      </Card>
    </Badge.Ribbon>
    <Badge.Ribbon text="Hippies" color="magenta">
      <Card title="Pushes open the window" size="small">
        and raises the spyglass.
      </Card>
    </Badge.Ribbon>
  </Space>
);

export default App;

```

### Ribbon Debug

```tsx
import React from 'react';
import { Badge, Card, Space } from '@otakus/design';

const App: React.FC = () => (
  <Space direction="vertical" style={{ width: '100%' }}>
    <Badge.Ribbon text="啦啦啦啦">
      <Card>推开窗户举起望远镜</Card>
    </Badge.Ribbon>
    <Badge.Ribbon text="啦啦啦啦" color="purple">
      <Card>推开窗户举起望远镜</Card>
    </Badge.Ribbon>
    <Badge.Ribbon text="啦啦啦啦" color="#2db7f5">
      <Card>推开窗户举起望远镜</Card>
    </Badge.Ribbon>
    <Badge.Ribbon text="啦啦啦啦" color="#2db7f5" placement="start">
      <Card>推开窗户举起望远镜</Card>
    </Badge.Ribbon>
    <Badge.Ribbon text="啦啦啦啦" color="#2db7f5" placement="end">
      <Card>推开窗户举起望远镜</Card>
    </Badge.Ribbon>
  </Space>
);

export default App;

```

### Mixed usage

```tsx
import React from 'react';
import { Avatar, Badge, Space } from '@otakus/design';

const App: React.FC = () => (
  <Space size="middle" wrap>
    <Space size="middle" wrap>
      <Badge count={5} status="success">
        <Avatar shape="square" size="large" />
      </Badge>
      <Badge count={5} status="warning">
        <Avatar shape="square" size="large" />
      </Badge>
      <Badge count={5} color="blue">
        <Avatar shape="square" size="large" />
      </Badge>
      <Badge count={5} color="#fa541c">
        <Avatar shape="square" size="large" />
      </Badge>
      <Badge dot status="success">
        <Avatar shape="square" size="large" />
      </Badge>
      <Badge dot status="warning">
        <Avatar shape="square" size="large" />
      </Badge>
      <Badge dot status="processing">
        <Avatar shape="square" size="large" />
      </Badge>
      <Badge dot color="blue">
        <Avatar shape="square" size="large" />
      </Badge>
      <Badge dot color="#fa541c">
        <Avatar shape="square" size="large" />
      </Badge>
    </Space>

    <Space size="middle" wrap>
      <Badge count={0} showZero />
      <Badge count={0} showZero color="blue" />
      <Badge count={0} showZero color="#f0f" />
      <Badge count={0} showZero>
        <Avatar shape="square" size="large" />
      </Badge>
      <Badge count={0} showZero color="blue">
        <Avatar shape="square" size="large" />
      </Badge>
    </Space>
  </Space>
);

export default App;

```

### Title

```tsx
import React from 'react';
import { Avatar, Badge, Space } from '@otakus/design';

const App: React.FC = () => (
  <Space size="large">
    <Badge count={5} title="Custom hover text">
      <Avatar shape="square" size="large" />
    </Badge>
    <Badge count={-5} title="Negative">
      <Avatar shape="square" size="large" />
    </Badge>
  </Space>
);

export default App;

```

### Colorful Badge support count Debug

```tsx
import React from 'react';
import { Badge, Space } from '@otakus/design';

const colors = [
  'pink',
  'red',
  'yellow',
  'orange',
  'cyan',
  'green',
  'blue',
  'purple',
  'geekblue',
  'magenta',
  'volcano',
  'gold',
  'lime'
];

const AvatarItem = ({ color }: { color: string }) => (
  <div
    style={{
      width: 90,
      height: 90,
      lineHeight: '90px',
      background: '#ccc',
      textAlign: 'center'
    }}
  >
    {color}
  </div>
);

const App: React.FC = () => (
  <>
    <Space wrap size={['large', 'middle']}>
      {colors.map((color) => (
        <Badge color={color} count={44} key={color}>
          <AvatarItem color={color} />
        </Badge>
      ))}
    </Space>
    <Space wrap size={['large', 'middle']}>
      {colors.map((color) => (
        <Badge status="processing" color={color} text="loading" key={color} />
      ))}
    </Space>
  </>
);

export default App;

```

### Component Token

```tsx
import { NotificationOutlined } from '@otakus/icons';
import { Avatar, Badge, ConfigProvider, Space } from '@otakus/design';
import React from 'react';

/** Test usage. Do not use in your production. */
export default () => (
  <ConfigProvider
    theme={{
      components: {
        Badge: {
          indicatorHeight: 24,
          indicatorHeightSM: 18,
          dotSize: 4,
          textFontWeight: 'bold',
          statusSize: 8
        }
      }
    }}
  >
    <Space direction="vertical">
      <Badge count={5}>
        <Avatar shape="square" size="large" />
      </Badge>
      <Badge count={26} />
      <Badge dot>
        <NotificationOutlined />
      </Badge>
      <Badge status="success" text="Success" />
      <Badge size="small" count={0} showZero />
    </Space>
  </ConfigProvider>
);

```

