# Cascader

## API

通用属性参考：[通用属性](/components/common-props)

```js
<Cascader options={options} onChange={onChange} />
```

| 参数                    | 说明                                                                                                                                                          | 类型                                                               | 默认值                                                   | 版本                |
| ----------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------ | -------------------------------------------------------- | ------------------- |
| allowClear              | 支持清除                                                                                                                                                      | boolean \| { clearIcon?: ReactNode }                               | true                                                     | 5.8.0: 支持对象形式 |
| autoClearSearchValue    | 是否在选中项后清空搜索框，只在 `multiple` 为 `true` 时有效                                                                                                    | boolean                                                            | true                                                     | 5.9.0               |
| autoFocus               | 自动获取焦点                                                                                                                                                  | boolean                                                            | false                                                    |                     |
| changeOnSelect          | （单选时生效）当此项为 true 时，点选每级菜单选项值都会发生变化，具体见上面的演示                                                                              | boolean                                                            | false                                                    |                     |
| className               | 自定义类名                                                                                                                                                    | string                                                             | -                                                        |                     |
| defaultValue            | 默认的选中项                                                                                                                                                  | string\[] \| number\[]                                             | \[]                                                      |                     |
| disabled                | 禁用                                                                                                                                                          | boolean                                                            | false                                                    |                     |
| displayRender           | 选择后展示的渲染函数                                                                                                                                          | (label, selectedOptions) => ReactNode                              | label => label.join(`/`)                                 | `multiple`: 4.18.0  |
| tagRender               | 自定义 tag 内容 render，仅在多选时生效                                                                                                                        | ({ label: string, onClose: function, value: string }) => ReactNode | -                                                        |                     |
| popupClassName          | 自定义浮层类名                                                                                                                                                | string                                                             | -                                                        | 4.23.0              |
| dropdownRender          | 自定义下拉框内容                                                                                                                                              | (menus: ReactNode) => ReactNode                                    | -                                                        | 4.4.0               |
| expandIcon              | 自定义次级菜单展开图标                                                                                                                                        | ReactNode                                                          | -                                                        | 4.4.0               |
| expandTrigger           | 次级菜单的展开方式，可选 'click' 和 'hover'                                                                                                                   | string                                                             | `click`                                                  |                     |
| fieldNames              | 自定义 options 中 label value children 的字段                                                                                                                 | object                                                             | { label: `label`, value: `value`, children: `children` } |                     |
| getPopupContainer       | 菜单渲染父节点。默认渲染到 body 上，如果你遇到菜单滚动定位问题，试试修改为滚动的区域，并相对其定位。[示例](https://codepen.io/afc163/pen/zEjNOy?editors=0010) | function(triggerNode)                                              | () => document.body                                      |                     |
| loadData                | 用于动态加载选项，无法与 `showSearch` 一起使用                                                                                                                | (selectedOptions) => void                                          | -                                                        |                     |
| maxTagCount             | 最多显示多少个 tag，响应式模式会对性能产生损耗                                                                                                                | number \| `responsive`                                             | -                                                        | 4.17.0              |
| maxTagPlaceholder       | 隐藏 tag 时显示的内容                                                                                                                                         | ReactNode \| function(omittedValues)                               | -                                                        | 4.17.0              |
| maxTagTextLength        | 最大显示的 tag 文本长度                                                                                                                                       | number                                                             | -                                                        | 4.17.0              |
| notFoundContent         | 当下拉列表为空时显示的内容                                                                                                                                    | string                                                             | `Not Found`                                              |                     |
| open                    | 控制浮层显隐                                                                                                                                                  | boolean                                                            | -                                                        | 4.17.0              |
| options                 | 可选项数据源                                                                                                                                                  | [Option](#option)\[]                                               | -                                                        |                     |
| placeholder             | 输入框占位文本                                                                                                                                                | string                                                             | `请选择`                                                 |                     |
| placement               | 浮层预设位置                                                                                                                                                  | `bottomLeft` `bottomRight` `topLeft` `topRight`                    | `bottomLeft`                                             | 4.17.0              |
| showSearch              | 在选择框中显示搜索框                                                                                                                                          | boolean \| [Object](#showsearch)                                   | false                                                    |                     |
| size                    | 输入框大小                                                                                                                                                    | `large` \| `middle` \| `small`                                     | -                                                        |                     |
| status                  | 设置校验状态                                                                                                                                                  | 'error' \| 'warning'                                               | -                                                        | 4.19.0              |
| style                   | 自定义样式                                                                                                                                                    | CSSProperties                                                      | -                                                        |                     |
| suffixIcon              | 自定义的选择框后缀图标                                                                                                                                        | ReactNode                                                          | -                                                        |                     |
| value                   | 指定选中项                                                                                                                                                    | string\[] \| number\[]                                             | -                                                        |                     |
| variant                 | 形态变体                                                                                                                                                      | `outlined` \| `borderless` \| `filled`                             | `outlined`                                               | 5.13.0              |
| onChange                | 选择完成后的回调                                                                                                                                              | (value, selectedOptions) => void                                   | -                                                        |                     |
| onDropdownVisibleChange | 显示/隐藏浮层的回调                                                                                                                                           | (value) => void                                                    | -                                                        | 4.17.0              |
| multiple                | 支持多选节点                                                                                                                                                  | boolean                                                            | -                                                        | 4.17.0              |
| showCheckedStrategy     | 定义选中项回填的方式。`Cascader.SHOW_CHILD`: 只显示选中的子节点。`Cascader.SHOW_PARENT`: 只显示父节点（当父节点下所有子节点都选中时）。                       | `Cascader.SHOW_PARENT` \| `Cascader.SHOW_CHILD`                    | `Cascader.SHOW_PARENT`                                   | 4.20.0              |
| removeIcon              | 自定义的多选框清除图标                                                                                                                                        | ReactNode                                                          | -                                                        |                     |
| searchValue             | 设置搜索的值，需要与 `showSearch` 配合使用                                                                                                                    | string                                                             | -                                                        | 4.17.0              |
| onSearch                | 监听搜索，返回输入的值                                                                                                                                        | (search: string) => void                                           | -                                                        | 4.17.0              |
| dropdownMenuColumnStyle | 下拉菜单列的样式                                                                                                                                              | CSSProperties                                                      | -                                                        |                     |
| optionRender            | 自定义渲染下拉选项                                                                                                                                            | (option: Option) => React.ReactNode                                | -                                                        | 5.16.0              |

### showSearch

`showSearch` 为对象时，其中的字段：

| 参数            | 说明                                                                                          | 类型                                  | 默认值 | 版本 |
| --------------- | --------------------------------------------------------------------------------------------- | ------------------------------------- | ------ | ---- |
| filter          | 接收 `inputValue` `path` 两个参数，当 `path` 符合筛选条件时，应返回 true，反之则返回 false    | function(inputValue, path): boolean   | -      |      |
| limit           | 搜索结果展示数量                                                                              | number \| false                       | 50     |      |
| matchInputWidth | 搜索结果列表是否与输入框同宽（[效果](https://github.com/ant-design/ant-design/issues/25779)） | boolean                               | true   |      |
| render          | 用于渲染 filter 后的选项                                                                      | function(inputValue, path): ReactNode | -      |      |
| sort            | 用于排序 filter 后的选项                                                                      | function(a, b, inputValue)            | -      |      |

### Option

```typescript
interface Option {
  value: string | number;
  label?: React.ReactNode;
  disabled?: boolean;
  children?: Option[];
  // 标记是否为叶子节点，设置了 `loadData` 时有效
  // 设为 `false` 时会强制标记为父节点，即使当前节点没有 children，也会显示展开图标
  isLeaf?: boolean;
}
```

## 示例

### Basic

```tsx
import React from 'react';
import { Cascader } from '@otakus/design';

interface Option {
  value: string | number;
  label: string;
  children?: Option[];
}

const options: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou',
        children: [
          {
            value: 'xihu',
            label: 'West Lake'
          }
        ]
      }
    ]
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 'zhonghuamen',
            label: 'Zhong Hua Men'
          }
        ]
      }
    ]
  }
];

const onChange = (value: (string | number)[]) => {
  console.log(value);
};

const App: React.FC = () => (
  <Cascader options={options} onChange={onChange} placeholder="Please select" />
);

export default App;

```

### Default value

```tsx
import React from 'react';
import { Cascader } from '@otakus/design';

interface Option {
  value: string;
  label: string;
  children?: Option[];
}

const options: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou',
        children: [
          {
            value: 'xihu',
            label: 'West Lake'
          }
        ]
      }
    ]
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 'zhonghuamen',
            label: 'Zhong Hua Men'
          }
        ]
      }
    ]
  }
];

const onChange = (value: string[]) => {
  console.log(value);
};

const App: React.FC = () => (
  <Cascader defaultValue={['zhejiang', 'hangzhou', 'xihu']} options={options} onChange={onChange} />
);

export default App;

```

### Custom trigger

```tsx
import React, { useState } from 'react';
import { Cascader } from '@otakus/design';

interface Option {
  value: string;
  label: string;
  children?: Option[];
}

const options: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou'
      }
    ]
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing'
      }
    ]
  }
];

const App: React.FC = () => {
  const [text, setText] = useState('Unselect');

  const onChange = (_: string[], selectedOptions: Option[]) => {
    setText(selectedOptions.map((o) => o.label).join(', '));
  };

  return (
    <span>
      {text}
      &nbsp;
      <Cascader options={options} onChange={onChange}>
        <a>Change city</a>
      </Cascader>
    </span>
  );
};

export default App;

```

### Hover

```tsx
import React from 'react';
import { Cascader } from '@otakus/design';

interface Option {
  value: string;
  label: string;
  children?: Option[];
}

const options: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou',
        children: [
          {
            value: 'xihu',
            label: 'West Lake'
          }
        ]
      }
    ]
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 'zhonghuamen',
            label: 'Zhong Hua Men'
          }
        ]
      }
    ]
  }
];

const onChange = (value: string[]) => {
  console.log(value);
};

// Just show the latest item.
const displayRender = (labels: string[]) => labels[labels.length - 1];

const App: React.FC = () => (
  <Cascader
    options={options}
    expandTrigger="hover"
    displayRender={displayRender}
    onChange={onChange}
  />
);

export default App;

```

### Disabled option

```tsx
import React from 'react';
import { Cascader } from '@otakus/design';

interface Option {
  value: string;
  label: string;
  disabled?: boolean;
  children?: Option[];
}

const options: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou',
        children: [
          {
            value: 'xihu',
            label: 'West Lake'
          }
        ]
      }
    ]
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    disabled: true,
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 'zhonghuamen',
            label: 'Zhong Hua Men'
          }
        ]
      }
    ]
  }
];

const onChange = (value: string[]) => {
  console.log(value);
};

const App: React.FC = () => <Cascader options={options} onChange={onChange} />;

export default App;

```

### Change on select

```tsx
import React from 'react';
import { Cascader } from '@otakus/design';

interface Option {
  value: string;
  label: string;
  children?: Option[];
}

const options: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hanzhou',
        children: [
          {
            value: 'xihu',
            label: 'West Lake'
          }
        ]
      }
    ]
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 'zhonghuamen',
            label: 'Zhong Hua Men'
          }
        ]
      }
    ]
  }
];

const onChange = (value: string[]) => {
  console.log(value);
};

const App: React.FC = () => <Cascader options={options} onChange={onChange} changeOnSelect />;

export default App;

```

### Multiple

```tsx
import React from 'react';
import { Cascader } from '@otakus/design';

interface Option {
  value: string | number;
  label: string;
  children?: Option[];
  disableCheckbox?: boolean;
}

const options: Option[] = [
  {
    label: 'Light',
    value: 'light',
    children: new Array(20)
      .fill(null)
      .map((_, index) => ({ label: `Number ${index}`, value: index }))
  },
  {
    label: 'Bamboo',
    value: 'bamboo',
    children: [
      {
        label: 'Little',
        value: 'little',
        children: [
          {
            label: 'Toy Fish',
            value: 'fish',
            disableCheckbox: true
          },
          {
            label: 'Toy Cards',
            value: 'cards'
          },
          {
            label: 'Toy Bird',
            value: 'bird'
          }
        ]
      }
    ]
  }
];

const onChange = (value: string[][]) => {
  console.log(value);
};

const App: React.FC = () => (
  <Cascader
    style={{ width: '100%' }}
    options={options}
    onChange={onChange}
    multiple
    maxTagCount="responsive"
  />
);

export default App;

```

### ShowCheckedStrategy

```tsx
import React from 'react';
import { Cascader } from '@otakus/design';

const { SHOW_CHILD } = Cascader;

interface Option {
  value: string | number;
  label: string;
  children?: Option[];
}
const options: Option[] = [
  {
    label: 'Light',
    value: 'light',
    children: new Array(20)
      .fill(null)
      .map((_, index) => ({ label: `Number ${index}`, value: index }))
  },
  {
    label: 'Bamboo',
    value: 'bamboo',
    children: [
      {
        label: 'Little',
        value: 'little',
        children: [
          {
            label: 'Toy Fish',
            value: 'fish'
          },
          {
            label: 'Toy Cards',
            value: 'cards'
          },
          {
            label: 'Toy Bird',
            value: 'bird'
          }
        ]
      }
    ]
  }
];

const App: React.FC = () => {
  const onChange = (value: string[][]) => {
    console.log(value);
  };
  return (
    <>
      <Cascader
        style={{ width: '100%' }}
        options={options}
        onChange={onChange}
        multiple
        maxTagCount="responsive"
        showCheckedStrategy={SHOW_CHILD}
        defaultValue={[
          ['bamboo', 'little', 'fish'],
          ['bamboo', 'little', 'cards'],
          ['bamboo', 'little', 'bird']
        ]}
      />
      <br />
      <br />
      <Cascader
        style={{ width: '100%' }}
        options={options}
        onChange={onChange}
        multiple
        maxTagCount="responsive"
        defaultValue={['bamboo']}
      />
    </>
  );
};

export default App;

```

### Size

```tsx
import React from 'react';
import { Cascader } from '@otakus/design';

interface Option {
  value: string;
  label: string;
  children?: Option[];
}

const options: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou',
        children: [
          {
            value: 'xihu',
            label: 'West Lake'
          }
        ]
      }
    ]
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 'zhonghuamen',
            label: 'Zhong Hua Men'
          }
        ]
      }
    ]
  }
];

const onChange = (value: string[]) => {
  console.log(value);
};

const App: React.FC = () => (
  <>
    <Cascader size="large" options={options} onChange={onChange} />
    <br />
    <br />
    <Cascader options={options} onChange={onChange} />
    <br />
    <br />
    <Cascader size="small" options={options} onChange={onChange} />
    <br />
    <br />
  </>
);

export default App;

```

### Custom render

```tsx
import React from 'react';
import { Cascader } from '@otakus/design';
import type { CascaderProps, GetProp } from '@otakus/design';

type DefaultOptionType = GetProp<CascaderProps, 'options'>[number];

interface Option {
  value: string;
  label: string;
  children?: Option[];
  code?: number;
}

const options: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou',
        children: [
          {
            value: 'xihu',
            label: 'West Lake',
            code: 752100
          }
        ]
      }
    ]
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 'zhonghuamen',
            label: 'Zhong Hua Men',
            code: 453400
          }
        ]
      }
    ]
  }
];

const handleAreaClick = (
  e: React.MouseEvent<HTMLAnchorElement>,
  label: string,
  option: DefaultOptionType
) => {
  e.stopPropagation();
  console.log('clicked', label, option);
};

const displayRender = (labels: string[], selectedOptions: DefaultOptionType[]) =>
  labels.map((label, i) => {
    const option = selectedOptions[i];
    if (i === labels.length - 1) {
      return (
        <span key={option.value}>
          {label} (<a onClick={(e) => handleAreaClick(e, label, option)}>{option.code}</a>)
        </span>
      );
    }
    return <span key={option.value}>{label} / </span>;
  });

const App: React.FC = () => (
  <Cascader
    options={options}
    defaultValue={['zhejiang', 'hangzhou', 'xihu']}
    displayRender={displayRender}
    style={{ width: '100%' }}
  />
);

export default App;

```

### Search

```tsx
import React from 'react';
import { Cascader } from '@otakus/design';
import type { GetProp, CascaderProps } from '@otakus/design';

type DefaultOptionType = GetProp<CascaderProps, 'options'>[number];

interface Option {
  value: string;
  label: string;
  children?: Option[];
  disabled?: boolean;
}

const options: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou',
        children: [
          {
            value: 'xihu',
            label: 'West Lake'
          },
          {
            value: 'xiasha',
            label: 'Xia Sha',
            disabled: true
          }
        ]
      }
    ]
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 'zhonghuamen',
            label: 'Zhong Hua men'
          }
        ]
      }
    ]
  }
];

const onChange = (value: string[], selectedOptions: Option[]) => {
  console.log(value, selectedOptions);
};

const filter = (inputValue: string, path: DefaultOptionType[]) =>
  path.some(
    (option) => (option.label as string).toLowerCase().indexOf(inputValue.toLowerCase()) > -1
  );

const App: React.FC = () => (
  <Cascader
    options={options}
    onChange={onChange}
    placeholder="Please select"
    showSearch={{ filter }}
    onSearch={(value) => console.log(value)}
  />
);

export default App;

```

### Load Options Lazily

```tsx
import React, { useState } from 'react';
import { Cascader } from '@otakus/design';

interface Option {
  value?: string | number | null;
  label: React.ReactNode;
  children?: Option[];
  isLeaf?: boolean;
}

const optionLists: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    isLeaf: false
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    isLeaf: false
  }
];

const App: React.FC = () => {
  const [options, setOptions] = useState<Option[]>(optionLists);

  const onChange = (value: (string | number)[], selectedOptions: Option[]) => {
    console.log(value, selectedOptions);
  };

  const loadData = (selectedOptions: Option[]) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];

    // load options lazily
    setTimeout(() => {
      targetOption.children = [
        {
          label: `${targetOption.label} Dynamic 1`,
          value: 'dynamic1'
        },
        {
          label: `${targetOption.label} Dynamic 2`,
          value: 'dynamic2'
        }
      ];
      setOptions([...options]);
    }, 1000);
  };

  return <Cascader options={options} loadData={loadData} onChange={onChange} changeOnSelect />;
};

export default App;

```

### Custom Field Names

```tsx
import React from 'react';
import { Cascader } from '@otakus/design';

interface Option {
  code: string;
  name: string;
  items?: Option[];
}

const options: Option[] = [
  {
    code: 'zhejiang',
    name: 'Zhejiang',
    items: [
      {
        code: 'hangzhou',
        name: 'Hangzhou',
        items: [
          {
            code: 'xihu',
            name: 'West Lake'
          }
        ]
      }
    ]
  },
  {
    code: 'jiangsu',
    name: 'Jiangsu',
    items: [
      {
        code: 'nanjing',
        name: 'Nanjing',
        items: [
          {
            code: 'zhonghuamen',
            name: 'Zhong Hua Men'
          }
        ]
      }
    ]
  }
];

const onChange = (value: string[]) => {
  console.log(value);
};

const App: React.FC = () => (
  <Cascader
    fieldNames={{ label: 'name', value: 'code', children: 'items' }}
    options={options}
    onChange={onChange}
    placeholder="Please select"
  />
);

export default App;

```

### Custom Icons

```tsx
import React from 'react';
import { SettingOutlined } from '@otakus/icons';
import { Cascader } from '@otakus/design';

interface Option {
  value: string;
  label: string;
  children?: Option[];
}

const options: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou',
        children: [
          {
            value: 'xihu',
            label: 'West Lake'
          }
        ]
      }
    ]
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 'zhonghuamen',
            label: 'Zhong Hua Men'
          }
        ]
      }
    ]
  }
];

const onChange = (value: string[]) => {
  console.log(value);
};

const App: React.FC = () => (
  <>
    <Cascader
      suffixIcon={<SettingOutlined />}
      options={options}
      onChange={onChange}
      placeholder="Please select"
    />
    <br />
    <br />
    <Cascader suffixIcon="ab" options={options} onChange={onChange} placeholder="Please select" />
    <br />
    <br />
    <Cascader
      expandIcon={<SettingOutlined />}
      options={options}
      onChange={onChange}
      placeholder="Please select"
    />
    <br />
    <br />
    <Cascader expandIcon="ab" options={options} onChange={onChange} placeholder="Please select" />
  </>
);

export default App;

```

### Custom dropdown

```tsx
import React from 'react';
import { Cascader, Divider } from '@otakus/design';

interface Option {
  value: string;
  label: string;
  children?: Option[];
}

const options: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou',
        children: [
          {
            value: 'xihu',
            label: 'West Lake'
          }
        ]
      }
    ]
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 'zhonghuamen',
            label: 'Zhong Hua Men'
          }
        ]
      }
    ]
  }
];

const dropdownRender = (menus: React.ReactNode) => (
  <div>
    {menus}
    <Divider style={{ margin: 0 }} />
    <div style={{ padding: 8 }}>The footer is not very short.</div>
  </div>
);

const App: React.FC = () => (
  <Cascader options={options} dropdownRender={dropdownRender} placeholder="Please select" />
);

export default App;

```

### Placement

```tsx
import React, { useState } from 'react';
import type { RadioChangeEvent } from '@otakus/design';
import { Cascader, Radio } from '@otakus/design';

interface Option {
  value: string;
  label: string;
  children?: Option[];
}

const options: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou',
        children: [
          {
            value: 'xihu',
            label: 'West Lake'
          }
        ]
      }
    ]
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 'zhonghuamen',
            label: 'Zhong Hua Men'
          }
        ]
      }
    ]
  }
];

const App: React.FC = () => {
  const [placement, SetPlacement] = useState<'bottomLeft' | 'bottomRight' | 'topLeft' | 'topRight'>(
    'topLeft'
  );

  const placementChange = (e: RadioChangeEvent) => {
    SetPlacement(e.target.value);
  };

  return (
    <>
      <Radio.Group value={placement} onChange={placementChange}>
        <Radio.Button value="topLeft">topLeft</Radio.Button>
        <Radio.Button value="topRight">topRight</Radio.Button>
        <Radio.Button value="bottomLeft">bottomLeft</Radio.Button>
        <Radio.Button value="bottomRight">bottomRight</Radio.Button>
      </Radio.Group>
      <br />
      <br />
      <Cascader options={options} placeholder="Please select" placement={placement} />
    </>
  );
};

export default App;

```

### Status

```tsx
import React from 'react';
import { Cascader, Space } from '@otakus/design';

const App: React.FC = () => (
  <Space direction="vertical">
    <Cascader status="error" placeholder="Error" />
    <Cascader status="warning" multiple placeholder="Warning multiple" />
  </Space>
);

export default App;

```

### = 5.10.0">Panel

```tsx
import React from 'react';
import { Cascader, Flex } from '@otakus/design';

interface Option {
  value: string | number;
  label: string;
  children?: Option[];
}

const options: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou',
        children: [
          {
            value: 'xihu',
            label: 'West Lake'
          }
        ]
      }
    ]
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 'zhonghuamen',
            label: 'Zhong Hua Men'
          }
        ]
      }
    ]
  }
];

const onChange = (value: string[]) => {
  console.log(value);
};

const App: React.FC = () => (
  <Flex vertical gap="small" align="flex-start">
    <Cascader.Panel options={options} onChange={onChange} />
    <Cascader.Panel multiple options={options} onChange={onChange} />
    <Cascader.Panel />
  </Flex>
);

export default App;

```

### \_InternalPanelDoNotUseOrYouWillBeFired

```tsx
import React from 'react';
import { Cascader } from '@otakus/design';

const { _InternalPanelDoNotUseOrYouWillBeFired: InternalCascader } = Cascader;

interface Option {
  value: string | number;
  label: string;
  children?: Option[];
}

const options: Option[] = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou',
        children: [
          {
            value: 'xihu',
            label: 'West Lake'
          }
        ]
      }
    ]
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 'zhonghuamen',
            label: 'Zhong Hua Men'
          }
        ]
      }
    ]
  }
];

const App: React.FC = () => <InternalCascader options={options} placeholder="Please select" />;

export default App;

```

