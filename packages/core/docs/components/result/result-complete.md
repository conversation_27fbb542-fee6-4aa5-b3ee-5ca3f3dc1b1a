# Result

## API

### 修改

修改如下 API：

| 属性   | 说明                       | 类型                                                                                                                                                                   | 默认值 | 版本                                         |
| :----- | :------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----- | :------------------------------------------- |
| status | 结果的状态，决定图标和颜色 | `success` \| `info` \| `warning` \| `error` \| `403` \| `404` \| `500` \| `no-content` \| `no-data` \| `no-image` \| `network-error` \| `no-permission` \| `not-found` | info   | 0.7.5(`no-permission` \| `not-found` 0.10.2) |

新增如下 API：

| 属性 | 说明 | 类型    | 默认值 | 版本   |
| :--- | :--- | :------ | :----- | :----- |
| size | 大小 | `small` | -      | 0.10.2 |

### Antd

通用属性参考：[通用属性](/components/common-props)

| 参数     | 说明                       | 类型                                                                                                                                 | 默认值 |
| -------- | -------------------------- | ------------------------------------------------------------------------------------------------------------------------------------ | ------ |
| status   | 结果的状态，决定图标和颜色 | `success` \| `info` \| `warning` \| `error` \| `403` \| `404` \| `500` \| `no-content` \| `no-data` \| `no-image` \| `network-error` | info   |
| extra    | 操作区                     | ReactNode                                                                                                                            | -      |
| icon     | 自定义 icon                | ReactNode                                                                                                                            | -      |
| subTitle | subTitle 文字              | ReactNode                                                                                                                            | -      |
| title    | title 文字                 | ReactNode                                                                                                                            | -      |

## 示例

### Success

```tsx
import React from 'react';
import { Button, Result } from '@otakus/design';

const App: React.FC = () => (
  <Result
    status="success"
    title="Successfully Purchased Cloud Server ECS!"
    subTitle="Order number: **********828182881 Cloud server configuration takes 1-5 minutes, please wait."
    extra={[
      <Button type="primary" key="console">
        Go Console
      </Button>,
      <Button key="buy">Buy Again</Button>
    ]}
  />
);

export default App;

```

### Info

```tsx
import React from 'react';
import { Button, Result } from '@otakus/design';

const App: React.FC = () => (
  <Result
    title="Your operation has been executed"
    extra={
      <Button type="primary" key="console">
        Go Console
      </Button>
    }
  />
);

export default App;

```

### Warning

```tsx
import React from 'react';
import { Button, Result } from '@otakus/design';

const App: React.FC = () => (
  <Result
    status="warning"
    title="There are some problems with your operation."
    extra={
      <Button type="primary" key="console">
        Go Console
      </Button>
    }
  />
);

export default App;

```

### 403

```tsx
import React from 'react';
import { Button, Result } from '@otakus/design';

const App: React.FC = () => (
  <Result
    status="403"
    title="403"
    subTitle="Sorry, you are not authorized to access this page."
    extra={<Button type="primary">Back Home</Button>}
  />
);

export default App;

```

### 404

```tsx
import React from 'react';
import { Button, Result } from '@otakus/design';

const App: React.FC = () => (
  <Result
    status="404"
    title="404"
    subTitle="Sorry, the page you visited does not exist."
    extra={<Button type="primary">Back Home</Button>}
  />
);

export default App;

```

### 500

```tsx
import React from 'react';
import { Button, Result } from '@otakus/design';

const App: React.FC = () => (
  <Result
    status="500"
    title="500"
    subTitle="Sorry, something went wrong."
    extra={<Button type="primary">Back Home</Button>}
  />
);

export default App;

```

### Error

```tsx
import React from 'react';
import { CloseCircleOutlined } from '@otakus/icons';
import { Button, Result, Typography } from '@otakus/design';

const { Paragraph, Text } = Typography;

const App: React.FC = () => (
  <Result
    status="error"
    title="Submission Failed"
    subTitle="Please check and modify the following information before resubmitting."
    extra={[
      <Button type="primary" key="console">
        Go Console
      </Button>,
      <Button key="buy">Buy Again</Button>
    ]}
  >
    <div className="desc">
      <Paragraph>
        <Text
          strong
          style={{
            fontSize: 16
          }}
        >
          The content you submitted has the following error:
        </Text>
      </Paragraph>
      <Paragraph>
        <CloseCircleOutlined className="site-result-demo-error-icon" /> Your account has been
        frozen. <a>Thaw immediately &gt;</a>
      </Paragraph>
      <Paragraph>
        <CloseCircleOutlined className="site-result-demo-error-icon" /> Your account is not yet
        eligible to apply. <a>Apply Unlock &gt;</a>
      </Paragraph>
    </div>
  </Result>
);

export default App;

```

### Custom icon

```tsx
import React from 'react';
import { ClickOutlined } from '@otakus/icons';
import { Button, Result } from '@otakus/design';

const App: React.FC = () => (
  <Result
    icon={<ClickOutlined />}
    title="Great, we have done all the operations!"
    extra={<Button type="primary">Next</Button>}
  />
);

export default App;

```

### Component Token

```tsx
import React from 'react';
import { Button, ConfigProvider, Result } from '@otakus/design';

const App: React.FC = () => (
  <ConfigProvider
    theme={{
      components: {
        Result: {
          titleFontSize: 18,
          subtitleFontSize: 14,
          iconFontSize: 48,
          extraMargin: `12px 0 0 0`
        }
      }
    }}
  >
    <Result
      status="success"
      title="Successfully Purchased Cloud Server ECS!"
      subTitle="Order number: **********828182881 Cloud server configuration takes 1-5 minutes, please wait."
      extra={[
        <Button type="primary" key="console">
          Go Console
        </Button>,
        <Button key="buy">Buy Again</Button>
      ]}
    />
  </ConfigProvider>
);

export default App;

```

### no content

```tsx
import { Result } from '@otakus/design';
import React from 'react';

const App: React.FC = () => <Result status="no-content" title="no content" />;

export default App;

```

### no data

```tsx
import { Result } from '@otakus/design';
import React from 'react';

const App: React.FC = () => <Result status="no-data" title="no data" />;

export default App;

```

### no image

```tsx
import { Result } from '@otakus/design';
import React from 'react';

const App: React.FC = () => <Result status="no-image" title="no image" />;

export default App;

```

### network error

```tsx
import { Result } from '@otakus/design';
import React from 'react';

const App: React.FC = () => <Result status="network-error" title="network error" />;

export default App;

```

### size 大小

```tsx
import React, { useState } from 'react';
import { Button, Result, Switch, Space } from '@otakus/design';

const App: React.FC = () => {
  const [isSmall, setIsSmall] = useState(false);

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <div>
        <Space align="center">
          <span>小尺寸:</span>
          <Switch
            checked={isSmall}
            onChange={setIsSmall}
            checkedChildren="小"
            unCheckedChildren="默认"
          />
        </Space>
      </div>

      <Result
        size={isSmall ? 'small' : undefined}
        status="no-data"
        title="Error Title"
        subTitle="Objectively scale orthogonal collaboration and idea-sharing after enterprise-wide manufactured products."
        extra={[<Button>Cancel</Button>, <Button type="primary">OK</Button>]}
      />
    </Space>
  );
};

export default App;

```

### No Permission

```tsx
import { Result } from '@otakus/design';
import React from 'react';

const App: React.FC = () => {
  return (
    <div
      style={{
        width: '100%',
        height: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}
    >
      <div
        style={{
          width: '60%',
          maxWidth: 864,
          minWidth: 664,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '5vh'
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <div
            style={{
              fontSize: 48,
              fontWeight: 500,
              color: 'var(--otakus-color-text-heading)',
              lineHeight: '60px'
            }}
          >
            暂无权限
          </div>
          <div
            style={{
              fontSize: 28,
              fontWeight: 500,
              color: 'var(--otakus-color-text-heading)',
              lineHeight: '36px'
            }}
          >
            No Permission
          </div>
          <div
            style={{
              fontSize: 14,
              lineHeight: '22px',
              fontWeight: 400,
              color: 'var(--otakus-color-text-secondary)',
              marginTop: 24
            }}
          >
            抱歉，您没有权限访问此页面
          </div>
          <div
            style={{
              fontSize: 14,
              lineHeight: '22px',
              fontWeight: 400,
              color: 'var(--otakus-color-text-secondary)'
            }}
          >
            You do not have permission to view this page
          </div>
        </div>
        <Result status="no-permission" />
      </div>
    </div>
  );
};

export default App;

```

### Not Found

```tsx
import { Result } from '@otakus/design';
import React from 'react';

const App: React.FC = () => {
  return (
    <div
      style={{
        width: '100%',
        height: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}
    >
      <div
        style={{
          width: '60%',
          maxWidth: 864,
          minWidth: 664,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '5vh'
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <div
            style={{
              fontSize: 48,
              fontWeight: 500,
              color: 'var(--otakus-color-text-heading)',
              lineHeight: '60px'
            }}
          >
            404
          </div>
          <div
            style={{
              fontSize: 28,
              fontWeight: 500,
              color: 'var(--otakus-color-text-heading)',
              lineHeight: '36px'
            }}
          >
            Not Found
          </div>
          <div
            style={{
              fontSize: 14,
              lineHeight: '22px',
              fontWeight: 400,
              color: 'var(--otakus-color-text-secondary)',
              marginTop: 24
            }}
          >
            抱歉，您访问的页面不存在
          </div>
          <div
            style={{
              fontSize: 14,
              lineHeight: '22px',
              fontWeight: 400,
              color: 'var(--otakus-color-text-secondary)'
            }}
          >
            Sorry, the page you visited does not exist
          </div>
        </div>
        <Result status="not-found" />
      </div>
    </div>
  );
};

export default App;

```

