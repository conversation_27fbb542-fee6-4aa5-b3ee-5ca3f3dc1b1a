# Radio

## API

### 新增

新增 CardList Radio类型，只能在Radio.Group内使用。**不论是使用children还是options传递子元素，使用时都必须设置 `optionType="cardList"`**

#### Radio.Group

| 参数         | 说明                          | 类型                                | 默认值    | 版本   |
| ------------ | ----------------------------- | ----------------------------------- | --------- | ------ |
| optionType   | 用于设置 Radio `options` 类型 | `default` \| `button` \| `cardList` | `default` | 0.10.0 |
| cardListSize | cardList 卡片大小             | `default` \| `large`                | `default` | 0.10.0 |

#### Radio.CardList

继承Radio/Radio.Button的所有属性外，新增：

| 参数         | 说明              | 类型                 | 默认值    |
| ------------ | ----------------- | -------------------- | --------- |
| cardListSize | cardList 卡片大小 | `default` \| `large` | `default` |

通用属性参考：[通用属性](/components/common-props)

### Radio/Radio.Button


| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| autoFocus | 自动获取焦点 | boolean | false |
| checked | 指定当前是否选中 | boolean | false |
| defaultChecked | 初始是否选中 | boolean | false |
| disabled | 禁用 Radio | boolean | false |
| value | 根据 value 进行比较，判断是否选中 | any | - |

### Radio.Group

单选框组合，用于包裹一组 `Radio`。


| 参数 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| buttonStyle | RadioButton 的风格样式，目前有描边和填色两种风格 | `outline` \| `solid` | `outline` |  |  |
| defaultValue | 默认选中的值 | any | - |  |  |
| disabled | 禁选所有子单选器 | boolean | false |  |  |
| name | RadioGroup 下所有 `input[type="radio"]` 的 `name` 属性 | string | - |  |  |
| options | 以配置形式设置子元素 | string\[] \| number\[] \| Array&lt;[CheckboxOptionType](#checkboxoptiontype)> | - |  |  |
| optionType | 用于设置 Radio `options` 类型 | `default` \| `button` \| `cardList` | `default` | 0.10.0 |  |
| size | 大小，只对按钮样式生效 | `large` \| `middle` \| `small` | - |  |  |
| value | 用于设置当前选中的值 | any | - |  |  |
| onChange | 选项变化时的回调函数 | function(e:Event) | - |  |  |
| cardListSize | cardList 卡片大小             | `default` \| `large`                | `default` | 0.10.0 |

### CheckboxOptionType

| Property | Description                       | Type                                                                                                 | Default | Version |
| -------- | --------------------------------- | ---------------------------------------------------------------------------------------------------- | ------- | ------- |
| label    | 用于作为 Radio 选项展示的文本     | `ReactNode` (optionType="cardList"模式下还支持 `(checked: boolean, disabled: boolean) => ReactNode`) | -       | 4.4.0   |
| value    | 关联 Radio 选项的值               | `string` \| `number` \| `boolean`                                                                    | -       | 4.4.0   |
| style    | 应用到 Radio 选项的 style         | `React.CSSProperties`                                                                                | -       | 4.4.0   |
| disabled | 指定 Radio 选项是否要禁用         | `boolean`                                                                                            | `false` | 4.4.0   |
| title    | 添加 Title 属性值                 | `string`                                                                                             | -       | 4.4.0   |
| id       | 添加 Radio Id 属性值              | `string`                                                                                             | -       | 4.4.0   |
| onChange | 当 Radio Group 的值发送改变时触发 | `(e: CheckboxChangeEvent) => void;`                                                                  | -       | 4.4.0   |
| required | 指定 Radio 选项是否必填           | `boolean`                                                                                            | `false` | 4.4.0   |

## 示例

### Basic

```tsx
import React from 'react';
import { Radio } from '@otakus/design';

const App: React.FC = () => <Radio>Radio</Radio>;

export default App;

```

### disabled

```tsx
import React, { useState } from 'react';
import { Button, Radio } from '@otakus/design';

const App: React.FC = () => {
  const [disabled, setDisabled] = useState(true);

  const toggleDisabled = () => {
    setDisabled(!disabled);
  };

  return (
    <>
      <Radio defaultChecked={false} disabled={disabled}>
        Disabled
      </Radio>
      <Radio defaultChecked disabled={disabled}>
        Disabled
      </Radio>
      <br />
      <Button type="primary" onClick={toggleDisabled} style={{ marginTop: 16 }}>
        Toggle disabled
      </Button>
    </>
  );
};

export default App;

```

### Radio Group

```tsx
import React, { useState } from 'react';
import type { RadioChangeEvent } from '@otakus/design';
import { Radio } from '@otakus/design';

const App: React.FC = () => {
  const [value, setValue] = useState(1);

  const onChange = (e: RadioChangeEvent) => {
    console.log('radio checked', e.target.value);
    setValue(e.target.value);
  };

  return (
    <Radio.Group onChange={onChange} value={value}>
      <Radio value={1}>A</Radio>
      <Radio value={2}>B</Radio>
      <Radio value={3}>C</Radio>
      <Radio value={4}>D</Radio>
    </Radio.Group>
  );
};

export default App;

```

### Vertical Radio.Group

```tsx
import React, { useState } from 'react';
import type { RadioChangeEvent } from '@otakus/design';
import { Input, Radio, Space } from '@otakus/design';

const App: React.FC = () => {
  const [value, setValue] = useState(1);

  const onChange = (e: RadioChangeEvent) => {
    console.log('radio checked', e.target.value);
    setValue(e.target.value);
  };

  return (
    <Radio.Group onChange={onChange} value={value}>
      <Space direction="vertical">
        <Radio value={1}>Option A</Radio>
        <Radio value={2}>Option B</Radio>
        <Radio value={3}>Option C</Radio>
        <Radio value={4}>
          More...
          {value === 4 ? <Input style={{ width: 100, marginLeft: 10 }} /> : null}
        </Radio>
      </Space>
    </Radio.Group>
  );
};

export default App;

```

### Radio.Group group - optional

```tsx
import React, { useState } from 'react';
import type { RadioChangeEvent } from '@otakus/design';
import { Radio } from '@otakus/design';

const plainOptions = ['Apple', 'Pear', 'Orange'];
const options = [
  { label: 'Apple', value: 'Apple' },
  { label: 'Pear', value: 'Pear' },
  { label: 'Orange', value: 'Orange', title: 'Orange' }
];
const optionsWithDisabled = [
  { label: 'Apple', value: 'Apple' },
  { label: 'Pear', value: 'Pear' },
  { label: 'Orange', value: 'Orange', disabled: true }
];

const App: React.FC = () => {
  const [value1, setValue1] = useState('Apple');
  const [value2, setValue2] = useState('Apple');
  const [value3, setValue3] = useState('Apple');
  const [value4, setValue4] = useState('Apple');

  const onChange1 = ({ target: { value } }: RadioChangeEvent) => {
    console.log('radio1 checked', value);
    setValue1(value);
  };

  const onChange2 = ({ target: { value } }: RadioChangeEvent) => {
    console.log('radio2 checked', value);
    setValue2(value);
  };

  const onChange3 = ({ target: { value } }: RadioChangeEvent) => {
    console.log('radio3 checked', value);
    setValue3(value);
  };

  const onChange4 = ({ target: { value } }: RadioChangeEvent) => {
    console.log('radio4 checked', value);
    setValue4(value);
  };

  return (
    <>
      <Radio.Group options={plainOptions} onChange={onChange1} value={value1} />
      <br />
      <Radio.Group options={optionsWithDisabled} onChange={onChange2} value={value2} />
      <br />
      <br />
      <Radio.Group options={options} onChange={onChange3} value={value3} optionType="button" />
      <br />
      <br />
      <Radio.Group
        options={optionsWithDisabled}
        onChange={onChange4}
        value={value4}
        optionType="button"
        buttonStyle="solid"
      />
    </>
  );
};

export default App;

```

### radio style

```tsx
import React from 'react';
import type { RadioChangeEvent } from '@otakus/design';
import { Flex, Radio } from '@otakus/design';

const onChange = (e: RadioChangeEvent) => {
  console.log(`radio checked:${e.target.value}`);
};

const App: React.FC = () => (
  <Flex vertical gap="middle">
    <Radio.Group onChange={onChange} defaultValue="a">
      <Radio.Button value="a">Hangzhou</Radio.Button>
      <Radio.Button value="b">Shanghai</Radio.Button>
      <Radio.Button value="c">Beijing</Radio.Button>
      <Radio.Button value="d">Chengdu</Radio.Button>
    </Radio.Group>
    <Radio.Group onChange={onChange} defaultValue="a">
      <Radio.Button value="a">Hangzhou</Radio.Button>
      <Radio.Button value="b" disabled>
        Shanghai
      </Radio.Button>
      <Radio.Button value="c">Beijing</Radio.Button>
      <Radio.Button value="d">Chengdu</Radio.Button>
    </Radio.Group>
    <Radio.Group disabled onChange={onChange} defaultValue="a">
      <Radio.Button value="a">Hangzhou</Radio.Button>
      <Radio.Button value="b">Shanghai</Radio.Button>
      <Radio.Button value="c">Beijing</Radio.Button>
      <Radio.Button value="d">Chengdu</Radio.Button>
    </Radio.Group>
  </Flex>
);

export default App;

```

### Radio.Group with name

```tsx
import React from 'react';
import { Radio } from '@otakus/design';

const App: React.FC = () => (
  <Radio.Group name="radiogroup" defaultValue={1}>
    <Radio value={1}>A</Radio>
    <Radio value={2}>B</Radio>
    <Radio value={3}>C</Radio>
    <Radio value={4}>D</Radio>
  </Radio.Group>
);

export default App;

```

### Size

```tsx
import React from 'react';
import { Flex, Radio } from '@otakus/design';

const App: React.FC = () => (
  <Flex vertical gap="middle">
    <Radio.Group defaultValue="a" size="large">
      <Radio.Button value="a">Hangzhou</Radio.Button>
      <Radio.Button value="b">Shanghai</Radio.Button>
      <Radio.Button value="c">Beijing</Radio.Button>
      <Radio.Button value="d">Chengdu</Radio.Button>
    </Radio.Group>
    <Radio.Group defaultValue="a">
      <Radio.Button value="a">Hangzhou</Radio.Button>
      <Radio.Button value="b">Shanghai</Radio.Button>
      <Radio.Button value="c">Beijing</Radio.Button>
      <Radio.Button value="d">Chengdu</Radio.Button>
    </Radio.Group>
    <Radio.Group defaultValue="a" size="small">
      <Radio.Button value="a">Hangzhou</Radio.Button>
      <Radio.Button value="b">Shanghai</Radio.Button>
      <Radio.Button value="c">Beijing</Radio.Button>
      <Radio.Button value="d">Chengdu</Radio.Button>
    </Radio.Group>
  </Flex>
);

export default App;

```

### Solid radio button

```tsx
import React from 'react';
import { Flex, Radio } from '@otakus/design';

const App: React.FC = () => (
  <Flex vertical gap="middle">
    <Radio.Group defaultValue="a" buttonStyle="solid">
      <Radio.Button value="a">Hangzhou</Radio.Button>
      <Radio.Button value="b">Shanghai</Radio.Button>
      <Radio.Button value="c">Beijing</Radio.Button>
      <Radio.Button value="d">Chengdu</Radio.Button>
    </Radio.Group>
    <Radio.Group defaultValue="c" buttonStyle="solid">
      <Radio.Button value="a">Hangzhou</Radio.Button>
      <Radio.Button value="b" disabled>
        Shanghai
      </Radio.Button>
      <Radio.Button value="c">Beijing</Radio.Button>
      <Radio.Button value="d">Chengdu</Radio.Button>
    </Radio.Group>
  </Flex>
);

export default App;

```

### Radio CardList

```tsx
import React, { useState } from 'react';
import type { RadioChangeEvent } from '@otakus/design';
import { Radio, Switch } from '@otakus/design';

const optionsWithDisabled = [
  { label: 'Apple', value: 'Apple' },
  { label: 'Pear', value: 'Pear' },
  {
    label:
      '这是一个超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长的Option',
    value: 'Orange',
    disabled: true
  }
];

const App: React.FC = () => {
  const [value, setValue] = useState('Apple');
  const [isDefaultSize, setIsDefaultSize] = useState(true);

  const onChange = ({ target: { value } }: RadioChangeEvent) => {
    console.log('radio list checked', value);
    setValue(value);
  };

  return (
    <>
      <Switch
        checkedChildren="default"
        unCheckedChildren="large"
        value={isDefaultSize}
        onChange={setIsDefaultSize}
      />
      <br />
      <br />
      <Radio.Group
        options={optionsWithDisabled}
        onChange={onChange}
        value={value}
        optionType="cardList"
        cardListSize={isDefaultSize ? 'default' : 'large'}
      />
    </>
  );
};

export default App;

```

### 自定义Radio CardList

```tsx
import React, { useState } from 'react';
import type { RadioChangeEvent } from '@otakus/design';
import { Radio } from '@otakus/design';
import { ExclamationCircleOutlined } from '@otakus/icons';

const CustomLabel = ({
  number,
  title,
  checked
}: {
  number: number;
  title: string;
  checked: boolean;
}) => {
  return (
    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
      <div>
        {title}
        <span style={{ color: 'var(--otakus-color-text-description', paddingLeft: '4px' }}>
          ({number})
        </span>
      </div>
      {checked && (
        <ExclamationCircleOutlined style={{ color: 'var(--otakus-color-text-description' }} />
      )}
    </div>
  );
};

const optionsWithDisabled = [
  {
    label: (checked: boolean) => <CustomLabel title="Apple" number={10} checked={checked} />,
    value: 'Apple'
  },
  {
    label: (checked: boolean) => <CustomLabel title="Pear" number={11} checked={checked} />,
    value: 'Pear'
  },
  {
    label: (checked: boolean) => <CustomLabel title="Orange" number={12} checked={checked} />,
    value: 'Orange',
    disabled: true
  }
];

const App: React.FC = () => {
  const [value, setValue] = useState('Apple');

  const onChange = ({ target: { value } }: RadioChangeEvent) => {
    console.log('radio list checked', value);
    setValue(value);
  };

  return (
    <>
      <Radio.Group
        options={optionsWithDisabled}
        onChange={onChange}
        value={value}
        optionType="cardList"
      />
    </>
  );
};

export default App;

```

### Radio CardList + 虚拟列表

```tsx
import React, { useState } from 'react';
import type { RadioChangeEvent } from '@otakus/design';
import { Badge, Descriptions, Radio, Title } from '@otakus/design';
import VirtualList from 'rc-virtual-list';
import { ShowPerson } from '@otakus/person-render';

const CustomLabel = ({ title }: { title: string }) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title title={title}></Title>
        <Badge status="success" text="状态" />
      </div>
      <Descriptions
        column={1}
        items={Array(2)
          .fill(0)
          .map((_, i) => ({ key: i, label: '信息分类', children: '一串重要的信息' }))}
      />
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          color: 'var(--otakus-color-text)'
        }}
      >
        <ShowPerson
          languageType="zh-CN"
          info={[
            {
              cn_name: '何飞翔',
              avatar_url: 'https://wework.qpic.cn/wwpic3az/442175_9Ej2Be6kQnajGp6_1702535237/0',
              en_name: 'peter.H',
              domain: 'feixiang.he'
            }
          ]}
        />
        <div style={{ color: 'var(--otakus-color-text-description' }}>2025-05-01</div>
      </div>
    </div>
  );
};

const options = Array(1000)
  .fill(0)
  .map((_, i) => ({ key: i, value: i }));

const App: React.FC = () => {
  const [value, setValue] = useState(0);

  const onChange = ({ target: { value } }: RadioChangeEvent) => {
    console.log('radio list checked', value);
    setValue(value);
  };

  return (
    <>
      <Radio.Group onChange={onChange} value={value} optionType="cardList">
        <VirtualList data={options} height={400} itemHeight={158} itemKey="key">
          {(item) => (
            <Radio.CardList value={item.value}>
              <CustomLabel title={`明细${item.value}`} />
            </Radio.CardList>
          )}
        </VirtualList>
      </Radio.Group>
    </>
  );
};

export default App;

```

### Badge style

```tsx
import React from 'react';
import { Badge, Radio } from '@otakus/design';

const App: React.FC = () => (
  <Radio.Group buttonStyle="solid">
    <Badge count={1}>
      <Radio.Button value={1}>Click Me</Radio.Button>
    </Badge>
    <Badge count={2}>
      <Radio.Button value={2}>Not Me</Radio.Button>
    </Badge>
  </Radio.Group>
);

export default App;

```

### Wireframe

```tsx
import React from 'react';
import { ConfigProvider, Radio } from '@otakus/design';

const App: React.FC = () => (
  <ConfigProvider theme={{ token: { wireframe: true } }}>
    <Radio.Group value={1}>
      <Radio value={1}>A</Radio>
      <Radio value={2}>B</Radio>
      <Radio value={3}>C</Radio>
      <Radio value={4}>D</Radio>
    </Radio.Group>
    <br />
    <Radio.Group value={1} disabled>
      <Radio value={1}>A</Radio>
      <Radio value={2}>B</Radio>
      <Radio value={3}>C</Radio>
      <Radio value={4}>D</Radio>
    </Radio.Group>
  </ConfigProvider>
);

export default App;

```

### Component Token

```tsx
import React from 'react';
import { ConfigProvider, Radio, Space } from '@otakus/design';

const App: React.FC = () => (
  <ConfigProvider
    theme={{
      components: {
        Radio: {
          radioSize: 20,
          dotSize: 10,
          dotColorDisabled: 'grey',
          buttonBg: '#f6ffed',
          buttonCheckedBg: '#d9f7be',
          buttonColor: '#faad14',
          buttonPaddingInline: 20,
          buttonCheckedBgDisabled: '#fffbe6',
          buttonCheckedColorDisabled: '#ffe58f',
          buttonSolidCheckedColor: '#ffa39e',
          wrapperMarginInlineEnd: 20
        }
      }
    }}
  >
    <Space direction="vertical">
      <Radio checked>Test</Radio>
      <Radio checked disabled>
        Disabled
      </Radio>
      <Radio.Group defaultValue="a">
        <Radio.Button value="a">Hangzhou</Radio.Button>
        <Radio.Button value="b">Shanghai</Radio.Button>
        <Radio.Button value="c">Beijing</Radio.Button>
        <Radio.Button value="d">Chengdu</Radio.Button>
      </Radio.Group>
      <Radio.Group defaultValue="a" disabled>
        <Radio.Button value="a">Hangzhou</Radio.Button>
        <Radio.Button value="b">Shanghai</Radio.Button>
        <Radio.Button value="c">Beijing</Radio.Button>
        <Radio.Button value="d">Chengdu</Radio.Button>
      </Radio.Group>
      <Radio.Group defaultValue="a" buttonStyle="solid">
        <Radio.Button value="a">Hangzhou</Radio.Button>
        <Radio.Button value="b">Shanghai</Radio.Button>
        <Radio.Button value="c">Beijing</Radio.Button>
        <Radio.Button value="d">Chengdu</Radio.Button>
      </Radio.Group>
    </Space>
  </ConfigProvider>
);

export default App;

```

