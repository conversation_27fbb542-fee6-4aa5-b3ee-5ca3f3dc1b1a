# Anchor

## API

通用属性参考：[通用属性](/components/common-props)

### Anchor Props

| 参数             | 说明                                                                     | 类型                                                            | 默认值       | 版本  |
| ---------------- | ------------------------------------------------------------------------ | --------------------------------------------------------------- | ------------ | ----- |
| affix            | 固定模式                                                                 | boolean                                                         | true         |       |
| bounds           | 锚点区域边界                                                             | number                                                          | 5            |       |
| getContainer     | 指定滚动的容器                                                           | () => HTMLElement                                               | () => window |       |
| getCurrentAnchor | 自定义高亮的锚点                                                         | (activeLink: string) => string                                  | -            |       |
| offsetTop        | 距离窗口顶部达到指定偏移量后触发                                         | number                                                          |              |       |
| showInkInFixed   | `affix={false}` 时是否显示小方块                                         | boolean                                                         | false        |       |
| targetOffset     | 锚点滚动偏移量，默认与 offsetTop 相同，[例子](#anchor-demo-targetoffset) | number                                                          | -            |       |
| onChange         | 监听锚点链接改变                                                         | (currentActiveLink: string) => void                             | -            |       |
| onClick          | `click` 事件的 handler                                                   | (e: MouseEvent, link: object) => void                           | -            |       |
| items            | 数据化配置选项内容，支持通过 children 嵌套                               | { key, href, title, target, children }\[] [具体见](#anchoritem) | -            | 5.1.0 |
| direction        | 设置导航方向                                                             | `vertical` \| `horizontal`                                      | `vertical`   | 5.2.0 |
| replace          | 替换浏览器历史记录中项目的 href 而不是推送它                             | boolean                                                         | false        | 5.7.0 |

### AnchorItem

| 参数     | 说明                                             | 类型                         | 默认值 | 版本  |
| -------- | ------------------------------------------------ | ---------------------------- | ------ | ----- |
| key      | 唯一标志                                         | string \| number             | -      |       |
| href     | 锚点链接                                         | string                       | -      |       |
| target   | 该属性指定在何处显示链接的资源                   | string                       | -      |       |
| title    | 文字内容                                         | ReactNode                    | -      |       |
| children | 嵌套的 Anchor Link，`注意：水平方向该属性不支持` | [AnchorItem](#anchoritem)\[] | -      |       |
| replace  | 替换浏览器历史记录中的项目 href 而不是推送它     | boolean                      | false  | 5.7.0 |

### Link Props

建议使用 items 形式。

| 参数   | 说明                           | 类型      | 默认值 | 版本 |
| ------ | ------------------------------ | --------- | ------ | ---- |
| href   | 锚点链接                       | string    | -      |      |
| target | 该属性指定在何处显示链接的资源 | string    | -      |      |
| title  | 文字内容                       | ReactNode | -      |      |

## 示例

### Basic

```tsx
import React from 'react';
import { ThemeProvider, Anchor, Row, Col } from '@otakus/design';

const App: React.FC = () => (
  <ThemeProvider>
    <Row>
      <Col span={16}>
        <div id="part-1" style={{ height: '100vh', background: 'rgba(255,0,0,0.02)' }} />
        <div id="part-2" style={{ height: '100vh', background: 'rgba(0,255,0,0.02)' }} />
        <div id="part-3" style={{ height: '100vh', background: 'rgba(0,0,255,0.02)' }} />
      </Col>
      <Col span={8}>
        <Anchor
          items={[
            {
              key: 'part-1',
              href: '#part-1',
              title: 'Part 1'
            },
            {
              key: 'part-2',
              href: '#part-2',
              title: 'Part 2'
            },
            {
              key: 'part-3',
              href: '#part-3',
              title: 'Part 3'
            }
          ]}
        />
      </Col>
    </Row>
  </ThemeProvider>
);

export default App;

```

### Horizontal Anchor

```tsx
import React from 'react';
import { ThemeProvider, Anchor } from '@otakus/design';

const App: React.FC = () => (
  <ThemeProvider>
    <div style={{ padding: '20px' }}>
      <Anchor
        direction="horizontal"
        items={[
          {
            key: 'part-1',
            href: '#part-1',
            title: 'Part 1'
          },
          {
            key: 'part-2',
            href: '#part-2',
            title: 'Part 2'
          },
          {
            key: 'part-3',
            href: '#part-3',
            title: 'Part 3'
          }
        ]}
      />
    </div>
    <div>
      <div
        id="part-1"
        style={{
          width: '100vw',
          height: '100vh',
          textAlign: 'center',
          background: 'rgba(0,255,0,0.02)'
        }}
      />
      <div
        id="part-2"
        style={{
          width: '100vw',
          height: '100vh',
          textAlign: 'center',
          background: 'rgba(0,0,255,0.02)'
        }}
      />
      <div
        id="part-3"
        style={{ width: '100vw', height: '100vh', textAlign: 'center', background: '#FFFBE9' }}
      />
    </div>
  </ThemeProvider>
);

export default App;

```

### Static Anchor

```tsx
import React from 'react';
import { Anchor } from '@otakus/design';

const App: React.FC = () => (
  <Anchor
    affix={false}
    items={[
      {
        key: '1',
        href: '#components-anchor-demo-basic',
        title: 'Basic demo'
      },
      {
        key: '2',
        href: '#components-anchor-demo-static',
        title: 'Static demo'
      },
      {
        key: '3',
        href: '#api',
        title: 'API',
        children: [
          {
            key: '4',
            href: '#anchor-props',
            title: 'Anchor Props'
          },
          {
            key: '5',
            href: '#link-props',
            title: 'Link Props'
          }
        ]
      }
    ]}
  />
);

export default App;

```

### Customize the onClick event

```tsx
import React from 'react';
import { Anchor } from '@otakus/design';

const handleClick = (
  e: React.MouseEvent<HTMLElement>,
  link: {
    title: React.ReactNode;
    href: string;
  }
) => {
  e.preventDefault();
  console.log(link);
};

const App: React.FC = () => (
  <Anchor
    affix={false}
    onClick={handleClick}
    items={[
      {
        key: '1',
        href: '#components-anchor-demo-basic',
        title: 'Basic demo'
      },
      {
        key: '2',
        href: '#components-anchor-demo-static',
        title: 'Static demo'
      },
      {
        key: '3',
        href: '#api',
        title: 'API',
        children: [
          {
            key: '4',
            href: '#anchor-props',
            title: 'Anchor Props'
          },
          {
            key: '5',
            href: '#link-props',
            title: 'Link Props'
          }
        ]
      }
    ]}
  />
);

export default App;

```

### Customize the anchor highlight

```tsx
import React from 'react';
import { Anchor } from '@otakus/design';

const getCurrentAnchor = () => '#components-anchor-demo-static';

const App: React.FC = () => (
  <Anchor
    affix={false}
    getCurrentAnchor={getCurrentAnchor}
    items={[
      {
        key: '1',
        href: '#components-anchor-demo-basic',
        title: 'Basic demo'
      },
      {
        key: '2',
        href: '#components-anchor-demo-static',
        title: 'Static demo'
      },
      {
        key: '3',
        href: '#api',
        title: 'API',
        children: [
          {
            key: '4',
            href: '#anchor-props',
            title: 'Anchor Props'
          },
          {
            key: '5',
            href: '#link-props',
            title: 'Link Props'
          }
        ]
      }
    ]}
  />
);

export default App;

```

### Set Anchor scroll offset

```tsx
import React, { useEffect, useState } from 'react';
import { ThemeProvider, Anchor, Row, Col } from '@otakus/design';

const App: React.FC = () => {
  const topRef = React.useRef<HTMLDivElement>(null);
  const [targetOffset, setTargetOffset] = useState<number>();

  useEffect(() => {
    setTargetOffset(topRef.current?.clientHeight);
  }, []);

  return (
    <ThemeProvider>
      <Row>
        <Col span={18}>
          <div
            id="part-1"
            style={{ height: '100vh', background: 'rgba(255,0,0,0.02)', marginTop: '30vh' }}
          >
            Part 1
          </div>
          <div id="part-2" style={{ height: '100vh', background: 'rgba(0,255,0,0.02)' }}>
            Part 2
          </div>
          <div id="part-3" style={{ height: '100vh', background: 'rgba(0,0,255,0.02)' }}>
            Part 3
          </div>
        </Col>
        <Col span={6}>
          <Anchor
            targetOffset={targetOffset}
            items={[
              {
                key: 'part-1',
                href: '#part-1',
                title: 'Part 1'
              },
              {
                key: 'part-2',
                href: '#part-2',
                title: 'Part 2'
              },
              {
                key: 'part-3',
                href: '#part-3',
                title: 'Part 3'
              }
            ]}
          />
        </Col>
      </Row>

      <div
        style={{
          height: '30vh',
          background: 'rgba(0,0,0,0.85)',
          position: 'fixed',
          top: 0,
          left: 0,
          width: '75%',
          color: '#FFF'
        }}
        ref={topRef}
      >
        <div>Fixed Top Block</div>
      </div>
    </ThemeProvider>
  );
};

export default App;

```

### Listening for anchor link change

```tsx
import React from 'react';
import { Anchor } from '@otakus/design';

const onChange = (link: string) => {
  console.log('Anchor:OnChange', link);
};

const App: React.FC = () => (
  <Anchor
    affix={false}
    onChange={onChange}
    items={[
      {
        key: '1',
        href: '#components-anchor-demo-basic',
        title: 'Basic demo'
      },
      {
        key: '2',
        href: '#components-anchor-demo-static',
        title: 'Static demo'
      },
      {
        key: '3',
        href: '#api',
        title: 'API',
        children: [
          {
            key: '4',
            href: '#anchor-props',
            title: 'Anchor Props'
          },
          {
            key: '5',
            href: '#link-props',
            title: 'Link Props'
          }
        ]
      }
    ]}
  />
);

export default App;

```

### Replace href in history

```tsx
import React from 'react';
import { ThemeProvider, Anchor, Col, Row } from '@otakus/design';

const App: React.FC = () => (
  <ThemeProvider>
    <Row>
      <Col span={16}>
        <div id="part-1" style={{ height: '100vh', background: 'rgba(255,0,0,0.02)' }} />
        <div id="part-2" style={{ height: '100vh', background: 'rgba(0,255,0,0.02)' }} />
        <div id="part-3" style={{ height: '100vh', background: 'rgba(0,0,255,0.02)' }} />
      </Col>
      <Col span={8}>
        <Anchor
          replace
          items={[
            {
              key: 'part-1',
              href: '#part-1',
              title: 'Part 1'
            },
            {
              key: 'part-2',
              href: '#part-2',
              title: 'Part 2'
            },
            {
              key: 'part-3',
              href: '#part-3',
              title: 'Part 3'
            }
          ]}
        />
      </Col>
    </Row>
  </ThemeProvider>
);

export default App;

```

### Deprecated JSX demo

```tsx
import React from 'react';
import { Anchor } from '@otakus/design';

const { Link } = Anchor;

const App: React.FC = () => (
  <Anchor affix={false}>
    <Link href="#components-anchor-demo-basic" title="Basic demo" />
    <Link href="#components-anchor-demo-static" title="Static demo" />
    <Link href="#api" title="API">
      <Link href="#anchor-props" title="Anchor Props" />
      <Link href="#link-props" title="Link Props" />
    </Link>
  </Anchor>
);

export default App;

```

### Component Token

```tsx
import { Anchor, Col, ThemeProvider, ConfigProvider, Row } from '@otakus/design';
import React from 'react';

/** Test usage. Do not use in your production. */

export default () => (
  <ThemeProvider>
    <ConfigProvider
      theme={{
        components: {
          Anchor: {
            linkPaddingBlock: 100,
            linkPaddingInlineStart: 50
          }
        }
      }}
    >
      <Row>
        <Col span={16}>
          <div id="part-1" style={{ height: '100vh', background: 'rgba(255,0,0,0.02)' }} />
          <div id="part-2" style={{ height: '100vh', background: 'rgba(0,255,0,0.02)' }} />
          <div id="part-3" style={{ height: '100vh', background: 'rgba(0,0,255,0.02)' }} />
        </Col>
        <Col span={8}>
          <Anchor
            items={[
              {
                key: 'part-1',
                href: '#part-1',
                title: 'Part 1'
              },
              {
                key: 'part-2',
                href: '#part-2',
                title: 'Part 2'
              },
              {
                key: 'part-3',
                href: '#part-3',
                title: 'Part 3'
              }
            ]}
          />
        </Col>
      </Row>
    </ConfigProvider>
  </ThemeProvider>
);

```

