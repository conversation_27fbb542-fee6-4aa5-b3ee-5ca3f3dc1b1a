# Switch

## API

通用属性参考：[通用属性](/components/common-props)

| 参数              | 说明                                | 类型                                     | 默认值    | 版本   |
| ----------------- | ----------------------------------- | ---------------------------------------- | --------- | ------ |
| autoFocus         | 组件自动获取焦点                    | boolean                                  | false     |        |
| checked           | 指定当前是否选中                    | boolean                                  | false     |        |
| checkedChildren   | 选中时的内容                        | ReactNode                                | -         |        |
| className         | Switch 器类名                       | string                                   | -         |        |
| defaultChecked    | 初始是否选中                        | boolean                                  | false     |        |
| defaultValue      | `defaultChecked` 的别名             | boolean                                  | -         | 5.12.0 |
| disabled          | 是否禁用                            | boolean                                  | false     |        |
| loading           | 加载中的开关                        | boolean                                  | false     |        |
| size              | 开关大小，可选值：`default` `small` | string                                   | `default` |        |
| unCheckedChildren | 非选中时的内容                      | ReactNode                                | -         |        |
| value             | `checked` 的别名                    | boolean                                  | -         | 5.12.0 |
| onChange          | 变化时的回调函数                    | function(checked: boolean, event: Event) | -         |        |
| onClick           | 点击时的回调函数                    | function(checked: boolean, event: Event) | -         |        |

## 示例

### Basic

```tsx
import React from 'react';
import { Switch } from '@otakus/design';

const onChange = (checked: boolean) => {
  console.log(`switch to ${checked}`);
};

const App: React.FC = () => <Switch defaultChecked onChange={onChange} />;

export default App;

```

### Disabled

```tsx
import React, { useState } from 'react';
import { Button, Space, Switch } from '@otakus/design';

const App: React.FC = () => {
  const [disabled, setDisabled] = useState(true);

  const toggle = () => {
    setDisabled(!disabled);
  };

  return (
    <Space direction="vertical">
      <Switch disabled={disabled} defaultChecked />
      <Button type="primary" onClick={toggle}>
        Toggle disabled
      </Button>
    </Space>
  );
};

export default App;

```

### Text & icon

```tsx
import React from 'react';
import { CheckOutlined, CloseOutlined } from '@otakus/icons';
import { Switch, Space } from '@otakus/design';

const App: React.FC = () => (
  <Space direction="vertical">
    <Switch checkedChildren="开启" unCheckedChildren="关闭" defaultChecked />
    <Switch checkedChildren="1" unCheckedChildren="0" />
    <Switch
      checkedChildren={<CheckOutlined />}
      unCheckedChildren={<CloseOutlined />}
      defaultChecked
    />
  </Space>
);

export default App;

```

### Two sizes

```tsx
import React from 'react';
import { Switch } from '@otakus/design';

const App: React.FC = () => (
  <>
    <Switch defaultChecked />
    <br />
    <Switch size="small" defaultChecked />
  </>
);

export default App;

```

### Loading

```tsx
import React from 'react';
import { Switch } from '@otakus/design';

const App: React.FC = () => (
  <>
    <Switch loading defaultChecked />
    <br />
    <Switch size="small" loading />
  </>
);

export default App;

```

### Custom component token

```tsx
import React from 'react';
import { ConfigProvider, Space, Switch } from '@otakus/design';

const App: React.FC = () => (
  <ConfigProvider
    theme={{
      components: {
        Switch: {
          trackHeight: 14,
          trackMinWidth: 32,
          // opacityLoading: 0.1,
          colorPrimary: 'rgb(25, 118, 210, 0.5)',
          trackPadding: -3,
          handleSize: 20,
          handleBg: 'rgb(25, 118, 210)',
          handleShadow:
            'rgba(0, 0, 0, 0.2) 0px 2px 1px -1px, rgba(0, 0, 0, 0.14) 0px 1px 1px 0px, rgba(0, 0, 0, 0.12) 0px 1px 3px 0px'
          // innerMinMargin: 4,
          // innerMaxMargin: 8,
        }
      }
    }}
  >
    <Space>
      <Switch defaultChecked />
    </Space>
  </ConfigProvider>
);

export default App;

```

