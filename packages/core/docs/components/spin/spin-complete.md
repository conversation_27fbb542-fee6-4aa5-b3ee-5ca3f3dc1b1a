# Spin

## API

通用属性参考：[通用属性](/components/common-props)

| 参数             | 说明                                         | 类型          | 默认值    | 版本   |
| ---------------- | -------------------------------------------- | ------------- | --------- | ------ |
| delay            | 延迟显示加载效果的时间（防止闪烁）           | number (毫秒) | -         |
| indicator        | 加载指示符                                   | ReactNode     | -         |
| size             | 组件大小，可选值为 `small` `default` `large` | string        | `default` |
| spinning         | 是否为加载中状态                             | boolean       | true      |
| tip              | 当作为包裹元素时，可以自定义描述文案         | ReactNode     | -         |
| wrapperClassName | 包装器的类属性                               | string        | -         |
| fullscreen       | 显示带有 `Spin` 组件的背景                   | boolean       | false     | 5.11.0 |

### 静态方法

- `Spin.setDefaultIndicator(indicator: ReactNode)`

  你可以自定义全局默认 Spin 的元素。

## 示例

### Basic Usage

```tsx
import React from 'react';
import { Spin } from '@otakus/design';

const App: React.FC = () => <Spin />;

export default App;

```

### Size

```tsx
import React from 'react';
import { Flex, Spin } from '@otakus/design';

const App: React.FC = () => (
  <Flex align="center" gap="middle">
    <Spin size="small" />
    <Spin />
    <Spin size="large" />
  </Flex>
);

export default App;

```

### Inside a container

```tsx
import React from 'react';
import { Spin } from '@otakus/design';

const App: React.FC = () => (
  <div className="example">
    <Spin />
  </div>
);

export default App;

```

### Embedded mode

```tsx
import React from 'react';
import { Alert, Spin, Switch } from '@otakus/design';

const App: React.FC = () => {
  const [loading, setLoading] = React.useState<boolean>(false);
  return (
    <>
      <Spin spinning={loading}>
        <Alert
          type="info"
          message="Alert message title"
          description="Further details about the context of this alert."
        />
      </Spin>
      <div style={{ marginTop: 16 }}>
        Loading state：
        <Switch checked={loading} onChange={setLoading} />
      </div>
    </>
  );
};

export default App;

```

### Customized description

```tsx
import React from 'react';
import { Alert, Flex, Spin } from '@otakus/design';

const contentStyle: React.CSSProperties = {
  padding: 50,
  background: 'rgba(0, 0, 0, 0.05)',
  borderRadius: 4
};

const content = <div style={contentStyle} />;

const App: React.FC = () => (
  <Flex gap="small" vertical>
    <Flex gap="small">
      <Spin tip="Loading" size="small">
        {content}
      </Spin>
      <Spin tip="Loading">{content}</Spin>
      <Spin tip="Loading" size="large">
        {content}
      </Spin>
    </Flex>
    <Spin tip="Loading...">
      <Alert
        message="Alert message title"
        description="Further details about the context of this alert."
        type="info"
      />
    </Spin>
  </Flex>
);

export default App;

```

### Delay

```tsx
import React from 'react';
import { Alert, Spin, Switch } from '@otakus/design';

const App: React.FC = () => {
  const [loading, setLoading] = React.useState<boolean>(false);
  return (
    <>
      <Spin spinning={loading} delay={500}>
        <Alert
          type="info"
          message="Alert message title"
          description="Further details about the context of this alert."
        />
      </Spin>
      <div style={{ marginTop: 16 }}>
        Loading state：
        <Switch checked={loading} onChange={setLoading} />
      </div>
    </>
  );
};

export default App;

```

### Custom spinning indicator

```tsx
import React from 'react';
import { LoadingOutlined } from '@otakus/icons';
import { Spin } from '@otakus/design';

const App: React.FC = () => <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />;

export default App;

```

### Fullscreen

```tsx
import React from 'react';
import { Button, Spin } from '@otakus/design';

const App: React.FC = () => {
  const [spinning, setSpinning] = React.useState<boolean>(false);

  const showLoader = () => {
    setSpinning(true);
    setTimeout(() => {
      setSpinning(false);
    }, 3000);
  };

  return (
    <>
      <Button onClick={showLoader}>Show fullscreen for 3s</Button>
      <Spin spinning={spinning} fullscreen />
    </>
  );
};

export default App;

```

