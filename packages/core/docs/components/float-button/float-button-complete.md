# FloatButton

## API

通用属性参考：[通用属性](/components/common-props)

> 自 `antd@5.0.0` 版本开始提供该组件。

### 共同的 API

| 参数        | 说明                                                  | 类型                                | 默认值    | 版本  |
| ----------- | ----------------------------------------------------- | ----------------------------------- | --------- | ----- |
| icon        | 自定义图标                                            | ReactNode                           | -         |       |
| description | 文字及其它内容                                        | ReactNode                           | -         |       |
| tooltip     | 气泡卡片的内容                                        | ReactNode \| () => ReactNode        | -         |       |
| type        | 设置按钮类型                                          | `default` \| `primary`              | `default` |       |
| shape       | 设置按钮形状                                          | `circle` \| `square`                | `circle`  |       |
| onClick     | 点击按钮时的回调                                      | (event) => void                     | -         |       |
| href        | 点击跳转的地址，指定此属性 button 的行为和 a 链接一致 | string                              | -         |       |
| target      | 相当于 a 标签的 target 属性，href 存在时生效          | string                              | -         |       |
| badge       | 带徽标数字的悬浮按钮（不支持 `status` 以及相关属性）  | [BadgeProps](/components/badge#api) | -         | 5.4.0 |

### FloatButton.Group

| 参数         | 说明                                      | 类型                    | 默认值              | 版本 |
| ------------ | ----------------------------------------- | ----------------------- | ------------------- | ---- |
| shape        | 设置包含的 FloatButton 按钮形状           | `circle` \| `square`    | `circle`            |      |
| trigger      | 触发方式（有触发方式为菜单模式）          | `click` \| `hover`      | -                   |      |
| open         | 受控展开，需配合 trigger 一起使用         | boolean                 | -                   |      |
| closeIcon    | 自定义关闭按钮                            | React.ReactNode         | `<CloseOutlined />` |      |
| onOpenChange | 展开收起时的回调，需配合 trigger 一起使用 | (open: boolean) => void | -                   |      |

### FloatButton.BackTop

| 参数             | 说明                               | 类型              | 默认值       | 版本 |
| ---------------- | ---------------------------------- | ----------------- | ------------ | ---- |
| duration         | 回到顶部所需时间（ms）             | number            | 450          |      |
| target           | 设置需要监听其滚动事件的元素       | () => HTMLElement | () => window |      |
| visibilityHeight | 滚动高度达到此参数值才出现 BackTop | number            | 400          |      |
| onClick          | 点击按钮的回调函数                 | () => void        | -            |      |

## 示例

### Basic

```tsx
import React from 'react';
import { FloatButton, ThemeProvider } from '@otakus/design';

const App: React.FC = () => (
  <ThemeProvider>
    <FloatButton onClick={() => console.log('onClick')} />
  </ThemeProvider>
);

export default App;

```

### Type

```tsx
import React from 'react';
import { QuestionCircleOutlined } from '@otakus/icons';
import { FloatButton, ThemeProvider } from '@otakus/design';

const App: React.FC = () => (
  <ThemeProvider>
    <FloatButton icon={<QuestionCircleOutlined />} type="primary" style={{ right: 24 }} />
    <FloatButton icon={<QuestionCircleOutlined />} type="default" style={{ right: 94 }} />
  </ThemeProvider>
);

export default App;

```

### Shape

```tsx
import React from 'react';
import { SettingOutlined } from '@otakus/icons';
import { FloatButton, ThemeProvider } from '@otakus/design';

const App: React.FC = () => (
  <ThemeProvider>
    <FloatButton shape="circle" type="primary" style={{ right: 94 }} icon={<SettingOutlined />} />
    <FloatButton shape="square" type="primary" style={{ right: 24 }} icon={<SettingOutlined />} />
  </ThemeProvider>
);

export default App;

```

### Description

```tsx
import React from 'react';
import { FileOutlined } from '@otakus/icons';
import { FloatButton, ThemeProvider } from '@otakus/design';

const App: React.FC = () => (
  <ThemeProvider>
    <FloatButton
      icon={<FileOutlined />}
      description="HELP INFO"
      shape="square"
      style={{ right: 24 }}
    />
    <FloatButton description="HELP INFO" shape="square" style={{ right: 94 }} />
    <FloatButton icon={<FileOutlined />} description="HELP" shape="square" style={{ right: 164 }} />
  </ThemeProvider>
);

export default App;

```

### FloatButton with tooltip

```tsx
import React from 'react';
import { FloatButton, ThemeProvider } from '@otakus/design';

const App: React.FC = () => (
  <ThemeProvider>
    <FloatButton tooltip={<div>Documents</div>} />
  </ThemeProvider>
);

export default App;

```

### FloatButton Group

```tsx
import React from 'react';
import { QuestionCircleOutlined, SyncOutlined } from '@otakus/icons';
import { FloatButton, ThemeProvider } from '@otakus/design';

const App: React.FC = () => (
  <ThemeProvider>
    <FloatButton.Group shape="circle" style={{ right: 24 }}>
      <FloatButton icon={<QuestionCircleOutlined />} />
      <FloatButton />
      <FloatButton.BackTop visibilityHeight={0} />
    </FloatButton.Group>
    <FloatButton.Group shape="square" style={{ right: 94 }}>
      <FloatButton icon={<QuestionCircleOutlined />} />
      <FloatButton />
      <FloatButton icon={<SyncOutlined />} />
      <FloatButton.BackTop visibilityHeight={0} />
    </FloatButton.Group>
  </ThemeProvider>
);

export default App;

```

### Menu mode

```tsx
import { CommentOutlined, SettingOutlined } from '@otakus/icons';
import React from 'react';
import { FloatButton, ThemeProvider } from '@otakus/design';

const App: React.FC = () => (
  <ThemeProvider>
    <FloatButton.Group
      trigger="click"
      type="primary"
      style={{ right: 24 }}
      icon={<SettingOutlined />}
    >
      <FloatButton />
      <FloatButton icon={<CommentOutlined />} />
    </FloatButton.Group>
    <FloatButton.Group
      trigger="hover"
      type="primary"
      style={{ right: 94 }}
      icon={<SettingOutlined />}
    >
      <FloatButton />
      <FloatButton icon={<CommentOutlined />} />
    </FloatButton.Group>
  </ThemeProvider>
);

export default App;

```

### Controlled mode

```tsx
import { CommentOutlined, SettingOutlined } from '@otakus/icons';
import React, { useState } from 'react';
import { FloatButton, Switch, ThemeProvider } from '@otakus/design';

const App: React.FC = () => {
  const [open, setOpen] = useState(true);

  const onChange = (checked: boolean) => {
    setOpen(checked);
  };

  return (
    <ThemeProvider>
      <FloatButton.Group
        open={open}
        trigger="click"
        style={{ right: 24 }}
        icon={<SettingOutlined />}
      >
        <FloatButton />
        <FloatButton icon={<CommentOutlined />} />
      </FloatButton.Group>
      <Switch onChange={onChange} checked={open} style={{ margin: 16 }} />
    </ThemeProvider>
  );
};

export default App;

```

### BackTop

```tsx
import React from 'react';
import { FloatButton, ThemeProvider } from '@otakus/design';

const App: React.FC = () => (
  <div style={{ height: '300vh', padding: 10 }}>
    <ThemeProvider>
      <div>Scroll to bottom</div>
      <div>Scroll to bottom</div>
      <div>Scroll to bottom</div>
      <div>Scroll to bottom</div>
      <div>Scroll to bottom</div>
      <div>Scroll to bottom</div>
      <div>Scroll to bottom</div>
      <FloatButton.BackTop />
    </ThemeProvider>
  </div>
);

export default App;

```

### badge

```tsx
import { QuestionCircleOutlined } from '@otakus/icons';
import React from 'react';
import { FloatButton, ThemeProvider } from '@otakus/design';

const App: React.FC = () => (
  <ThemeProvider>
    <FloatButton shape="circle" badge={{ dot: true }} style={{ right: 24 + 70 + 70 }} />
    <FloatButton.Group shape="circle" style={{ right: 24 + 70 }}>
      <FloatButton
        href="https://ant.design/index-cn"
        tooltip={<div>custom badge color</div>}
        badge={{ count: 5, color: 'blue' }}
      />
      <FloatButton badge={{ count: 5 }} />
    </FloatButton.Group>
    <FloatButton.Group shape="circle">
      <FloatButton badge={{ count: 12 }} icon={<QuestionCircleOutlined />} />
      <FloatButton badge={{ count: 123, overflowCount: 999 }} />
      <FloatButton.BackTop visibilityHeight={0} />
    </FloatButton.Group>
  </ThemeProvider>
);

export default App;

```

