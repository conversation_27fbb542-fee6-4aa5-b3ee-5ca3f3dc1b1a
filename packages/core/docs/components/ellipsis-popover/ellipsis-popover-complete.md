# EllipsisPopover

当文本内容过长时，显示省略号并提供完整内容的提示框组件。

## 使用示例

### 基础用法

### 自定义最大长度

### 富文本内容

### 多行文本

## API

### EllipsisPopover

| 参数          | 说明                     | 类型            | 默认值 |
| ------------- | ------------------------ | --------------- | ------ |
| content       | 提示框内容               | string          | -      |
| maxTextLength | 最大显示文本长度         | number          | 100    |
| children      | 触发提示框的元素         | React.ReactNode | -      |
| copyContent   | 复制内容，默认是 content | string          |

除了以上属性，组件还支持所有 [Popover](./popover) 组件的属性。

## 注意事项

1. 当 `content` 为字符串类型且长度超过 `maxTextLength` 时，会自动显示"复制全部"按钮
2. 组件内部使用了 `dangerouslySetInnerHTML` 来渲染 HTML 内容，请确保传入的内容是安全的
3. 组件继承自 Popover 组件，支持所有 Popover 的属性配置

## 示例

### basic

```tsx
import React from 'react';
import { EllipsisPopover } from '@otakus/design';

const Demo = () => {
  const longText =
    '这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。';

  return (
    <EllipsisPopover content={longText}>
      <div
        style={{ width: 200, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
      >
        {longText}
      </div>
    </EllipsisPopover>
  );
};

export default Demo;

```

### max-length

```tsx
import React from 'react';
import { EllipsisPopover } from '@otakus/design';

const Demo = () => {
  const longText =
    '这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。这是底部';

  return (
    <EllipsisPopover content={longText} maxTextLength={50}>
      <div
        style={{ width: 200, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
      >
        {longText}
      </div>
    </EllipsisPopover>
  );
};

export default Demo;

```

### rich-text

```tsx
import React from 'react';
import { EllipsisPopover } from '@otakus/design';

const Demo = () => {
  const richContent = `
    <div>
      <h3>标题</h3>
      <p>这是一段<strong>富文本</strong>内容，支持<em>HTML</em>标签。</p>
      <ul>
        <li>列表项 1</li>
        <li>列表项 2</li>
      </ul>
    </div>
  `;

  return (
    <EllipsisPopover content={richContent}>
      <div
        style={{ width: 200, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
      >
        <div dangerouslySetInnerHTML={{ __html: richContent }} />
      </div>
    </EllipsisPopover>
  );
};

export default Demo;

```

### multi-line

```tsx
import React, { useState } from 'react';
import { EllipsisPopover } from '@otakus/design';

const Demo = () => {
  const [text, setText] = useState(
    `这是一段很长的文本内容，当文本内容超过最大长度限制时，会显示省略号，并且可以通过提示框查看完整内容。\n这是第二段内容。\n这是第三段内容。`
  );

  return (
    <div>
      <textarea
        value={text}
        onChange={(e) => setText(e.target.value)}
        style={{
          width: '100%',
          height: '100px',
          marginBottom: '16px',
          padding: '8px',
          border: '1px solid #d9d9d9',
          borderRadius: '4px'
        }}
      />
      <EllipsisPopover content={text}>
        <div
          style={{
            width: 200,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            lineHeight: 1.5
          }}
        >
          {text}
        </div>
      </EllipsisPopover>
    </div>
  );
};

export default Demo;

```

