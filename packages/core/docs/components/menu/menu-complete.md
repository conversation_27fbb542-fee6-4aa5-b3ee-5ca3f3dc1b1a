# Menu

## API

不建议通过 `Menu.theme` 单独设置菜单 light/dark，EEE 范围内设计出稿不会出现整站 light 主题模式下单独使用 dark Menu 的情况。

通用属性参考：[通用属性](/components/common-props)

### Menu

| 参数                 | 说明                                           | 类型                                                                       | 默认值                 | 版本   |
| -------------------- | ---------------------------------------------- | -------------------------------------------------------------------------- | ---------------------- | ------ |
| defaultOpenKeys      | 初始展开的 SubMenu 菜单项 key 数组             | string\[]                                                                  | -                      |        |
| defaultSelectedKeys  | 初始选中的菜单项 key 数组                      | string\[]                                                                  | -                      |        |
| expandIcon           | 自定义展开图标                                 | ReactNode \| `(props: SubMenuProps & { isSubMenu: boolean }) => ReactNode` | -                      | 4.9.0  |
| forceSubMenuRender   | 在子菜单展示之前就渲染进 DOM                   | boolean                                                                    | false                  |        |
| inlineCollapsed      | inline 时菜单是否收起状态                      | boolean                                                                    | -                      |        |
| inlineIndent         | inline 模式的菜单缩进宽度                      | number                                                                     | 24                     |        |
| items                | 菜单内容                                       | [ItemType\[\]](#itemtype)                                                  | -                      | 4.20.0 |
| mode                 | 菜单类型，现在支持垂直、水平、和内嵌模式三种   | `vertical` \| `horizontal` \| `inline`                                     | `vertical`             |        |
| multiple             | 是否允许多选                                   | boolean                                                                    | false                  |        |
| openKeys             | 当前展开的 SubMenu 菜单项 key 数组             | string\[]                                                                  | -                      |        |
| overflowedIndicator  | 用于自定义 Menu 水平空间不足时的省略收缩的图标 | ReactNode                                                                  | `<EllipsisOutlined />` |        |
| selectable           | 是否允许选中                                   | boolean                                                                    | true                   |        |
| selectedKeys         | 当前选中的菜单项 key 数组                      | string\[]                                                                  | -                      |        |
| style                | 根节点样式                                     | CSSProperties                                                              | -                      |        |
| subMenuCloseDelay    | 用户鼠标离开子菜单后关闭延时，单位：秒         | number                                                                     | 0.1                    |        |
| subMenuOpenDelay     | 用户鼠标进入子菜单后开启延时，单位：秒         | number                                                                     | 0                      |        |
| theme                | 主题颜色                                       | `light` \| `dark`                                                          | `light`                |        |
| triggerSubMenuAction | SubMenu 展开/关闭的触发行为                    | `hover` \| `click`                                                         | `hover`                |        |
| onClick              | 点击 MenuItem 调用此函数                       | function({ item, key, keyPath, domEvent })                                 | -                      |        |
| onDeselect           | 取消选中时调用，仅在 multiple 生效             | function({ item, key, keyPath, selectedKeys, domEvent })                   | -                      |        |
| onOpenChange         | SubMenu 展开/关闭的回调                        | function(openKeys: string\[])                                              | -                      |        |
| onSelect             | 被选中时调用                                   | function({ item, key, keyPath, selectedKeys, domEvent })                   | -                      |        |

> 更多属性查看 [rc-menu](https://github.com/react-component/menu#api)

### ItemType

> type ItemType = [MenuItemType](#menuitemtype) | [SubMenuType](#submenutype) | [MenuItemGroupType](#menuItemgrouptype) | [MenuDividerType](#menudividertype);

#### MenuItemType

| 参数     | 说明                     | 类型      | 默认值 | 版本 |
| -------- | ------------------------ | --------- | ------ | ---- |
| danger   | 展示错误状态样式         | boolean   | false  |      |
| disabled | 是否禁用                 | boolean   | false  |      |
| icon     | 菜单图标                 | ReactNode | -      |      |
| key      | item 的唯一标志          | string    | -      |      |
| label    | 菜单项标题               | ReactNode | -      |      |
| title    | 设置收缩时展示的悬浮标题 | string    | -      |      |

#### SubMenuType

| 参数           | 说明                                 | 类型                        | 默认值 | 版本 |
| -------------- | ------------------------------------ | --------------------------- | ------ | ---- |
| children       | 子菜单的菜单项                       | [ItemType\[\]](#itemtype)   | -      |      |
| disabled       | 是否禁用                             | boolean                     | false  |      |
| icon           | 菜单图标                             | ReactNode                   | -      |      |
| key            | 唯一标志                             | string                      | -      |      |
| label          | 菜单项标题                           | ReactNode                   | -      |      |
| popupClassName | 子菜单样式，`mode="inline"` 时无效   | string                      | -      |      |
| popupOffset    | 子菜单偏移量，`mode="inline"` 时无效 | \[number, number]           | -      |      |
| onTitleClick   | 点击子菜单标题                       | function({ key, domEvent }) | -      |      |
| theme          | 设置子菜单的主题，默认从 Menu 上继承 | `light` \| `dark`           | -      |      |

#### MenuItemGroupType

定义类型为 `group` 时，会作为分组处理:

```ts
const groupItem = {
  type: 'group', // Must have
  label: 'My Group',
  children: []
};
```

| 参数     | 说明         | 类型                              | 默认值 | 版本 |
| -------- | ------------ | --------------------------------- | ------ | ---- |
| children | 分组的菜单项 | [MenuItemType\[\]](#menuitemtype) | -      |      |
| label    | 分组标题     | ReactNode                         | -      |      |

#### MenuDividerType

菜单项分割线，只用在弹出菜单内，需要定义类型为 `divider`：

```ts
const dividerItem = {
  type: 'divider' // Must have
};
```

| 参数   | 说明     | 类型    | 默认值 | 版本 |
| ------ | -------- | ------- | ------ | ---- |
| dashed | 是否虚线 | boolean | false  |      |

### BadgeItem

用于展示带有数字徽标的菜单项。

| 参数      | 说明                     | 类型      | 默认值 | 版本  |
| --------- | ------------------------ | --------- | ------ | ----- |
| count     | 徽标数字                 | number    | -      | 0.9.0 |
| collapsed | 是否处于折叠状态         | boolean   | -      | 0.9.0 |
| icon      | 菜单图标                 | ReactNode | -      | 0.9.0 |
| key       | item 的唯一标志          | string    | -      | 0.9.0 |
| children  | 菜单项标题               | ReactNode | -      | 0.9.0 |
| title     | 设置收缩时展示的悬浮标题 | string    | -      | 0.9.0 |

### RedirectItem

用于展示带有外部链接图标的菜单项。

| 参数     | 说明                     | 类型      | 默认值    | 版本  |
| -------- | ------------------------ | --------- | --------- | ----- |
| url      | 跳转链接                 | string    | -         | 0.9.0 |
| target   | 链接打开方式             | string    | '\_blank' | 0.9.0 |
| icon     | 菜单图标                 | ReactNode | -         | 0.9.0 |
| key      | item 的唯一标志          | string    | -         | 0.9.0 |
| children | 菜单项标题               | ReactNode | -         | 0.9.0 |
| title    | 设置收缩时展示的悬浮标题 | string    | -         | 0.9.0 |

## 示例

### horizontal

```tsx
/**
 * title: 顶部导航
 * description: 水平的顶部导航菜单。
 */

import { ConfigProvider, Menu } from '@otakus/design';
import React, { useState } from 'react';
import { AppstoreOutlined, MailOutlined, SettingOutlined } from '@otakus/icons';
import type { MenuProps } from '@otakus/design';

const items: MenuProps['items'] = [
  {
    label: '一级标题一',
    key: 'mail',
    icon: <MailOutlined />
  },
  {
    label: '一级标题二',
    key: 'app',
    icon: <AppstoreOutlined />,
    disabled: true
  },
  {
    label: '一级标题三 - 子菜单',
    key: 'SubMenu',
    icon: <SettingOutlined />,
    children: [
      {
        type: 'group',
        label: 'Item 1',
        children: [
          {
            label: '菜单项 1',
            key: 'setting:1'
          },
          {
            label: '菜单项 2',
            key: 'setting:2'
          }
        ]
      },
      {
        type: 'group',
        label: 'Item 2',
        children: [
          {
            label: '菜单项 3',
            key: 'setting:3'
          },
          {
            label: '菜单项 4',
            key: 'setting:4'
          }
        ]
      }
    ]
  },
  {
    label: (
      <a href="https://ant.design" target="_blank" rel="noopener noreferrer">
        一级标题四 - 链接
      </a>
    ),
    key: 'alipay'
  }
];

const App: React.FC = () => {
  const [current, setCurrent] = useState('mail');

  const onClick: MenuProps['onClick'] = (e) => {
    console.log('click ', e);
    setCurrent(e.key);
  };

  return (
    <ConfigProvider>
      <Menu onClick={onClick} selectedKeys={[current]} mode="horizontal" items={items} />
    </ConfigProvider>
  );
};

export default App;

```

### inline

```tsx
/**
 * title: 内嵌菜单
 * description: 垂直菜单，子菜单内嵌在菜单区域。
 */

import React from 'react';
import { AppstoreOutlined, MailOutlined, SettingOutlined } from '@otakus/icons';
import type { MenuProps } from '@otakus/design';
import { Menu } from '@otakus/design';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[],
  type?: 'group'
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
    type
  } as MenuItem;
}

const items: MenuProps['items'] = [
  getItem('一级标题一', 'sub1', <MailOutlined />, [
    getItem('菜单项 1', 'g1', null, [getItem('菜单项 1', '1'), getItem('菜单项 2', '2')], 'group'),
    getItem('菜单项 2', 'g2', null, [getItem('菜单项 3', '3'), getItem('菜单项 4', '4')], 'group')
  ]),

  getItem('一级标题二', 'sub2', <AppstoreOutlined />, [
    getItem('菜单项 5', '5'),
    getItem('菜单项 6', '6'),
    getItem('子菜单', 'sub3', null, [getItem('菜单项 7', '7'), getItem('菜单项 8', '8')])
  ]),

  getItem('一级标题三', 'sub4', <SettingOutlined />, [
    getItem('菜单项 9', '9'),
    getItem('菜单项 10', '10'),
    getItem('菜单项 11', '11'),
    getItem('菜单项 12', '12')
  ]),

  getItem('Group', 'grp', null, [getItem('菜单项 13', '13'), getItem('菜单项 14', '14')], 'group')
];

const App: React.FC = () => {
  const onClick: MenuProps['onClick'] = (e) => {
    console.log('click ', e);
  };

  return (
    <Menu
      onClick={onClick}
      style={{ width: 256 }}
      defaultSelectedKeys={['1']}
      defaultOpenKeys={['sub1']}
      mode="inline"
      items={items}
    />
  );
};

export default App;

```

### custom-item

```tsx
/**
 * title: 自定义菜单项
 * description: 自定义菜单项。目前支持 `Menu.BadgeItem` 和 `Menu.RedirectItem` 的扩展。
 */

import React, { useState } from 'react';
import {
  HomeOutlined,
  DesktopOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PieChartOutlined,
  AppstoreOutlined
} from '@otakus/icons';
import { Button, Menu } from '@otakus/design';

const App: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  return (
    <div style={{ width: 256 }}>
      <Button type="primary" onClick={toggleCollapsed} style={{ marginBottom: 16 }}>
        {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
      </Button>
      <Menu
        defaultSelectedKeys={['1']}
        defaultOpenKeys={['sub1']}
        mode="inline"
        inlineCollapsed={collapsed}
      >
        <Menu.BadgeItem count={10} key="1" icon={<PieChartOutlined />} collapsed={collapsed}>
          菜单项 1
        </Menu.BadgeItem>
        <Menu.BadgeItem count={99} key="2" icon={<DesktopOutlined />} collapsed={collapsed}>
          菜单项 2
        </Menu.BadgeItem>
        <Menu.BadgeItem count={8} key="3" icon={<HomeOutlined />} collapsed={collapsed}>
          菜单项 3
        </Menu.BadgeItem>
        <Menu.RedirectItem url="https://otakus.mihoyo.com" key="4" icon={<AppstoreOutlined />}>
          菜单项 4
        </Menu.RedirectItem>
      </Menu>
    </div>
  );
};

export default App;

```

### inline-collapsed

```tsx
/**
 * title: 缩起内嵌菜单
 * description: 内嵌菜单可以被缩起/展开。你可以在 Layout 里查看侧边布局结合的完整示例。
 */

import React, { useState } from 'react';
import {
  AppstoreOutlined,
  HomeOutlined,
  DesktopOutlined,
  MailOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PieChartOutlined
} from '@otakus/icons';
import type { MenuProps } from '@otakus/design';
import { Button, Menu } from '@otakus/design';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[],
  type?: 'group'
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
    type
  } as MenuItem;
}

const items: MenuItem[] = [
  getItem('菜单项 1', '1', <PieChartOutlined />),
  getItem('菜单项 2', '2', <DesktopOutlined />),
  getItem('菜单项 3', '3', <HomeOutlined />),

  getItem('一级标题一', 'sub1', <MailOutlined />, [
    getItem('菜单项 5', '5'),
    getItem('菜单项 6', '6'),
    getItem('菜单项 7', '7'),
    getItem('菜单项 8', '8')
  ]),

  getItem('一级标题二', 'sub2', <AppstoreOutlined />, [
    getItem('菜单项 9', '9'),
    getItem('菜单项 10', '10'),

    getItem('子菜单', 'sub3', null, [getItem('菜单项 11', '11'), getItem('菜单项 12', '12')])
  ])
];

const App: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  return (
    <div style={{ width: 256 }}>
      <Button type="primary" onClick={toggleCollapsed} style={{ marginBottom: 16 }}>
        {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
      </Button>
      <Menu
        defaultSelectedKeys={['1']}
        defaultOpenKeys={['sub1']}
        mode="inline"
        inlineCollapsed={collapsed}
        items={items}
      />
    </div>
  );
};

export default App;

```

### sider-current

```tsx
/**
 * title: 只展开当前父级菜单
 * description: 点击菜单，收起其他展开的所有菜单，保持菜单聚焦简洁。
 */

import React, { useState } from 'react';
import { AppstoreOutlined, MailOutlined, SettingOutlined } from '@otakus/icons';
import type { MenuProps } from '@otakus/design';
import { Menu } from '@otakus/design';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[],
  type?: 'group'
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
    type
  } as MenuItem;
}

const items: MenuItem[] = [
  getItem('一级标题一', 'sub1', <MailOutlined />, [
    getItem('菜单项 1', '1'),
    getItem('菜单项 2', '2'),
    getItem('菜单项 3', '3'),
    getItem('菜单项 4', '4')
  ]),
  getItem('一级标题二', 'sub2', <AppstoreOutlined />, [
    getItem('菜单项 5', '5'),
    getItem('菜单项 6', '6'),
    getItem('子菜单', 'sub3', null, [getItem('菜单项 7', '7'), getItem('菜单项 8', '8')])
  ]),
  getItem('一级标题三', 'sub4', <SettingOutlined />, [
    getItem('菜单项 9', '9'),
    getItem('菜单项 10', '10'),
    getItem('菜单项 11', '11'),
    getItem('菜单项 12', '12')
  ])
];

// submenu keys of first level
const rootSubmenuKeys = ['sub1', 'sub2', 'sub4'];

const App: React.FC = () => {
  const [openKeys, setOpenKeys] = useState(['sub1']);

  const onOpenChange: MenuProps['onOpenChange'] = (keys) => {
    const latestOpenKey = keys.find((key) => openKeys.indexOf(key) === -1);
    if (latestOpenKey && rootSubmenuKeys.indexOf(latestOpenKey!) === -1) {
      setOpenKeys(keys);
    } else {
      setOpenKeys(latestOpenKey ? [latestOpenKey] : []);
    }
  };

  return (
    <Menu
      mode="inline"
      openKeys={openKeys}
      onOpenChange={onOpenChange}
      style={{ width: 256 }}
      items={items}
    />
  );
};

export default App;

```

### vertical

```tsx
/**
 * title: 垂直菜单
 * description: 子菜单是弹出的形式。
 */

import React from 'react';
import { AppstoreOutlined, MailOutlined, SettingOutlined } from '@otakus/icons';
import type { MenuProps } from '@otakus/design';
import { Menu } from '@otakus/design';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key?: React.Key | null,
  icon?: React.ReactNode,
  children?: MenuItem[],
  type?: 'group'
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
    type
  } as MenuItem;
}

const items: MenuItem[] = [
  getItem('一级标题一', 'sub1', <MailOutlined />, [
    getItem('Item 1', null, null, [getItem('菜单项 1', '1'), getItem('菜单项 2', '2')], 'group'),
    getItem('Item 2', null, null, [getItem('菜单项 3', '3'), getItem('菜单项 4', '4')], 'group')
  ]),

  getItem('一级标题二', 'sub2', <AppstoreOutlined />, [
    getItem('菜单项 5', '5'),
    getItem('菜单项 6', '6'),
    getItem('子菜单', 'sub3', null, [getItem('菜单项 7', '7'), getItem('菜单项 8', '8')])
  ]),

  getItem('一级标题三', 'sub4', <SettingOutlined />, [
    getItem('菜单项 9', '9'),
    getItem('菜单项 10', '10'),
    getItem('菜单项 11', '11'),
    getItem('菜单项 12', '12')
  ])
];

const onClick: MenuProps['onClick'] = (e) => {
  console.log('click', e);
};

const App: React.FC = () => (
  <Menu onClick={onClick} style={{ width: 256 }} mode="vertical" items={items} />
);

export default App;

```

### theme

```tsx
/**
 * title: 主题
 * description: 内建了两套主题 light 和 dark，默认 light。
 */

import React, { useState } from 'react';
import { AppstoreOutlined, MailOutlined, SettingOutlined } from '@otakus/icons';
import type { MenuProps, MenuTheme } from '@otakus/design';
import { Menu, Switch } from '@otakus/design';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key?: React.Key | null,
  icon?: React.ReactNode,
  children?: MenuItem[],
  type?: 'group'
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
    type
  } as MenuItem;
}

const items: MenuItem[] = [
  getItem('一级标题一', 'sub1', <MailOutlined />, [
    getItem('菜单项 1', '1'),
    getItem('菜单项 2', '2'),
    getItem('菜单项 3', '3'),
    getItem('菜单项 4', '4')
  ]),

  getItem('一级标题二', 'sub2', <AppstoreOutlined />, [
    getItem('菜单项 5', '5'),
    getItem('菜单项 6', '6'),
    getItem('Submenu', 'sub3', null, [getItem('菜单项 7', '7'), getItem('菜单项 8', '8')])
  ]),

  getItem('一级标题三', 'sub4', <SettingOutlined />, [
    getItem('菜单项 9', '9'),
    getItem('菜单项 10', '10'),
    getItem('菜单项 11', '11'),
    getItem('菜单项 12', '12')
  ])
];

const App: React.FC = () => {
  const [theme, setTheme] = useState<MenuTheme>('dark');
  const [current, setCurrent] = useState('1');

  const changeTheme = (value: boolean) => {
    setTheme(value ? 'dark' : 'light');
  };

  const onClick: MenuProps['onClick'] = (e) => {
    console.log('click ', e);
    setCurrent(e.key);
  };

  return (
    <>
      <Switch
        checked={theme === 'dark'}
        onChange={changeTheme}
        checkedChildren="Dark"
        unCheckedChildren="Light"
      />
      <br />
      <br />
      <Menu
        theme={theme}
        onClick={onClick}
        style={{ width: 256 }}
        defaultOpenKeys={['sub1']}
        selectedKeys={[current]}
        mode="inline"
        items={items}
      />
    </>
  );
};

export default App;

```

### submenu-theme

```tsx
/**
 * title: 子菜单主题
 * description: 你可以通过 theme 属性来设置 SubMenu 的主题从而达到不同目录树下不同主题色的效果。该例子默认为根目录深色，子目录浅色效果。
 */

import { MailOutlined } from '@otakus/icons';
import React, { useState } from 'react';
import type { MenuProps, MenuTheme } from '@otakus/design';
import { Menu, Switch } from '@otakus/design';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key?: React.Key | null,
  icon?: React.ReactNode,
  children?: MenuItem[],
  theme?: 'light' | 'dark'
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
    theme
  } as MenuItem;
}

const App: React.FC = () => {
  const [theme, setTheme] = useState<MenuTheme>('light');
  const [current, setCurrent] = useState('1');

  const changeTheme = (value: boolean) => {
    setTheme(value ? 'dark' : 'light');
  };

  const onClick: MenuProps['onClick'] = (e) => {
    setCurrent(e.key);
  };

  const items: MenuItem[] = [
    getItem(
      'Navigation One',
      'sub1',
      <MailOutlined />,
      [getItem('菜单项 1', '1'), getItem('菜单项 2', '2'), getItem('菜单项 3', '3')],
      theme
    ),
    getItem('菜单项 5', '5'),
    getItem('菜单项 6', '6')
  ];

  return (
    <>
      <Switch
        checked={theme === 'dark'}
        onChange={changeTheme}
        checkedChildren="Dark"
        unCheckedChildren="Light"
      />
      <br />
      <br />
      <Menu
        onClick={onClick}
        style={{ width: 256 }}
        openKeys={['sub1']}
        selectedKeys={[current]}
        mode="vertical"
        theme="dark"
        items={items}
        getPopupContainer={function test(node) {
          return node.parentNode as HTMLElement;
        }}
      />
    </>
  );
};

export default App;

```

### switch-mode

```tsx
/**
 * title: 切换菜单类型
 * description: 展示动态切换模式。
 */

import React, { useState } from 'react';
import {
  AppstoreOutlined,
  CalendarOutlined,
  LinkOutlined,
  MailOutlined,
  SettingOutlined
} from '@otakus/icons';
import { Divider, Menu, Switch } from '@otakus/design';
import type { MenuProps } from '@otakus/design';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key?: React.Key | null,
  icon?: React.ReactNode,
  children?: MenuItem[]
): MenuItem {
  return {
    key,
    icon,
    children,
    label
  } as MenuItem;
}

const items: MenuItem[] = [
  getItem('一级标题一', '1', <MailOutlined />),
  getItem('一级标题二', '2', <CalendarOutlined />),
  getItem('一级标题二', 'sub1', <AppstoreOutlined />, [
    getItem('菜单项 3', '3'),
    getItem('菜单项 4', '4'),
    getItem('子菜单', 'sub1-2', null, [getItem('菜单项 5', '5'), getItem('菜单项 6', '6')])
  ]),
  getItem('一级标题三', 'sub2', <SettingOutlined />, [
    getItem('菜单项 7', '7'),
    getItem('菜单项 8', '8'),
    getItem('菜单项 9', '9'),
    getItem('菜单项 10', '10')
  ]),
  getItem(
    <a href="https://ant.design" target="_blank" rel="noopener noreferrer">
      Ant Design
    </a>,
    'link',
    <LinkOutlined />
  )
];

const App: React.FC = () => {
  const [mode, setMode] = useState<'vertical' | 'inline'>('inline');

  const changeMode = (value: boolean) => {
    setMode(value ? 'vertical' : 'inline');
  };

  return (
    <>
      <Switch onChange={changeMode} /> Change Mode
      <Divider type="vertical" />
      <br />
      <br />
      <Menu
        style={{ width: 256 }}
        defaultSelectedKeys={['1']}
        defaultOpenKeys={['sub1']}
        mode={mode}
        items={items}
      />
    </>
  );
};

export default App;

```

