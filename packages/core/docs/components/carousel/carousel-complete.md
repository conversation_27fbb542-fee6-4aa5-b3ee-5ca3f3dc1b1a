# Carousel

## API

通用属性参考：[通用属性](/components/common-props)

| 参数           | 说明                                                                                     | 类型                                    | 默认值    | 版本   |
| -------------- | ---------------------------------------------------------------------------------------- | --------------------------------------- | --------- | ------ |
| arrows         | 是否显示箭头                                                                             | boolean                                 | false     | 5.17.0 |
| autoplay       | 是否自动切换                                                                             | boolean                                 | false     |        |
| autoplaySpeed  | 自动切换的间隔（毫秒）                                                                   | number                                  | 3000      |        |
| dotPosition    | 面板指示点位置，可选 `top` `bottom` `left` `right`                                       | string                                  | `bottom`  |        |
| dots           | 是否显示面板指示点，如果为 `object` 则同时可以指定 `dotsClass` 或者                      | boolean \| { className?: string }       | true      |        |
| fade           | 使用渐变切换动效                                                                         | boolean                                 | false     |        |
| infinite       | 是否无限循环切换（实现方式是复制两份 children 元素，如果子元素有副作用则可能会引发 bug） | boolean                                 | true      |        |
| speed          | 切换动效的时间（毫秒）                                                                   | number                                  | 500       |        |
| easing         | 动画效果                                                                                 | string                                  | `linear`  |        |
| effect         | 动画效果函数                                                                             | `scrollx` \| `fade`                     | `scrollx` |        |
| afterChange    | 切换面板的回调                                                                           | (current: number) => void               | -         |        |
| beforeChange   | 切换面板的回调                                                                           | (current: number, next: number) => void | -         |        |
| waitForAnimate | 是否等待切换动画                                                                         | boolean                                 | false     |        |

## 示例

### 基本

```tsx
import React from 'react';
import { Carousel } from '@otakus/design';

const contentStyle: React.CSSProperties = {
  margin: 0,
  height: '160px',
  color: '#fff',
  lineHeight: '160px',
  textAlign: 'center',
  background: '#364d79'
};

const App: React.FC = () => {
  const onChange = (currentSlide: number) => {
    console.log(currentSlide);
  };

  return (
    <Carousel afterChange={onChange}>
      <div>
        <h3 style={contentStyle}>1</h3>
      </div>
      <div>
        <h3 style={contentStyle}>2</h3>
      </div>
      <div>
        <h3 style={contentStyle}>3</h3>
      </div>
      <div>
        <h3 style={contentStyle}>4</h3>
      </div>
    </Carousel>
  );
};

export default App;

```

### 位置

```tsx
import React, { useState } from 'react';
import type { CarouselProps, RadioChangeEvent } from '@otakus/design';
import { Carousel, Radio } from '@otakus/design';

type DotPosition = CarouselProps['dotPosition'];

const contentStyle: React.CSSProperties = {
  height: '160px',
  color: '#fff',
  lineHeight: '160px',
  textAlign: 'center',
  background: '#364d79'
};

const App: React.FC = () => {
  const [dotPosition, setDotPosition] = useState<DotPosition>('top');

  const handlePositionChange = ({ target: { value } }: RadioChangeEvent) => {
    setDotPosition(value);
  };

  return (
    <>
      <Radio.Group onChange={handlePositionChange} value={dotPosition} style={{ marginBottom: 8 }}>
        <Radio.Button value="top">Top</Radio.Button>
        <Radio.Button value="bottom">Bottom</Radio.Button>
        <Radio.Button value="left">Left</Radio.Button>
        <Radio.Button value="right">Right</Radio.Button>
      </Radio.Group>
      <Carousel dotPosition={dotPosition}>
        <div>
          <h3 style={contentStyle}>1</h3>
        </div>
        <div>
          <h3 style={contentStyle}>2</h3>
        </div>
        <div>
          <h3 style={contentStyle}>3</h3>
        </div>
        <div>
          <h3 style={contentStyle}>4</h3>
        </div>
      </Carousel>
    </>
  );
};

export default App;

```

### 自动切换

```tsx
import React from 'react';
import { Carousel } from '@otakus/design';

const contentStyle: React.CSSProperties = {
  height: '160px',
  color: '#fff',
  lineHeight: '160px',
  textAlign: 'center',
  background: '#364d79'
};

const App: React.FC = () => (
  <Carousel autoplay>
    <div>
      <h3 style={contentStyle}>1</h3>
    </div>
    <div>
      <h3 style={contentStyle}>2</h3>
    </div>
    <div>
      <h3 style={contentStyle}>3</h3>
    </div>
    <div>
      <h3 style={contentStyle}>4</h3>
    </div>
  </Carousel>
);

export default App;

```

### 渐显

```tsx
import React from 'react';
import { Carousel } from '@otakus/design';

const contentStyle: React.CSSProperties = {
  height: '160px',
  color: '#fff',
  lineHeight: '160px',
  textAlign: 'center',
  background: '#364d79'
};

const App: React.FC = () => (
  <Carousel effect="fade">
    <div>
      <h3 style={contentStyle}>1</h3>
    </div>
    <div>
      <h3 style={contentStyle}>2</h3>
    </div>
    <div>
      <h3 style={contentStyle}>3</h3>
    </div>
    <div>
      <h3 style={contentStyle}>4</h3>
    </div>
  </Carousel>
);

export default App;

```

### 切换箭头

```tsx
import React from 'react';
import { Carousel } from '@otakus/design';

const contentStyle: React.CSSProperties = {
  margin: 0,
  height: '160px',
  color: '#fff',
  lineHeight: '160px',
  textAlign: 'center',
  background: '#364d79'
};

const App: React.FC = () => (
  <>
    <Carousel arrows infinite={false}>
      <div>
        <h3 style={contentStyle}>1</h3>
      </div>
      <div>
        <h3 style={contentStyle}>2</h3>
      </div>
      <div>
        <h3 style={contentStyle}>3</h3>
      </div>
      <div>
        <h3 style={contentStyle}>4</h3>
      </div>
    </Carousel>
    <br />
    <Carousel arrows dotPosition="left" infinite={false}>
      <div>
        <h3 style={contentStyle}>1</h3>
      </div>
      <div>
        <h3 style={contentStyle}>2</h3>
      </div>
      <div>
        <h3 style={contentStyle}>3</h3>
      </div>
      <div>
        <h3 style={contentStyle}>4</h3>
      </div>
    </Carousel>
  </>
);

export default App;

```

### 组件 Token

```tsx
import React from 'react';
import { Carousel, ConfigProvider } from '@otakus/design';

/** Test usage. Do not use in your production. */
const contentStyle: React.CSSProperties = {
  margin: 0,
  height: '160px',
  color: '#fff',
  lineHeight: '160px',
  textAlign: 'center',
  background: '#364d79'
};

export default () => (
  <ConfigProvider
    theme={{
      components: {
        Carousel: {
          dotWidth: 50,
          dotHeight: 50,
          dotActiveWidth: 80
        }
      }
    }}
  >
    <Carousel>
      <div>
        <h3 style={contentStyle}>1</h3>
      </div>
      <div>
        <h3 style={contentStyle}>2</h3>
      </div>
      <div>
        <h3 style={contentStyle}>3</h3>
      </div>
      <div>
        <h3 style={contentStyle}>4</h3>
      </div>
    </Carousel>
  </ConfigProvider>
);

```

