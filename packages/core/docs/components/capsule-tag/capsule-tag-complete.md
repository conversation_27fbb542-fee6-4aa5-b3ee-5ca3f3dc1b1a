# CapsuleTag

### 使用示例

### 组件属性

| 属性名    | 类型                                      | 必填 | 描述             |
| --------- | ----------------------------------------- | ---- | ---------------- |
| id        | string                                    | 否   | 唯一标识         |
| label     | React.ReactNode                           | 否   | 前缀文本         |
| size      | TagSizeType                               | 否   | 尺寸，迷你或默认 |
| onClose   | (e: React.MouseEvent, id: string) => void | 否   | 关闭时的回调     |
| className | string                                    | 否   | 自定义类名       |
| style     | React.CSSProperties                       | 否   | 自定义样式       |
| color     | TagProps['color']                         | 否   | 颜色             |
| icon      | TagProps['icon']                          | 否   | 图标             |
| closeIcon | TagProps['closeIcon']                     | 否   | 关闭按钮         |
| show      | boolean                                   | 否   | 是否显示         |

## 示例

### 基本使用

```tsx
import { CapsuleTag, Space } from '@otakus/design';

function App() {
  return (
    <Space>
      <CapsuleTag id="my-tag-1" label="Small" color="blue" size="small">
        内容
      </CapsuleTag>
      <CapsuleTag id="my-tag-3" label="default" color="green">
        内容
      </CapsuleTag>
      <CapsuleTag id="my-tag-4" label="Mini" color="red" size="mini">
        内容
      </CapsuleTag>
      <CapsuleTag id="my-tag-5" label="Large" color="purple" size="large">
        内容
      </CapsuleTag>
      <CapsuleTag id="my-tag-6" label="标签" color="orange" closeIcon>
        内容
      </CapsuleTag>
      <CapsuleTag id="my-tag-7" label="标签" color="pink">
        内容
      </CapsuleTag>
    </Space>
  );
}

export default App;

```

### 配置ICON

```tsx
/**
 * description: 当需要在 `Tag` 内嵌入 `Icon` 时，可以设置 `icon` 属性，或者直接在 `Tag` 内使用 `Icon` 组件。如果想控制 `Icon` 具体的位置，只能直接使用 `Icon` 组件，而非 `icon` 属性。
 */

import React from 'react';
import { DesktopOutlined, SettingOutlined, StarOutlined, HeartOutlined } from '@otakus/icons';

import { Space, CapsuleTag } from '@otakus/design';

const App: React.FC = () => (
  <Space size={[0, 8]} wrap>
    <CapsuleTag icon={<StarOutlined />} color="#55acee" label="标签">
      Star
    </CapsuleTag>
    <CapsuleTag icon={<HeartOutlined />} color="#cd201f" label="标签">
      Heart
    </CapsuleTag>
    <CapsuleTag icon={<DesktopOutlined />} color="#3b5999" label="标签">
      Desktop
    </CapsuleTag>
    <CapsuleTag icon={<SettingOutlined />} color="#55acee" label="标签">
      Setting
    </CapsuleTag>
  </Space>
);

export default App;

```

