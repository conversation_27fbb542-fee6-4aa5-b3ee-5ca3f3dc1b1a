import { FIGMA_PACKAGES } from '@/consts/figma-packages';

export const getComponentPrompt = (COMPONENT_STATISTICS: string, FIGMA_COMPONENT_LIST: string) => `
你是一个 Figma 组件解析器，你的任务是将从数据库拿到的组件统计数据和 figma 使用的组件列表进行比对，找出在 figma 中使用的 otakus 组件。
以下是从数据库拿到的组件统计数据：
<组件统计数据>
${COMPONENT_STATISTICS}
</组件统计数据>
以下是 figma 使用的组件列表：
<figma 组件列表>
${FIGMA_COMPONENT_LIST}
</figma 组件列表>
请仔细比对这两份数据,在比对过程中，需要仔细确认每个组件的名称和相关信息。
如果在 <figma 组件列表> 中组件名称命中了 <组件统计数据> 中的基础组件，则组件的包名是 ${FIGMA_PACKAGES.DESIGN}。
如果在 <figma 组件列表> 中组件名称命中了 <组件统计数据> 中的 pro组件，则组件的包名是 ${FIGMA_PACKAGES.PRO_COMPONENTS}。
如果在 <figma 组件列表> 中有类似于 import { HomeOutlined } from '@ant-design/icons'; 的组件，那么认为是 otakus/icons 组件，返回的组件包名为 ${FIGMA_PACKAGES.ICONS},必须牢记。
`

export const getProCompoentInfo = (componentNames: string[]) => `请根据组件名称列表，返回组件的详细API，属性，参数，配置。组件名称列表：${componentNames.join(',')}`

export const GET_COMPONENT_LIST_PROMPT = `
有哪些基础组件和 pro组件
`

export const AFTER_GET_FIGMA_DATA = `IMPORTANT: After you call this tool, you MUST call get_image to get an image of the node for context.`

