import { z } from "zod";
import { createZodDto } from 'nestjs-zod'
import { Doc } from '@/consts/description';

export const DocScheme = z.object({
  componentName: z.string().describe(Doc.COMPONENT_NAME),
  componentPackage: z.string().describe(Doc.COMPONENT_PACKAGE),
  clientLanguages: z.string().optional().describe(Doc.CLIENT_LANGUAGES),
  clientFrameworks: z.string().optional().describe(Doc.CLIENT_FRAMEWORKS),
  clientName: z.string().optional().describe(Doc.CLIENT_NAME),
})

export class DocDto extends createZodDto(DocScheme) { }
