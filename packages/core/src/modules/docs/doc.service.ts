import { Injectable, Logger } from "@nestjs/common";
import { DifyService } from "../dify/dify.service";
import { CacheService } from "@/infra/cache/cache.service";
import { DocDto } from "./doc.dto";
import { getComponentPrompt } from "@/consts/prompt/doc";

@Injectable()
export class DocsService {
  private readonly logger = new Logger(DocsService.name);
  constructor(private readonly difyService: DifyService, private readonly cacheService: CacheService) { }
  async getComponentInfoFromRemote(docDto: DocDto) {
    this.logger.log('getProComponentInfo', docDto);
    const response = await this.difyService.runWorkFlow('DIFY_DOC_WORKFLOW_API_KEY', {
      input: getComponentPrompt(docDto.componentName, docDto.componentPackage)
    })
    return response
  }

  async getComponentInfo(docDto: DocDto) {
    const cacheKey = `${docDto.componentName}:${docDto.componentPackage}`;
    const cachedData = await this.cacheService.get(cacheKey);
    if (cachedData) {
      return cachedData;
    }
    const data = await this.getComponentInfoFromRemote(docDto);
    await this.cacheService.set(cacheKey, data);
    return data;
  }

}
