import { expect, describe, it } from '@jest/globals';
import { render, act } from '@testing-library/react';
import React from 'react';
import { DataContext, DataProvider } from '../../src/components/person-search/context/DataContext';

describe('DataProvider', () => {
  it('provides the expected context value', async () => {
    let contextValue;
    await act(async () => {
      render(
        <DataProvider
          env="test"
          systemId="123"
          clientId="test-client"
          purpose="ACCOUNT"
          language="zh-CN"
          localeFile={{ 'en-US': {}, 'zh-CN': {} }}
          urls={{
            search_url: ''
          }}
          headers={{}}
          showResigned
          open
        >
          <DataContext.Consumer>
            {(value) => {
              contextValue = value;
              return null;
            }}
          </DataContext.Consumer>
        </DataProvider>
      );
    });

    expect(contextValue.env).toEqual('test');
    expect(contextValue.systemId).toEqual('123');
    expect(contextValue.language).toEqual('zh-CN');
    expect(contextValue.localeFile).toEqual({ 'en-US': {}, 'zh-CN': {} });
    expect(contextValue.urls).toEqual({ search_url: '' });
    expect(contextValue.headers).toEqual({});
    expect(contextValue.showResigned).toEqual(true);
    expect(contextValue.open).toEqual(true);
  });
});
