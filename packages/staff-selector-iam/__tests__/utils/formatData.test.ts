import { expect, describe, it } from '@jest/globals';
import {
  formatData,
  getLabelKey,
  formatUserData,
  formatOrgData,
  formatUserOrgData
} from '../../src/utils/formatData';
import { PURPOSE_ENUM } from '../../src/constants/enum';
import { IUserItem, IOrgItem } from '../../src/types';

describe('formatData utils', () => {
  describe('getLabelKey', () => {
    it('should return en_name for en-US language', () => {
      expect(getLabelKey('en-US')).toBe('en_name');
    });

    it('should return cn_name for zh-CN language', () => {
      expect(getLabelKey('zh-CN')).toBe('cn_name');
    });
  });

  describe('formatUserData', () => {
    const mockUser: IUserItem = {
      mi_id: 753,
      cn_name: '张三',
      en_name: '<PERSON>',
      domain: 'example.com',
      display_dept_profile: {
        full_en_path: 'IT/Dev',
        full_cn_path: '技术部/开发组'
      }
    };

    it('should format single user data correctly', () => {
      const result = formatUserData(mockUser, 'zh-CN');
      expect(result).toEqual({
        item: {
          ...mockUser,
          full_en_path: 'IT/Dev',
          full_cn_path: '技术部/开发组',
          reportSelectType: null,
          value: 753
        },
        value: 753,
        label: '张三 (example.com)',
        purpose: PURPOSE_ENUM.ACCOUNT
      });
    });

    it('should format array of users correctly', () => {
      const result = formatUserData([mockUser], 'en-US');
      expect(result).toHaveLength(1);
      expect(result[0].label).toBe('Zhang San (example.com)');
    });
  });

  describe('formatOrgData', () => {
    const mockOrg: IOrgItem = {
      dept_id: 1,
      cn_name: '技术部',
      en_name: 'IT Department',
      child_dept_ids: [],
      parent_dept_id: 1,
      isDept: true
    };

    it('should format single org data correctly', () => {
      const result = formatOrgData(mockOrg, 'zh-CN');
      expect(result).toEqual({
        item: {
          ...mockOrg,
          isLeaf: true,
          reportSelectType: null,
          value: 1
        },
        value: 1,
        label: '技术部',
        isLeaf: true,
        parent_dept_id: 1,
        purpose: PURPOSE_ENUM.DEPARTMENT
      });
    });

    it('should format array of orgs correctly', () => {
      const result = formatOrgData([mockOrg], 'en-US');
      expect(result).toHaveLength(1);
      expect(result[0].label).toBe('IT Department');
    });
  });

  describe('formatData', () => {
    const mockUser: IUserItem = {
      mi_id: 753,
      cn_name: '张三',
      en_name: 'Zhang San',
      domain: 'example.com'
    };

    const mockOrg: IOrgItem = {
      dept_id: 1,
      cn_name: '技术部',
      en_name: 'IT Department',
      child_dept_ids: [],
      isDept: true
    };

    it('should format user data when purpose is ACCOUNT', () => {
      const result: IUserItem[] = formatData([mockUser], {
        purpose: PURPOSE_ENUM.ACCOUNT,
        language: 'zh-CN'
      }) as IUserItem[];
      expect(result[0].purpose).toBe(PURPOSE_ENUM.ACCOUNT);
    });

    it('should format org data when purpose is DEPARTMENT', () => {
      const result = formatData([mockOrg], {
        purpose: PURPOSE_ENUM.DEPARTMENT,
        language: 'zh-CN'
      }) as IOrgItem[];
      expect(result[0].purpose).toBe(PURPOSE_ENUM.DEPARTMENT);
    });

    it('should format mixed data when purpose is HYBRID', () => {
      const result = formatData([mockUser, mockOrg], {
        purpose: PURPOSE_ENUM.HYBRID,
        language: 'zh-CN'
      }) as IUserItem[] | IOrgItem[];
      expect(result).toHaveLength(2);
      expect(result[0].purpose).toBe(PURPOSE_ENUM.ACCOUNT);
      expect(result[1].purpose).toBe(PURPOSE_ENUM.DEPARTMENT);
    });
  });

  describe('formatUserOrgData', () => {
    const mockUser: IUserItem = {
      mi_id: 753,
      cn_name: '张三',
      en_name: 'Zhang San',
      domain: 'example.com',
      isDept: false
    };

    const mockOrg: IOrgItem = {
      dept_id: 1,
      cn_name: '技术部',
      en_name: 'IT Department',
      child_dept_ids: [],
      isDept: true
    };

    it('should format mixed data correctly', () => {
      const result = formatUserOrgData([mockUser, mockOrg], 'zh-CN');
      expect(result).toBeDefined();
      expect(result!).toHaveLength(2);
      expect(result![0].purpose).toBe(PURPOSE_ENUM.ACCOUNT);
      expect(result![1].purpose).toBe(PURPOSE_ENUM.DEPARTMENT);
    });

    it('should handle single user correctly', () => {
      const result = formatUserOrgData(mockUser, 'zh-CN') as IUserItem | IOrgItem;
      expect(result?.purpose).toBe(PURPOSE_ENUM.ACCOUNT);
    });

    it('should handle single org correctly', () => {
      const result = formatUserOrgData(mockOrg, 'zh-CN') as IOrgItem | IUserItem;
      expect(result?.purpose).toBe(PURPOSE_ENUM.DEPARTMENT);
    });
  });
});
