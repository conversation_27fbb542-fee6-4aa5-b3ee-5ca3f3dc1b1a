---
nav:
  title: 通用组件
  order: 3
title: 账号/组织选择（IAM）
isLogin: true
group:
  title: 安全合规
  order: 1
order: 1
demo:
  cols: 2
---

### 各端文档链接指引

- PC 端：[PC 端 React 文档（当前）](https://otakus.mihoyo.com/biz-components/staff-selector-iam)、[PC 端 Vue 文档](https://fe.mihoyo.com/index.html#/zh-CN/component/acc-dept-select)、[UI设计稿](https://www.figma.com/design/LH8ASJuiLW94qUWcmSx47Z/%F0%9F%92%BB-Otaku-Design-PC-%E9%80%9A%E7%94%A8%E7%BB%84%E4%BB%B6--02?node-id=65-9&t=bSq25TN2uxh7g5ig-1)

- Mobile 端：[Mobile 端 React 文档](https://otakus.mihoyo.com/mobile/components/staff-selector-iam)、[Mobile 端 Vue 文档](https://fe.mihoyo.com/index.html#/zh-CN/component/business-pages?navBusiness=https%253A%252F%252Ffe.mihoyo.com%252Fstatic%252Fotaku-mobile-doc%252Findex.html%2523%252Fstaff-selector-iam-vue)、[UI设计稿](https://www.figma.com/design/L8FmiNh9p8hxeexqfPVSkm/%F0%9F%93%B1-Otaku-Design-Mob-%E9%80%9A%E7%94%A8%E7%BB%84%E4%BB%B6?node-id=274-10065&t=BgLUwjSSWApxgsyf-1)

## 1.使用前必读

### 接入指引

[【接入指引】IAM选账号/组织组件](https://km.mihoyo.com/articleBase/17467/1626593)

### 1.1 名词解释

> 详细了解：<a href="https://km.mihoyo.com/articleBase/16670/1213999" target="_blank">企微平替接口&人员和账号简述</a>

#### 人员和账号

##### 人员

通过招聘走入职流程进入人员主档的正式或者外包员工，简单来说就是公司“员工”。

##### 账号

包括在人员入职过程中，调用IAM为员工创建的个人账号和由自行IAM创建并自主管理的账号,如：打包机账号、自动化平台账号，还有外部客服、前台、保安小哥等类型账号。

##### 区别

1. “人员”是走入职流程来的，账号是可以由IAM单独创建的。
2. “人员”一定有账号，但是账号不一定挂在人员身上。从范围上看账号数据是大于人员数据的。
3. 人员状态是在职和离职、账号状态是停用和启用。

> <p style="color: var(--otakus-color-error); font-weight: bold;">⚠️特别注意：</p>
> 1. 账号选择组件，选择的是<span style="color: var(--otakus-color-error)">账号</span>，包括系统账号，系统账号无法在米有人的接口中获取到信息。<br />
> 2. 如果疑问，请咨询IAM同学（诸婧文）。

#### 行政组织树和Wave组织树

##### 行政组织树

行政组织树来源于核心人事系统”米有人“，是HR系统的组织树

##### Wave组织树

Wave组织树是基于行政组织树进行了二次加工，多了一些自建组织，并且增加了人员可见性

##### 区别

1. Wave 部分orgId 和行政组织orgCode不同，可以通过id&code映射接口做mapping
2. Wave拥有自建组织，这块是行政组织所没有的。

> <p style="color: var(--otakus-color-error); font-weight: bold;">⚠️特别注意：</p>
> <p style="color: var(--otakus-color-error); font-weight: bold;">1. 行政组织树可以获取到失效组织，Wave组织树获取不到（逻辑删除）。</p>

### 1.2 组件对比

#### 与账号选择（UC）对比

| 对比项     | 新：**账号/组织选择（IAM）**                                                                                       | 旧：账号选择（UC）                              |
| ---------- | ------------------------------------------------------------------------------------------------------------------ | ----------------------------------------------- |
| 数据源     | IAM                                                                                                                | MDM                                             |
| 认证网关   | 仅支持新认证网关                                                                                                   | 同时支持新旧认证网关（新网关需升级到2.4.0以上） |
| 数据主键   | 支持账号(mi_id)、域账号(domain)、工号(emp_no)                                                                      | 仅支持域账号(domain)                            |
| 离职人员   | 需要走审批开通使用，仅可以通过域账号完全匹配了才允许被搜索出来，不支持模糊搜索，暂不支持离职标签展示。支持停用标签 | 支持域账号模糊搜索，支持离职标签展示            |
| 自定义群组 | 功能已支持，UC群组数据尚未同步                                                                                     | 支持                                            |

#### 与组织选择（PaaS）对比

| 对比项   | 新：**账号/组织选择（IAM）**                      | 旧：组织选择(PaaS)            |
| -------- | ------------------------------------------------- | ----------------------------- |
| 认证网关 | 新认证网关（api.agw.mihoyo.com）                  | 旧认证网关(ssoapi.mihoyo.com) |
| 数据主键 | Wave组织树（dept_id），支持行政组织树（org_code） | 行政组织树（org_code）        |

### 1.3 产品接入

1、接入新网关：产品/后端同学请根据<a href="https://km.mihoyo.com/articleBase/16671/1208121" target="_blank">【接入指引】云上认证网关</a>完成项目接入。

2、申请SceneId：审批→【企业效能】→[选账号/组织组件申请表单](https://infopaas.app.mihoyo.com/newrender/?applicationCode=eec1bc06edaa4ab4&tenantCode=a31f2cc533624533&globalDefKey=a807ac4d77094e3c8eb1847920be34c2&origin=launchProcess&from_approval=Y&isPaasIfame=Y&approval_language_type=zh-CN)

### 1.4 前端认证网关对接

前端同学请根据<a href="https://km.mihoyo.com/articleBase/16671/1325288" target="_blank">【前端 认证网关对接】</a>完成认证网关对接。

### 1.5 旧组件迁移指南

请安装新包：`@otakus/staff-selector-iam`

#### 从账号选择（UC）迁移

账号选择（UC）使用的是`domain`作为数据主键，需要指定`accountKey`为`domain`，其余API不变。

参考DEMO：

<code src="./demo/select-account-old.tsx">兼容旧的选人组件(域账号)</code>

#### 从组织选择（PaaS）迁移

组织选择（PaaS）使用的是`org_code`作为数据主键，需要指定`departmentKey`为`org_code`，其余API不变。

参考DEMO：

<code src="./demo/select-dept-old.tsx">兼容旧的选组组件(org_code)</code>

#### 从自己开发的业务组件迁移

如果业务组件使用的数据主键为`emp_no`, 需要指定`accountKey`为`emp_no`。

其他情况，请联系我们。

产品： 诸婧文（jingwen.zhu）
后端： 蓝迪（di.lan）
前端： 何飞翔（feixiang.he） 李蒙（meng.li）

## 2.1 使用组件

### 2.2 安装

```bash
pnpm add @otakus/staff-selector-iam
```

### 2.3 组件简介

组件支持两种调用方式

1. OtakuPersonSelect：下拉选择+弹窗的交互方式
2. OtakuPersonSearch：以受控方式单独使用弹窗

### 2.4 使用示例

> HOYO_ENV 是EEE HOYO框架默认的环境变量，非HOYO框架的项目请自行替换成项目中的环境变量

#### 选账号

<code src="./demo/select-account.tsx">选账号</code>
<code src="./demo/select-account-no-fetch.tsx">选账号-使用传入的数据进行初始化回显</code>
<code src="./demo/select-account-no-recent.tsx">选账号-不展示最近选择</code>

<!-- <code src="./demo/select-account-no-dept.tsx">选账号-不展示按x组织选择</code> -->

<code src="./demo/select-limit.tsx">选账号-仅展示3个tag</code>
<code src="./demo/select-account-en.tsx">账号选择-多语言-英文</code>
<code src="./demo/select-account-disabled.tsx">禁用态</code>
<code src="./demo/select-disabled-account.tsx">选择停用域账号</code>
<code src="./demo/select-account-single.tsx">单选</code>

#### 选组织

> 场景值OTAKU没有组织权限（敏感数据需要申请），如果需要预览组织的样式的话，请去[test环境](https://otakus-test.mihoyo.com/biz-components/staff-selector-iam)

<code src="./demo/select-dept.tsx">选组织</code>
<code src="./demo/select-dept-no-recent.tsx">选组织-不展示最近选择</code>

<!-- <code src="./demo/select-dept-no-dept.tsx">选组织-不展示按x组织选择</code> -->

<code src="./demo/select-limit-dept.tsx">选组织-仅展示3个tag</code>
<code src="./demo/select-dept-en.tsx">选组织-多语言-英文</code>
<code src="./demo/select-dept-disabled.tsx">禁用态</code>
<code src="./demo/select-disabled-dept.tsx">选择失效组织</code>
<code src="./demo/select-dept-single.tsx">单选</code>

<!-- #### 选账号和组织

<code src="./demo/select-acct-and-dept.tsx">选账号和组织</code>
<code src="./demo/select-acct-and-dept-no-recent.tsx">选账号和组织-不展示最近选择</code>
<code src="./demo/select-acct-and-dept-no-dept.tsx">选账号和组织-不展示按x组织选择</code>
<code src="./demo/select-acct-and-dept-no-acct-dept.tsx">选账号和组织-不展示按x组织选择-不展示最近选择</code>
<code src="./demo/select-limit-hybird.tsx">选账号和组织-仅展示3个tag</code>
<code src="./demo/select-en.tsx">账号&组织选择多语言-英文</code>
<code src="./demo/select-acct-and-dept-disabled.tsx">选账号和组织</code>
<code src="./demo/select-disabled-acct-dept.tsx">选择失效域账号和组织</code> -->

#### 其他

<code src="./demo/select-readonly.tsx">只读态</code>
<code src="./demo/select-readonly-disabled.tsx">停用账号只读态</code>
<code src="./demo/select-limit-count.tsx">限制数量</code>
<code src="./demo/person-search.tsx">单独使用弹窗</code>

<code src="./demo/select-account-with-custom-sevice.tsx">自定义服务对象</code>
<code src="./demo/person-search-account-with-custom-sevice.tsx">自定义服务对象</code>
<code src="./demo/select-form.tsx">在Form中使用</code>

## 3 API

### 3.1 OtakuPersonSelect

| 参数            | 说明                                                                                                                                                                                                                                                             | 类型                                                                                                                                     | 是否必传 | 默认值           | 版本    |
| --------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------- | -------- | ---------------- | ------- |
| **env**         | 内置通用 API 环境                                                                                                                                                                                                                                                | 'test' \| 'uat' \| 'pp' \| 'prod'                                                                                                        | **是**   | 'test'           | -       |
| clientId        | 新认证网关 clientId, 组件会自动获取，一般不需要配置。                                                                                                                                                                                                            | string                                                                                                                                   | 否       | -                | -       |
| scene           | 业务场景id，声明该场景的数据选择范围，和clientId绑定，需要向IAM申请                                                                                                                                                                                              | string                                                                                                                                   | 是       | -                | -       |
| purpose         | 组件用途 - 选账号/选组织                                                                                                                                                                                                                                         | 'account' \| 'department'                                                                                                                | 否       | account          | -       |
| accountKey      | 账号数据主键，和scene申请时选定的账号数据主键一致，如果业务是域账号为主键，需配置为‘domain’                                                                                                                                                                      | 'mi_id' \| 'domain' \| 'emp_no'                                                                                                          | 否       | 'mi_id'          | -       |
| departmentKey   | 组织数据主键，和scene申请时选定的组织数据主键一致，如果业务是以‘org_code’为主键，需配置为‘org_code’                                                                                                                                                              | 'dept_id' \| 'org_code'                                                                                                                  | 否       | 'dept_id'        | -       |
| value           | 已选值                                                                                                                                                                                                                                                           | [见下方](#value类型)                                                                                                                     | 否       | []               | -       |
| limit           | 已选账号上限                                                                                                                                                                                                                                                     | number                                                                                                                                   | 否       | 1000             | -       |
| deptLimit       | 已选组织上限                                                                                                                                                                                                                                                     | number                                                                                                                                   | 否       | 同 limit         | -       |
| showCommon      | 弹窗是否展示近期选择（可选停用账号的场景无近期选择功能）                                                                                                                                                                                                         | boolean                                                                                                                                  | 否       | true             | -       |
| language        | 多语言环境                                                                                                                                                                                                                                                       | 'zh-CN' \| 'en-US'                                                                                                                       | 否       | 'zh-CN'          | -       |
| mode            | 设置单选、多选                                                                                                                                                                                                                                                   | 'multiple' \| 'single'                                                                                                                   | 否       | 'multiple'       | -       |
| localeFile      | 文案配置信息，如 title 等                                                                                                                                                                                                                                        | -                                                                                                                                        | 否       | -                | -       |
| headers         | 调用内置通用 API 时所需要的额外 header                                                                                                                                                                                                                           | {[key: string]: string}                                                                                                                  | 否       | -                | -       |
| tabOrder        | 指定搜索 Tab 页的顺序，若该 tab 页无数据则不渲染                                                                                                                                                                                                                 | ('user' \| 'dept')[]                                                                                                                     | 否       | ['user', 'dept'] | -       |
| isReadOnly      | 只读                                                                                                                                                                                                                                                             | boolean                                                                                                                                  | 否       | false            | -       |
| **onChange**    | 选中 option，或 input 的 value 变化时，调用此函数                                                                                                                                                                                                                | (value: IDepartment(35-组织类型idepartment)[] \| IAccount(34-账号类型iaccount)[]) => void                                                | 否       | -                | -       |
| **onCancel**    | 点击弹窗中取消按钮或关闭的回调                                                                                                                                                                                                                                   | () => void                                                                                                                               | **是**   | -                | -       |
| **onOpen**      | 弹窗打开时的回调                                                                                                                                                                                                                                                 | (val: boolean) => void                                                                                                                   | **否**   | -                | -       |
| **onConfirm**   | 点击弹窗中确定按钮的回调,通过return false可以阻止关闭弹窗                                                                                                                                                                                                        | (value: [IDepartment](#35-组织类型idepartment)[] \| [IAccount](#34-账号类型iaccount)[], ) => void \| boolean \| Promise<boolean \| void> | **是**   | -                | -       |
| **onError**     | 访问接口失败时的回调                                                                                                                                                                                                                                             | (error) => void                                                                                                                          | 否       | -                | -       |
| select props    | 透传了 @otakus/design 的 select props，如 maxTagCount、maxTagTextLength 等                                                                                                                                                                                       | -                                                                                                                                        | 否       | -                | -       |
| readOnlyProps   | 透传给只读态的参数，如 showWaveIcon 等                                                                                                                                                                                                                           | -                                                                                                                                        | 否       | -                | >=0.3.0 |
| showCustomGroup | 是否展示自定义群组，只在选账号&多选时并且scene数据范围不包括停用账号时生效                                                                                                                                                                                       | boolean                                                                                                                                  | 否       | true             | >=0.6.0 |
| **openService** | 自定义服务对象                                                                                                                                                                                                                                                   | [IOpenService](#37-自定义服务对象iopenservice)                                                                                           | 否       | -                | >=0.6.0 |
| suffixVisible   | 是否展示打开弹窗的icon                                                                                                                                                                                                                                           | boolean                                                                                                                                  | 否       | true             | >=0.6.4 |
| skipDataFetch   | 只在选账号时生效：初始化时是否跳过回显接口调用，使用用户传入的数据进行回显, 传入的数据必须包含主键和名字（区分英文，例如以domain为主键时为[{domain: '', en_name: '', cn_name: ''}]，以mi_id为主键时为[{mi_id: 123, en_name: '', cn_name: ''}],以emp_no为主键同理 | boolean                                                                                                                                  | 否       | false            | >=0.6.7 |

### 3.2 OtakuPersonSearch

| 参数            | 说明                                                                                                | 类型                                                                                                                                   | 是否必传 | 默认值           | 版本    |
| --------------- | --------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------- | -------- | ---------------- | ------- |
| clientId        | 新认证网关 clientId                                                                                 | string                                                                                                                                 | 否       | -                |         |
| **env**         | 内置通用 API 环境                                                                                   | 'test' \| 'uat' \| 'pp' \| 'prod'                                                                                                      | **是**   | 'test'           | -       |
| **scene**       | 业务场景id，声明该场景的数据选择范围，和clientId绑定，需要向IAM申请                                 | string                                                                                                                                 | 是       | -                | -       |
| **purpose**     | 组件用途 - 选账号/选组织                                                                            | 'account' \| 'department'                                                                                                              | 否       | account          | -       |
| accountKey      | 账号数据主键，和scene申请时选定的账号数据主键一致，如果业务是域账号为主键，需配置为‘domain’         | 'mi_id' \| 'domain' \| 'emp_no'                                                                                                        | 否       | 'mi_id'          | -       |
| departmentKey   | 组织数据主键，和scene申请时选定的组织数据主键一致，如果业务是以‘org_code’为主键，需配置为‘org_code’ | 'dept_id' \| 'org_code'                                                                                                                | 否       | 'dept_id'        | -       |
| open            | 组件是否可见                                                                                        | boolean                                                                                                                                | 否       | false            | -       |
| value           | 已选值                                                                                              | [见下方]                                                                                                                               | 否       | []               | -       |
| limit           | 已选账号上限                                                                                        | number                                                                                                                                 | 否       | 1000             | -       |
| deptLimit       | 已选组织上限                                                                                        | number                                                                                                                                 | 否       | 同 limit         | -       |
| showCommon      | 弹窗是否展示近期选择                                                                                | boolean                                                                                                                                | 否       | true             | -       |
| showCustomGroup | 是否展示自定义群组，只在选账号&多选时并且scene数据范围不包括停用账号时生效                          | boolean                                                                                                                                | 否       | true             | >=0.6.0 |
| language        | 多语言环境                                                                                          | 'zh-CN' \| 'en-US'                                                                                                                     | 否       | 'zh-CN'          | -       |
| mode            | 设置单选、多选                                                                                      | 'multiple' \| 'single'                                                                                                                 | 否       | 'multiple'       | -       |
| localeFile      | 文案配置信息，如 title 等                                                                           | -                                                                                                                                      | 否       | -                | -       |
| headers         | 调用内置通用 API 时所需要的额外 header                                                              | {[key: string]: string}                                                                                                                | 否       | -                | -       |
| tabOrder        | 指定搜索 Tab 页的顺序，若该 tab 页无数据则不渲染                                                    | ('user' \| 'dept')[]                                                                                                                   | 否       | ['user', 'dept'] | -       |
| **onCancel**    | 点击取消按钮或关闭的回调                                                                            | () => void                                                                                                                             | **是**   | -                | -       |
| **onConfirm**   | 点击确定按钮的回调，通过return false可以阻止关闭弹窗                                                | (value: [IDepartment](#35-组织类型idepartment)[] \| [IAccount](#34-账号类型iaccount)[]) => void \| boolean \| Promise<boolean \| void> | **是**   | -                | -       |
| **onError**     | 访问接口失败时的回调                                                                                | (error) => void                                                                                                                        | 否       | -                | -       |
| **openService** | 自定义服务对象                                                                                      | [IOpenService](#37-自定义服务对象iopenservice)                                                                                         | 否       | -                | >=0.6.0 |

### 3.3 value类型

<!-- 只选账号的场景 `value` 的类型可接受 `mi_id` 的 `string`、`string[]` 或者包含 `mi_id` 的 `IAccount[]`。
例如：

- `73050`
- `[73050]`
- `[{ mi_id: 73050, ... }]`

只选组织的场景 `value` 的类型可接受 `dept_id` 的 `string`、`string[]` 或者包含 `dept_id` 的 `IDepartment[]`。
例如：

- `3750`
- `[3750]`
- `[{ dept_id: 73050, ... }]` -->

<!-- 账号&组织混选的场景`value`的类型为`Object[]`，每个Item需要通过isUser，isDept字段显式的告诉组件当前value是账号还是组织。
例如：

- `[{value: 73050, isUser: true}, {value: 3750, isDept: true}]` -->

### 3.4 账号类型（IAccount）

> 取代`IUserItem`，`IUserItem`会在1.0版本中删除

```javascript
// 账号类型 - 完整类型
export interface IAccount extends IAccountBase, IAccountExtend {}

// 账号类型 - 基本类型：https://plat-yapi.mihoyo.com/project/10144/interface/api/462705
export interface IAccountBase {
  // 用户头像
  avatar_url?: string;
  // 中文名
  cn_name?: string;
  // 域账号(停用)
  disabled_domains?: string[];
  // 工号(停用)
  disabled_emp_nos?: string[];
  // 所属部门信息
  display_dept_profile?: IDepartment;
  // 域账号
  domain?: string;
  // 邮箱
  email?: string;
  // 工号
  emp_no?: string;
  // 英文名
  en_name?: string;
  // 高亮信息，仅用于搜索结果高亮
  highlight?: IHighlight;
  // 米 id
  mi_id?: number;
  // 主键
  primary_key?: string;
  // 主键状态
  primary_key_status?: number;
  // MiID状态
  status?: number;
  // 租户 id
  tenant_id?: TenantType;
  // 用户名
  user_name?: string;
  // 用户类型
  user_type?: number;
  // 近期选择 标识 id，fetch_recent_select接口独有
  record_id?: number;
  // 昵称
  nick_name?: string;
}

// 账号类型 - 兼容属性（UC接口）:https://yapi.mihoyo.com/project/751/interface/api/167525
export interface IAccountOld {
  // 工号（冗余字段），选人用
  id?: string;
  // 工号，选组用
  empNo?: string;
  // 姓名
  name?: string;
  // 微信头像
  thumbnail?: string;
  // 所属组织>岗位细分
  orgPositionPath?: string;
  // 是否命中
  flag?: string | null;
  // 岗位细分
  positionTitle?: string;
  // 工作状态
  workStatus?: number | null;
  // 昵称
  nickName?: string;
}

// 账号类型 - 扩展属性，组件展示用
export interface IAccountExtend {
  // 是否是自定义数据
  isCustom?: boolean;
  // 是否禁用
  disabled?: boolean;

  // 来自display_dept_profile
  full_org_path?: string;
  // 组织路径英文
  full_en_path?: string;
  // 组织路径中文
  full_cn_path?: string;
  // 标识值
  value?: number;
  // 是否叶子节点
  isLeaf?: boolean;

  // 报告选择类型
  reportSelectType?: ReportChannelType;

  // 是否是用户, 用于判断是否是用户
  isUser?: boolean;

  // 批量选择时关键字
  keyword?: string;
}
```

### 3.5 组织类型（IDepartment）

> 取代`IOrgItem`，`IOrgItem`会在1.0版本中删除

```javascript
// 完整部门类型
export interface IDepartment extends IDepartmentBase, IDepartmentExtend {}

// 部门基础类型 - 模型来源：https://plat-yapi.mihoyo.com/project/10144/interface/api/462703
export interface IDepartmentBase {
  // 下一级人数
  child_acct_count?: number;
  // 下一级部门数
  child_dept_count?: number;
  // 中文名
  cn_name?: string;
  // 组织 id
  dept_id?: number;
  // 层级
  depth?: number;
  // 是否失效
  disabled?: boolean;
  // 英文名
  en_name?: string;
  // 组织路径英文
  full_en_path?: string;
  // 组织路径中文
  full_cn_path?: string;
  // 父级组织 id
  parent_dept_id?: number;
  // 所归属的组织 1 miHoYo, 2 HoYoverse
  tenant_id?: TenantType;
  // 组织路径
  org_code?: string;
  // 是否是行政组织
  is_primary?: boolean;
  // 高亮信息，仅用于搜索结果高亮,search_candidate接口独有
  highlight?: IHighlight;
  // 主键
  primary_key?: string;
  // 下一子级所有米 id,list_dept_detail接口独有
  child_mi_ids?: number[];
  // 下一子级所组织 id,list_dept_detail接口独有
  child_dept_ids?: number[];
}

// 部门自定义类型 - 组件扩展用
export interface IDepartmentExtend {
  // 子级数据
  children_details?: (IDepartment | IAccount)[]; // 如果children_details存在，下钻时则不会请求接口
  // 选中类型，self代表选中自身，只有在选组织或者选账号组织组件生效
  // children代表选中组织下的数据，如果是选账号组件，会选中组织下的账号数据。如果是选组织组件，会选中组织下的组织。如果是账号组织组件，会选中组织下的组织和账号
  select_type?: SelectType;
  // 是否叶子节点
  isLeaf?: boolean;
  // 标识值
  value?: number;
  // 报告选择类型
  reportSelectType?: ReportChannelType;
  // 是否是组织, 用于判断是否是组织
  isDept?: boolean;
  // 是否是自定义数据
  isCustom?: boolean;
}
```

### 3.6 多语言（localeFile）

组件已内置多语言，如果想修改文案，可以传对应的 key，具体可修改 key 如下：

```bash
const localeFile = {
  'en-US': {
    // 弹窗按钮确认文案
    confirm: 'Confirm',
    // 弹窗按钮取消文案
    cancel: 'Cancel',
    // 选账号场景弹窗标题文案
    account_title: 'Choose account',
    // 选组织场景弹窗标题文案
    dept_title: 'Choose organization',
    // 选账号场景弹窗搜索框文案
    account_search_placeholder: 'Search for name, pinyin, domain account, account name, nickname',
    // 选组织场景弹窗搜索框文案
    dept_search_placeholder: 'Search for organization name'
  },
  'zh-CN': {
    // 弹窗按钮确认文案
    confirm: '确定',
    // 弹窗按钮取消文案
    cancel: '取消',
    // 选账号场景弹窗标题文案
    account_title: '选择账号',
    // 选组织场景弹窗标题文案
    dept_title: '选择组织',
    // 选账号场景弹窗搜索框文案
    account_search_placeholder: '搜索姓名、拼音、域账号、账号名、昵称',
    // 选组织场景弹窗搜索框文案
    dept_search_placeholder: '搜索组织名'
  }
};
```

### 3.7 自定义服务对象（IOpenService）

```javascript
export interface IOpenService<A extends IAccount, B extends IDepartment, G extends IGroup> {
  /**
   * 搜索
   * @param keyword 搜索关键词
   * @returns SearchResult<A, B, G> 搜索结果
   */
  search: (keyword: string) => Promise<SearchResult<A, B, G>>;
  /**
   * 获取账号详情
   * @param value 账号ID或账号名称
   * @returns A[] 账号详情
   */
  getAccountDetail: (value: string[] | number[]) => Promise<A[]>;
  /**
   * 获取部门详情
   * @param value 部门ID或部门名称
   * @returns B[] 部门详情
   */
  getDepartmentDetail: (value: string[] | number[]) => Promise<B[]>;
  /**
   * 获取根部门详情
   * @returns B[] 根部门详情
   */
  getRootDepartment: () => Promise<B[]>;
  /**
   * 获取组详情
   * @param value 组ID或组名称
   * @returns G[] 组详情
   */
  getGroupDetail: (group_id: number) => Promise<{ accts: A[] }>;

  /**
   * 更新报告
   * @param params 报告参数
   * @returns ReportResult<A, B, G> 报告结果
   */
  reportCurrentSelect: (params: IReportParams) => Promise<void>;

  /**
   * 获取最近选择
   * @returns ICurrentSelectItem[] 最近选择
   */
  getRecentSelectData: () => Promise<SearchResult<A, B, G>>;

  /**
   * 获取部门层级
   * @param deptId 部门ID
   * @returns B[] 部门层级
   */
  getDeptHierarchy: (deptIds: number[] | string[]) => Promise<SearchResult<A, B, G>>;

  /**
   * 批量账号搜索
   * @param keywords 搜索关键词
   * @returns A[] 账号详情
   */
  batchAccountSearch: (keywords: string[]) => Promise<A[]>;

  /**
   * 删除组
   * @param groupId 组ID
   * @returns number 删除的组ID
   */
  deleteGroup: (groupId: number) => Promise<number>;

  /**
   * 创建群组
   * @param group 群组
   * @returns number 创建的群组ID
   */
  createGroup: (group: IGroupCreateParams) => Promise<number>;

  /**
   * 更新群组
   * @param group 群组
   * @returns number 更新的群组ID
   */
  updateGroup: (group: IGroupUpdateParams) => Promise<number>;

  /**
   * 获取组列表
   * @returns G[] 组列表
   */
  getGroupList: () => Promise<G[]>;

  /**
   * 转换账号数据
   * @param data 账号数据
   * @param from 来源
   * @returns A[] 转换后的账号数据
   */
  transformAccount: (data: A[], from: string) => Promise<A[]>;

  /**
   * 转换部门数据
   * @param data 部门数据
   * @param from 来源
   * @returns B[] 转换后的部门数据
   */
  transformDepartment: (data: B[], from: string) => Promise<B[]>;

  /**
   * 转换组数据
   * @param data 组数据
   * @param from 来源
   * @returns G[] 转换后的组数据
   */
  transformGroup: (data: G[], from: string) => Promise<G[]>;
}
```

## 4.FAQ

#### Q:项目A使用的UC选人组件，切换到IAM选账号组件后，原先保存的数据能不能正常展示？

A:如果老数据保存的是domain，需要给组件参数`accountKey`指定为`domain`后可正常展示。如果老数据保存的是`mi_id`则无需指定`accountKey`。注意，在申请表单中，选择的账号类型也需要是域账号。

#### Q:账号中不一定有域账号属性，如果项目A只需要选到有域账号的账号，怎么控制？

A:在你的业务接入IAM时需要选择选账号&组织的数据搜索范围，只需要将接入IAM时生成的数据搜索范围key传给组件的`scene`参数即可。

#### Q:infosysD的系统如何使用账号组织选择组件？

A:目前可以通过UMD方式引入组件。使用方法如下：

需要将项目中的react、react-dom、lodash、lodash-es、dayjs externals 出来。

```
const getHeadScripts = () => {
  const reactScript = process.env.NODE_ENV === 'development' ? [
    'https://info-static.mihoyo.com/unpkg/react/17.0.2/umd/react.development.js',
    'https://info-static.mihoyo.com/unpkg/react-dom/17.0.2/umd/react-dom.development.js',
  ] : [
    'https://info-static.mihoyo.com/unpkg/react/17.0.2/umd/react.production.min.js',
    'https://info-static.mihoyo.com/unpkg/react-dom/17.0.2/umd/react-dom.production.min.js',
  ];
  return {
    headScripts: [
      ...reactScript,
      'https://info-static.mihoyo.com/unpkg/lodash/4.17.21/lodash.min.js',
      'https://info-static.mihoyo.com/unpkg/dayjs/1.11.11/dayjs.min.js',
      //选人组件UMD文件,x.x.x替换为实际版本号
      'https://info-static.mihoyo.com/unpkg/@otakus/staff-selector-iam/x.x.x/umd/index.js'
    ]
  };
}

export default defineConfig({
  externals: {
    'react': 'React',
    'react-dom': 'ReactDOM',
     dayjs: 'dayjs',
     lodash: '_',
  },
  ...getHeadScripts(),

});

```

在页面使用组件。

```
const { PersonSelectWithThemeProvider } = window.OtakusStaffSelector;

function App() {
  return (
    {/* 请根据API文档传入实际的参数 */}
    <PersonSelectWithThemeProvider scene="OTAKU_TEST" env="test" />
  );
}
```

## 5.更新日志

<embed src="./CHANGELOG.md"></embed>
