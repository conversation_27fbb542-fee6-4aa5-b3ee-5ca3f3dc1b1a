import react from '@vitejs/plugin-react';
import path from 'path';
import { defineConfig, UserConfig } from 'vite';
import tsconfigPaths from 'vite-tsconfig-paths';
// import vitePluginImp from 'vite-plugin-imp';
import NpmImport from 'less-plugin-npm-import';
import vitePluginCommonjs from 'vite-plugin-commonjs';
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js';
// import svgr from "vite-plugin-svgr";
const entry = path.resolve(__dirname, 'src');

// const winPath = (path) => {
//   const isExtendedLengthPath = /^\\\\\?\\/.test(path);
//   if (isExtendedLengthPath) {
//     return path;
//   }
//   return path.replace(/\\/g, "/");
// }

// https://vitejs.dev/config/
export const BaseConfig: UserConfig = {
  plugins: [
    react(),
    tsconfigPaths(),
    vitePluginCommonjs(),
    cssInjectedByJsPlugin()
    // svgr(),
    // visualizer({
    //   gzipSize: true,
    //   brotliSize: true,
    //   emitFile: false,
    //   filename: 'test.html', //分析图生成的文件名
    //   open: true //如果存在本地服务端口，将在打包后自动展示
    // })
  ],
  esbuild: {
    drop: ['console', 'debugger']
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: false,
        modifyVars: {},
        module: true,
        plugins: [new NpmImport({ prefix: '~' })]
      }
    }
  },
  define: {
    'process.env.NODE_ENV': '"production"'
  },
  build: {
    cssCodeSplit: true,
    target: 'es2015',
    lib: {
      entry
    },
    rollupOptions: {
      external: ['react', 'react-dom', 'lodash', 'lodash-es', 'dayjs'],
      output: [
        {
          format: 'umd',
          name: 'OtakusStaffSelector',
          entryFileNames: '[name].js',
          globals: {
            react: 'React',
            'react-dom': 'ReactDOM',
            lodash: 'lodash',
            'lodash-es': 'lodash-es',
            dayjs: 'dayjs'
          },
          sourcemap: true,
          dir: 'umd'
        }
      ],
      treeshake: true
    }
  }
};
export default defineConfig(BaseConfig);
