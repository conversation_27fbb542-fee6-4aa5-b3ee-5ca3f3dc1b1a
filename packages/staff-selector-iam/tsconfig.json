{"compilerOptions": {"strict": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "noUnusedLocals": true, "noUnusedParameters": true, "skipLibCheck": true, "target": "esnext", "emitDeclarationOnly": true, "outDir": "es", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@otakus/staff-selector-iam": ["src/index.tsx"], "react": ["./node_modules/@types/react"]}, "jsx": "react", "types": ["jest"]}, "exclude": ["**/*.test.*", "demo", "**/demo/**", "**/GroupManage.tsx"], "include": ["src", "demo", "__tests__", "src/jest-dom.d.ts"]}