import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useContext,
  useMemo,
  ReactNode
} from 'react';
import { Input, Tabs, Checkbox, message, Empty } from '@otakus/design';
import { FixedSizeList as List } from 'react-window';
import GroupUserList from './GroupUserList';
import DeptDetail from './DeptDetail';
import BatchSelectResult from './BatchSelectResult';
import RenderItem from './RenderItem';
import PersonSearchSVG from '../PersonSearchSVG';
import BreadCrumbComponent from '../Breadcrumb';
import { DataContext } from '../../context/DataContext';
import { useApi } from '../../api';
import { isGroup } from '../../utils';
import { useI18n } from '../../hooks';
import { PURPOSE_ENUM, REPORT_SELECT_CHANNEL_ENUM, DEPT_ID } from '../../../../constants/enum';
import { usePrefixCls } from '../../../../hooks/usePrefixCls';
import { IReportItem } from '../../types';
import { useStyles } from '../../style';

const CheckboxGroupWrapper = ({ items, checkedList }) => {
  const prefix = usePrefixCls();
  const { styles } = useStyles({ prefixCls: prefix });
  const valueSet = new Set(
    checkedList?.map((checkedItem) => checkedItem.mi_id || checkedItem.dept_id)
  );
  const value = Array.from(valueSet);
  return (
    <Checkbox.Group
      value={value}
      className={styles.staffInputContainerListBody}
      style={{ height: '100%' }}
    >
      {items}
    </Checkbox.Group>
  );
};

const Row = ({ index, style, data }) => {
  const item = data.data[index];
  return (
    <div style={style}>
      <RenderItem
        key={item.isDept ? item.dept_id : item.mi_id}
        item={item}
        inputValue={data.inputValue}
        onCheckboxChange={data.onCheckboxChange}
        onGroupClick={data.onGroupClick}
        onDeptClick={data.onDeptClick}
      />
    </div>
  );
};

const UserDataList = ({
  searchResults,
  checkedList,
  row,
  inputValue,
  onCheckboxChange,
  onGroupClick,
  onDeptClick,
  height
}) => {
  const prefix = usePrefixCls();
  const { styles } = useStyles({ prefixCls: prefix });
  return (
    <div
      className={styles.staffInputContainerList}
      style={{ overflow: 'hidden', maxHeight: '100%', height: '100%' }}
    >
      <CheckboxGroupWrapper
        items={
          <List
            height={height} // 根据需要调整高度
            itemCount={searchResults?.user.length}
            itemSize={48} // 根据需要调整每个项的高度
            width={'100%'}
            itemData={{
              data: searchResults?.user,
              inputValue,
              onCheckboxChange,
              onGroupClick,
              onDeptClick
            }}
            overscanCount={20}
            style={{ height: '100%' }}
          >
            {row}
          </List>
        }
        checkedList={checkedList}
      />
    </div>
  );
};

const DeptList = ({
  searchResults,
  inputValue,
  onCheckboxChange,
  onGroupClick,
  onDeptClick,
  checkedList,
  height
}) => {
  const prefix = usePrefixCls();
  const { styles } = useStyles({ prefixCls: prefix });
  return (
    <div className={styles.staffInputContainerList} style={{ height: '100%' }}>
      <CheckboxGroupWrapper
        items={
          <List
            height={height} // 根据需要调整高度
            itemCount={searchResults?.depts.length}
            itemSize={48} // 根据需要调整每个项的高度
            width={'100%'}
            itemData={{
              data: searchResults?.depts,
              inputValue,
              onCheckboxChange,
              onGroupClick,
              onDeptClick
            }}
            style={{ height: '100%' }}
          >
            {Row}
          </List>
        }
        checkedList={checkedList}
      />
    </div>
  );
};

// 人员搜索
const PersonInput = (props) => {
  const {
    selectedList,
    tabOrder,
    onSelected,
    showOrg,
    showDept,
    entryData,
    onClickAllDept,
    onClear,
    showInputResult,
    setShowInputResult
  } = props;

  const i18n = useI18n();
  const prefix = usePrefixCls();
  const { styles } = useStyles({ prefixCls: prefix });

  // 搜索结果
  const [searchResults, setSearchResults] = useState({
    user: [],
    group: [],
    custom: [],
    depts: []
  });
  // 输入值
  const [inputValue, setInputValue] = useState('');
  // 是否处于拼音状态
  const [isComposing, setIsComposing] = useState(false);
  // 当前tab页  默认：'1'-全部，'2'-人员 '3'-群组 '4'-自定义
  const [activeTab, setActiveTab] = useState('1');
  // 是否查看组内人员
  const [showGroupUser, setShowGroupUser] = useState(true);
  // 是否查看组织下的子组织/人员
  const [showDeptDetail, setShowDeptDetail] = useState(false);
  // 选择的通用数据节点列表
  const [checkedList, setCheckedList] = useState([]);
  // 当前群组
  const [currentGroup, setCurrentGroup] = useState({});
  // 当前组织
  const [currentDept, setCurrentDept] = useState({});
  // 当前组织的下钻详情
  const [currentDeptHierarchy, setCurrentDeptHierarchy] = useState([]);
  // loading态
  const [dataLoaded, setDataLoaded] = useState(false);
  // 所有结果数量
  const [totalResult, setTotalResult] = useState(0);
  // 面包屑
  const [breadcrumbs, setBreadcrumbs] = useState<any>();
  // 批量添加时是否显示搜索结果
  const [showBatchResult, setShowBatchResult] = useState(false);
  // 批量添加搜索结果
  const [batchResult, setBatchResult] = useState([]);
  const [messageApi, contextHolder] = message.useMessage() as [typeof message, ReactNode];
  // 下钻loading
  const [drilldownLoading, setDrilldownLoading] = useState(false);

  // 创建一个 ref 来存储是否是组件的初始化状态
  const isInitialRender = useRef(true);
  // 定义searchTimer变量
  const searchTimer = useRef(null);

  const { purpose, language } = useContext(DataContext);

  const { searchUserByIam, searchDeptsByIam, getDeptHierarchy, batchAccountSearch } = useApi();

  const searchPlaceholder = useMemo(() => {
    if (purpose === PURPOSE_ENUM.ACCOUNT) {
      return i18n('account_search_placeholder', '搜索姓名、拼音、域账号、账号名、昵称');
    } else if (purpose === PURPOSE_ENUM.DEPARTMENT) {
      return i18n('dept_search_placeholder', '搜索组织名');
    } else if (purpose === PURPOSE_ENUM.HYBRID) {
      return i18n(
        'account_dept_search_placeholder',
        '搜索姓名、拼音、域账号、账号名、昵称、组织名'
      );
    }
  }, [purpose]);

  useEffect(() => {
    setBreadcrumbs([
      {
        key: 'all',
        name: i18n('all', '全部')
      }
    ]);
  }, []);

  const inputRef = useRef(null);
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  // 右侧清除选择时，左侧联动
  useEffect(() => {
    // 已选的 人/组
    const selectedNormalList = selectedList?.filter((i) => !i.isCustom);

    setCheckedList(selectedNormalList);
  }, [selectedList]);
  const batchSelectHandler = useCallback(() => {
    const innerHandler = async () => {
      // 判断如果批量搜索query超过2000的话，直接返回
      const batchQueryList = inputValue.split(';').filter((item) => item);
      if (batchQueryList.length > 2000) {
        messageApi.open({
          type: 'error',
          content: i18n('batch_limit', '最多支持输入2000个query')
        });
        setInputValue('');
        return;
      }
      setDataLoaded(false);
      const result = await batchAccountSearch(inputValue);
      // 遍历result，如果item.accts存在并且长度为一，表示精确匹配，直接选中
      // 用于存放精确匹配的数据，用于直接选中
      const exactMatchList = [];
      // 用于存放模糊匹配的数据和未匹配中的数据，用于展示
      const fuzzyMatchList = [];
      result.forEach((item) => {
        // 过滤掉item.accts=null和item.accts中的item为null的情况
        if (!item.accts) {
          item.accts = [];
        }
        item.accts = item.accts.filter((item) => item);
        if (item.accts && item.accts.length === 1) {
          exactMatchList.push({
            ...item.accts[0],
            isUser: true,
            keyword: item.keyword
          });
        } else if (item.accts && item.accts.length > 1) {
          // 如果item.accts存在并且长度大于一，表示模糊匹配，需要展示
          item.accts = item.accts
            .filter((item) => item)
            .map((acct) => ({
              ...acct,
              isUser: true
            }));
          fuzzyMatchList.push(item);
        } else {
          // 如果item.accts不存在，表示未找到，需要展示
          fuzzyMatchList.push(item);
        }
      });
      // 如果有精确匹配的数据，直接选中
      if (exactMatchList.length > 0) {
        const reportInfo: IReportItem = {
          channel: REPORT_SELECT_CHANNEL_ENUM.PARSE,
          src_id: 0
        };
        onSelected({ items: exactMatchList, reportInfo, filter: true });
      }
      // 最后fuzzyMatchList的数据展示在搜索结果中
      if (fuzzyMatchList.length > 0) {
        setBatchResult(fuzzyMatchList);
        setShowDeptDetail(false);
        setShowBatchResult(true);
        setShowInputResult(true);
        setDataLoaded(true);
      }
      // 全部精准匹配，清空输入框
      if (exactMatchList.length === batchQueryList.length) {
        setInputValue('');
      }
    };
    innerHandler();
  }, [inputValue]);
  // 搜索回调
  const handleSearch = () => {
    // 组件初始化状态，不执行
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }
    // 清除上一次延时
    if (searchTimer.current) {
      clearTimeout(searchTimer.current);
    }
    // 有输入内容
    if (inputValue) {
      // 拼音输入中，不搜索
      if (isComposing) {
        return;
      }
      searchTimer.current = setTimeout(() => {
        setShowInputResult(!!inputValue);
        const isBatchSearch = purpose !== PURPOSE_ENUM.DEPARTMENT && inputValue.includes(';');
        // 处理批量添加的情况
        if (isBatchSearch) {
          batchSelectHandler();
          return;
        } else {
          setShowBatchResult(false);
        }
        setDataLoaded(false);
        setBreadcrumbs([
          {
            key: 'all',
            name: i18n('all', '全部')
          }
        ]);

        const searchPromises = [];
        // 群组搜索接口
        // showCustomGroup && searchPromises.push(searchGroup(inputValue));
        // 新选人搜索账号
        purpose !== PURPOSE_ENUM.DEPARTMENT && searchPromises.push(searchUserByIam(inputValue));
        // 新选人搜索组织
        showOrg && searchPromises.push(searchDeptsByIam(inputValue));
        Promise.all(searchPromises)
          .then((responses) => {
            const result = {
              user: [],
              group: [],
              custom: [],
              depts: []
            };
            for (const response of responses) {
              const isCustomData = response.some((i) => i.isCustom);
              const isGroupData = response.some((i) => i.groupList);
              const isDept = response.some((i) => i.isDept);

              // 业务自定义数据
              if (isCustomData) {
                response.forEach((item) => {
                  result.custom.push(item);
                });
              }
              // 群组
              else if (isGroupData) {
                response.forEach((item) => {
                  result.group.push(...item.groupList);
                });
              }
              // 组织
              else if (isDept) {
                response.forEach((item) => {
                  result.depts.push(item);
                });
              }
              // 人员
              else if (!isGroupData) {
                response.forEach((item) => {
                  result.user.push(item);
                });
              }
            }
            setSearchResults(result);
            setTotalResult(
              result?.group?.length +
                result?.user?.length +
                result?.custom?.length +
                result?.depts?.length
            );
            // 每次搜索重定向到首页
            setActiveTab('1');
            setDataLoaded(true);
            setShowDeptDetail(false);
          })
          .catch(() => {
            setDataLoaded(true);
          });
      }, 300);
    } else {
      setShowInputResult(!!inputValue);
    }
  };
  // 搜索
  useEffect(() => {
    handleSearch();
  }, [inputValue, isComposing]);

  // 回车事件
  const handleKeyDown = (e) => {
    if (e.keyCode === 13) {
      handleSearch();
    }
  };

  useEffect(() => {
    if (!inputValue) {
      onClear();
      setBatchResult([]);
      setShowBatchResult(false);
    }
  }, [inputValue]);

  // 处理从按组织选择的情况
  useEffect(() => {
    const innerHandler = async () => {
      if (showDept) {
        setShowInputResult(true);
        setDataLoaded(false);
        try {
          const deptHierarchy = await getDeptHierarchy({
            deptId: entryData.dept_id
          });
          if (!deptHierarchy) {
            messageApi.open({
              type: 'error',
              content: i18n('can_not_find_dept_info', '查询组织信息失败')
            });
            return;
          }
          setCurrentDeptHierarchy(deptHierarchy);
          setBreadcrumbs((prev) => {
            const newBreadcrumbs = [...prev];
            const exists = newBreadcrumbs.some(
              (breadcrumb) => breadcrumb.key === entryData.dept_id
            );
            if (!exists) {
              newBreadcrumbs.push({
                key: entryData.dept_id,
                name: i18n('select_by_dept', '按组织架构选择'),
                deptHierarchy,
                ...entryData
              });
            }
            return newBreadcrumbs;
          });
          setShowDeptDetail(true);
          setDataLoaded(true);
        } catch (err) {
          console.error('Failed to get department hierarchy:', err);
        }
      } else {
        setBreadcrumbs([
          {
            key: 'all',
            name: i18n('all', '全部')
          }
        ]);
        // setShowResults(true);
        // setShowDeptDetail(false);
      }
    };
    innerHandler();
  }, [showDept, entryData]);

  // 处理中文搜索
  const handleCompositionStart = useCallback(() => {
    setIsComposing(true);
  }, []);

  const handleCompositionEnd = useCallback((e) => {
    setIsComposing(false);
    setInputValue(e.target.value);
  }, []);

  // tab 切换
  const handleTabChange = (key) => {
    setActiveTab(key);
    setCurrentGroup({});
    setShowGroupUser(false);
  };

  const onBreadcrumbClick = async (item) => {
    if (item.dept_id) {
      setDrilldownLoading(true);
      setBreadcrumbs(breadcrumbs.slice(0, breadcrumbs.indexOf(item) + 1));
      setCurrentDept(item);
      const deptHierarchy =
        item.deptHierarchy ||
        (await getDeptHierarchy({
          deptId: item.dept_id
        }));
      setCurrentDeptHierarchy(deptHierarchy);
    }
    setDrilldownLoading(false);
    if (item.key === 'all') {
      setShowDeptDetail(false);
      setShowInputResult(false);
      onClickAllDept();
    }
  };

  // 搜索结果群组点击
  const onGroupClick = useCallback((item) => {
    // 如果是群组，则跳到群组tab，否则跳到自定义类别tab
    setActiveTab(isGroup(item) ? '3' : '4');
    setCurrentGroup(item);
    setShowGroupUser(true);
  }, []);

  // 搜索结果组织点击
  const onDeptClick = useCallback(
    (item) => {
      try {
        // 如果是选组织，当前组织下没有子组织
        if (!item.child_dept_count && purpose === PURPOSE_ENUM.DEPARTMENT) {
          // 如果是选组织模式，点击组织后直接选中
          if (purpose === PURPOSE_ENUM.DEPARTMENT) {
            const reportInfo: IReportItem = {
              channel: REPORT_SELECT_CHANNEL_ENUM.DEPT,
              src_id: item.parent_dept_id,
              src_str: ''
            };
            onSelected({ items: item, reportInfo });
          }
          return false;
        }
        // 如果是选账号，当前组织下没有子账号和组织则不可下钻
        // if (purpose === PURPOSE_ENUM.ACCOUNT && !item.child_acct_count && !item.child_dept_count) {
        //   return false;
        // }
        // 如果是账号&组织混选模式，当前组织下没有子账号和组织则不可下钻直接选中
        // if (purpose === PURPOSE_ENUM.HYBRID && !item.child_acct_count && !item.child_dept_count) {
        //   const reportInfo: IReportItem = {
        //     channel: REPORT_SELECT_CHANNEL_ENUM.DEPT,
        //     src_id: item.parent_dept_id,
        //     src_str: ''
        //   };
        //   onSelected({ items: item, reportInfo });
        //   return false;
        // }

        // 如果是账号&组织混选模式，组织被选中后不可下钻，（不需要这个逻辑
        // if (
        //   purpose === PURPOSE_ENUM.HYBRID &&
        //   selectedList.some((checkedItem) => checkedItem.dept_id === item.dept_id)
        // ) {
        //   return false;
        // }
        const params: {
          deptId: string;
          showChildMi?: boolean;
          showChildDept?: boolean;
        } = {
          deptId: item.dept_id
        };
        if (purpose === PURPOSE_ENUM.DEPARTMENT) {
          params.showChildMi = false;
        }
        const dropdownHandler = async () => {
          setDrilldownLoading(true);
          setShowDeptDetail(true);
          const deptHierarchy = await getDeptHierarchy(params);
          setCurrentDept(item);
          setCurrentDeptHierarchy(deptHierarchy);
          setBreadcrumbs((prev) => {
            const newBreadcrumbs = [...prev];
            if (newBreadcrumbs.length === 1) {
              newBreadcrumbs.push({
                key: item.tenant_id || DEPT_ID.MIHOYO,
                name: i18n('select_by_dept', '按组织架构选择'),
                dept_id: item.tenant_id || DEPT_ID.MIHOYO
              });
            }
            newBreadcrumbs.push({
              key: item.dept_id,
              name: language === 'zh-CN' ? item.cn_name : item.en_name,
              deptHierarchy,
              ...item
            });
            return newBreadcrumbs;
          });
          setDrilldownLoading(false);
        };
        dropdownHandler();
        // 清空输入框
        if (inputRef.current) {
          // setInputValue('');
        }
      } catch (err) {
        console.error('Failed to get department hierarchy:', err);
      }
    },
    [selectedList]
  );

  // 查看更多
  const viewMore = useCallback((tab) => {
    setActiveTab(tab);
  }, []);

  // 多选框选 人 or 组
  const onCheckboxChange = useCallback(
    (content) => {
      // eslint-disable-next-line no-unsafe-optional-chaining
      const groupUsers = isGroup(content) ? [content, ...content?.groupUserOuts] : [content];

      const reportInfo: IReportItem = {
        channel: REPORT_SELECT_CHANNEL_ENUM.SEARCH,
        src_str: inputValue,
        src_id: 0
      };
      onSelected({ items: groupUsers, reportInfo });
    },
    [inputValue]
  );

  const tabs = useMemo(
    () => ({
      user:
        searchResults?.user.length > 0
          ? [
              {
                key: '2',
                label: `${i18n('search_handler', '账号')} (${
                  searchResults?.user.length > 99 ? '99+' : searchResults?.user.length
                })`,
                children: (
                  <UserDataList
                    row={Row}
                    checkedList={checkedList}
                    searchResults={searchResults}
                    inputValue={inputValue}
                    onCheckboxChange={onCheckboxChange}
                    onGroupClick={onGroupClick}
                    onDeptClick={onDeptClick}
                    height={445}
                  />
                )
              }
            ]
          : [],
      dept:
        searchResults?.depts?.length > 0
          ? [
              {
                key: '5',
                label: `${i18n('search_dept', '组织')} (${
                  searchResults?.depts.length > 99 ? '99+' : searchResults?.depts.length
                })`,
                children: (
                  <DeptList
                    searchResults={searchResults}
                    inputValue={inputValue}
                    onCheckboxChange={onCheckboxChange}
                    onGroupClick={onGroupClick}
                    onDeptClick={onDeptClick}
                    checkedList={checkedList}
                    height={445}
                  />
                )
              }
            ]
          : [],
      group:
        searchResults?.group.length > 0
          ? [
              {
                key: '3',
                label: `${i18n('search_group', '我的群组')} (${
                  searchResults?.group.length > 99 ? '99+' : searchResults?.group.length
                })`,
                children: showGroupUser ? (
                  <GroupUserList
                    currentGroup={currentGroup}
                    inputValue={inputValue}
                    checkedList={checkedList}
                    selectedList={selectedList}
                    onSelected={onSelected}
                    handleShowGroupUserChange={setShowGroupUser}
                  />
                ) : (
                  <div className={styles.staffInputContainerList}>
                    <CheckboxGroupWrapper
                      items={searchResults?.group.map((item) => (
                        <RenderItem
                          key={item.id}
                          item={item}
                          inputValue={inputValue}
                          onCheckboxChange={onCheckboxChange}
                          onGroupClick={onGroupClick}
                        />
                      ))}
                      checkedList={checkedList}
                    />
                  </div>
                )
              }
            ]
          : [],
      custom:
        searchResults?.custom.length > 0
          ? [
              {
                key: '4',
                label: `${i18n('search_custom', '搜索自定义')} (${
                  searchResults?.custom.length > 99 ? '99+' : searchResults?.custom.length
                })`,
                children: (
                  <div className={styles.staffInputContainerList}>
                    <CheckboxGroupWrapper
                      items={searchResults?.custom.map((item) => (
                        <RenderItem
                          key={item.id}
                          item={item}
                          inputValue={inputValue}
                          onCheckboxChange={onCheckboxChange}
                          onGroupClick={onGroupClick}
                        />
                      ))}
                      checkedList={checkedList}
                    />
                  </div>
                )
              }
            ]
          : []
    }),
    [searchResults, inputValue, checkedList]
  );

  // 全部 tab 下的 item
  const itemComponents = {
    user: searchResults?.user?.length > 0 && (
      <div className={styles.staffInputContainerListItemBlock}>
        <div className={styles.staffInputContainerListHead}>
          {i18n('search_handler', '账号')}（
          {searchResults?.user.length > 99 ? '99+' : searchResults?.user.length}）
        </div>
        <CheckboxGroupWrapper
          items={searchResults?.user
            ?.slice(0, 3)
            .map((item) => (
              <RenderItem
                key={item.mi_id}
                item={item}
                inputValue={inputValue}
                onCheckboxChange={onCheckboxChange}
                onGroupClick={onGroupClick}
              />
            ))}
          checkedList={checkedList}
        />
        {searchResults?.user?.length > 3 && (
          <div className={styles.staffInputContainerListMore} onClick={() => viewMore('2')}>
            {i18n('more', '查看更多')}
          </div>
        )}
      </div>
    ),
    dept: searchResults?.depts?.length > 0 && (
      <div className={styles.staffInputContainerListItemBlock}>
        <div className={styles.staffInputContainerListHead}>
          {i18n('search_dept', '组织')}（
          {searchResults?.depts.length > 99 ? '99+' : searchResults?.depts.length}）
        </div>
        <CheckboxGroupWrapper
          items={searchResults?.depts
            ?.slice(0, 3)
            .map((item) => (
              <RenderItem
                key={item.mi_id}
                item={item}
                inputValue={inputValue}
                onCheckboxChange={onCheckboxChange}
                onDeptClick={onDeptClick}
              />
            ))}
          checkedList={checkedList}
        />
        {searchResults?.depts?.length > 3 && (
          <div className={styles.staffInputContainerListMore} onClick={() => viewMore('5')}>
            {i18n('more', '查看更多')}
          </div>
        )}
      </div>
    ),
    group: searchResults?.group?.length > 0 && (
      <div className={styles.staffInputContainerListItemBlock}>
        <div className={styles.staffInputContainerListHead}>
          {i18n('search_group', '我的群组')}（
          {searchResults?.group.length > 99 ? '99+' : searchResults?.group.length}）
        </div>
        <CheckboxGroupWrapper
          items={searchResults?.group
            ?.slice(0, 3)
            .map((item) => (
              <RenderItem
                key={item.id}
                item={item}
                inputValue={inputValue}
                onCheckboxChange={onCheckboxChange}
                onGroupClick={onGroupClick}
              />
            ))}
          checkedList={checkedList}
        />
        {searchResults?.group?.length > 3 && (
          <div className={styles.staffInputContainerListMore} onClick={() => viewMore('3')}>
            {i18n('more', '查看更多')}
          </div>
        )}
      </div>
    ),
    custom: searchResults?.custom?.length > 0 && (
      <div className={styles.staffInputContainerListItemBlock}>
        <div className={styles.staffInputContainerListHead}>
          {i18n('search_custom', '搜索自定义')}
        </div>
        <CheckboxGroupWrapper
          items={searchResults?.custom
            ?.slice(0, 3)
            .map((item) => (
              <RenderItem
                key={item.id}
                item={item}
                inputValue={inputValue}
                onCheckboxChange={onCheckboxChange}
                onGroupClick={onGroupClick}
              />
            ))}
          checkedList={checkedList}
        />
        {searchResults?.custom?.length > 3 && (
          <div className={styles.staffInputContainerListMore} onClick={() => viewMore('4')}>
            {i18n('more', '查看更多')}
          </div>
        )}
      </div>
    )
  };

  // 所有搜索 tab items
  const tabItems = [
    {
      key: '1',
      label: `${i18n('all', '全部')} (${totalResult > 99 ? '99+' : totalResult})`,
      children: (
        <div className={`${styles.staffInputContainerList} is-all`}>
          {tabOrder.map((itemKey) =>
            itemComponents[itemKey]
              ? React.cloneElement(itemComponents[itemKey], { key: itemKey })
              : null
          )}
        </div>
      )
    },
    ...tabOrder.flatMap((tabKey) => tabs[tabKey] || [])
  ];

  // 只搜索到账号数据，不展示tab
  const isOnlyUserResults = useMemo(() => {
    return (
      searchResults?.user?.length > 0 &&
      searchResults?.group?.length === 0 &&
      searchResults?.depts?.length === 0 &&
      searchResults?.custom?.length === 0
    );
  }, [searchResults]);

  // 选组织时，不展示tab
  const isOnlyDeptResults = useMemo(() => {
    return (
      purpose === PURPOSE_ENUM.DEPARTMENT &&
      searchResults?.user?.length === 0 &&
      searchResults?.group?.length === 0 &&
      searchResults?.depts?.length > 0 &&
      searchResults?.custom?.length === 0
    );
  }, [searchResults]);

  // 组织&账号混选时，即使只搜到到一类数据也展示tab

  // 搜索结果
  const searchResultRenderUI =
    totalResult > 0 ? (
      isOnlyUserResults ? (
        <UserDataList
          row={Row}
          checkedList={checkedList}
          searchResults={searchResults}
          inputValue={inputValue}
          onCheckboxChange={onCheckboxChange}
          onGroupClick={onGroupClick}
          onDeptClick={onDeptClick}
          height={491}
        />
      ) : isOnlyDeptResults ? (
        <DeptList
          searchResults={searchResults}
          inputValue={inputValue}
          onCheckboxChange={onCheckboxChange}
          onGroupClick={onGroupClick}
          onDeptClick={onDeptClick}
          checkedList={checkedList}
          height={491}
        />
      ) : (
        <Tabs
          className={styles.searchTabs}
          activeKey={activeTab}
          onChange={handleTabChange}
          tabPosition={'top'}
          destroyInactiveTabPane
          items={tabItems}
          style={{ height: '100%' }}
        />
      )
    ) : (
      <div className={styles.staffInputContainerListEmpty}>
        <div className={`${styles.otakusSearchItemEmptyContent}`}>
          <Empty
            imageStyle={{ height: 60 }}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={i18n('search_results_empty', '搜索结果为空')}
          />
        </div>
      </div>
    );
  return (
    <div
      className={`${styles.staffInput} ${showInputResult ? `${styles.staffInputWithResult}` : ''}`}
    >
      {contextHolder}
      <Input
        placeholder={searchPlaceholder}
        prefix={<PersonSearchSVG type="search" />}
        ref={inputRef}
        value={inputValue}
        allowClear
        onChange={(e) => setInputValue(e.target.value.trim())}
        onCompositionStart={handleCompositionStart}
        onCompositionEnd={handleCompositionEnd}
        onKeyDown={handleKeyDown}
      />
      {showInputResult && (
        <div className={styles.staffInputContainer}>
          {dataLoaded ? (
            showDeptDetail ? (
              <>
                <BreadCrumbComponent items={breadcrumbs} onBreadcrumbClick={onBreadcrumbClick} />
                {!drilldownLoading ? (
                  <>
                    <DeptDetail
                      currentDept={currentDept}
                      currentDeptHierarchy={currentDeptHierarchy}
                      inputValue={inputValue}
                      checkedList={checkedList}
                      selectedList={selectedList}
                      onSelected={onSelected}
                      handleShowDeptDetailChange={setShowDeptDetail}
                      onDeptClick={onDeptClick}
                    />
                  </>
                ) : (
                  <div className={styles.staffInputLoading}>
                    <div className={`${styles.otakusSearchItemEmptyContent} is-loading`}>
                      <PersonSearchSVG type={'searchLoading'} />
                      <span>{i18n('Loading', '加载中...')}</span>
                    </div>
                  </div>
                )}
              </>
            ) : showBatchResult ? (
              <BatchSelectResult
                checkedList={checkedList}
                onSelected={onSelected}
                batchResult={batchResult}
                selectedList={selectedList}
              />
            ) : (
              searchResultRenderUI
            )
          ) : (
            <div className={styles.staffInputLoading}>
              <div className={`${styles.otakusSearchItemEmptyContent} is-loading`}>
                <PersonSearchSVG type={'searchLoading'} />
                <span>{i18n('Loading', '加载中...')}</span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PersonInput;
