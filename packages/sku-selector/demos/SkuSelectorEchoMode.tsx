import React, { useState } from 'react';
import { ThemeProvider, Button, Space } from '@otakus/design';
import { SkuSelector } from '@otakus/sku-selector';
import { MyOpenService } from './service';
import { ISku, IPurchaseType } from '@otakus/sku-selector-utils';

export const SkuSelectorEchoMode: React.FC = () => {
  const [echoMode, setEchoMode] = useState<'card' | 'item' | 'input'>('card');
  const [status, setStatus] = useState<'default' | 'error' | 'warning'>('default');
  const [selectedValue, setSelectedValue] = useState<ISku | IPurchaseType | null>(null);

  const service = new MyOpenService({
    collectSceneId: 'sku_selector_base',
    recentSceneId: 'sku_selector_base',
    bizParams: {
      flow_types: [1, 3, 4, 5, 7],
      stock_type: 'normal',
      applicant: 'baoying.zhao',
      department_code: '1429>1438>0954',
      apply_type: 'common',
      admin_request: false
    }
  });

  const handleChange = (item: ISku | IPurchaseType) => {
    setSelectedValue(item);
  };

  const handleToggleEchoMode = (mode: 'card' | 'item' | 'input') => {
    setEchoMode(mode);
  };

  const handleToggleStatus = (newStatus: 'default' | 'error' | 'warning') => {
    setStatus(newStatus);
  };

  const handleClear = () => {
    setSelectedValue(null);
  };

  return (
    <ThemeProvider>
      <div style={{ width: '100%' }}>
        <Space style={{ marginBottom: 16 }}>
          <strong>切换回显模式：</strong>
          <Button
            type={echoMode === 'card' ? 'primary' : 'default'}
            onClick={() => handleToggleEchoMode('card')}
          >
            卡片模式 (card)
          </Button>
          <Button
            type={echoMode === 'item' ? 'primary' : 'default'}
            onClick={() => handleToggleEchoMode('item')}
          >
            条目模式 (item)
          </Button>
          <Button
            type={echoMode === 'input' ? 'primary' : 'default'}
            onClick={() => handleToggleEchoMode('input')}
          >
            输入模式 (input)
          </Button>
        </Space>

        <Space style={{ marginBottom: 16, width: '100%' }}>
          <strong> 切换状态：</strong>
          <Button
            type={status === 'default' ? 'primary' : 'default'}
            onClick={() => handleToggleStatus('default')}
          >
            默认状态 (default)
          </Button>
          <Button
            type={status === 'error' ? 'primary' : 'default'}
            onClick={() => handleToggleStatus('error')}
          >
            错误状态 (error)
          </Button>
          <Button
            type={status === 'warning' ? 'primary' : 'default'}
            onClick={() => handleToggleStatus('warning')}
          >
            警告状态 (warning)
          </Button>
        </Space>

        <Button danger onClick={handleClear} disabled={!selectedValue} style={{ marginBottom: 16 }}>
          清空选择
        </Button>

        <div
          style={{
            border: '2px dashed #d9d9d9',
            padding: 20,
            borderRadius: 8
          }}
        >
          <SkuSelector
            service={service}
            placeholder="请选择采购类别或单品（回显模式演示）"
            echoMode={echoMode}
            status={status}
            value={selectedValue}
            onChange={handleChange}
            versionCompareUrl="https://km.mihoyo.com/doc/mhok5f0ecdu8?sheetId=5qRDtgp"
            canSelectEndLevelPurchaseType={true}
            canSelectNotEndLevelPurchaseType={true}
          />
        </div>
      </div>
    </ThemeProvider>
  );
};

export default SkuSelectorEchoMode;
