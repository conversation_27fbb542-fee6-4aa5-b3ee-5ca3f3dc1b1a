import {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  useEffect,
  useMemo,
  useCallback
} from 'react';
import cls from 'classnames';
import { Dropdown } from '@otakus/design';
import { PlusCircleOutlined } from '@otakus/icons';
import { TagOption, TagFilterProps, TagFilterRef } from './type';
import './index.scss';

// Constants
const DROPDOWN_TRIGGER_WIDTH = 40;
const SAFE_MARGIN = 8;
const MAX_DROPDOWN_HEIGHT = 400;

// Helper functions
const getLabel = (item: TagOption): string =>
  `${item.label || ''} ${item.count !== undefined ? `(${item.count})` : ''}`;

function dynamicRenderList({
  list,
  container,
  itemRender
}: {
  list: TagOption[];
  container: HTMLDivElement | null | undefined;
  itemRender(item: TagOption): HTMLDivElement;
}): number {
  if (!container || !list.length) {
    return -1;
  }

  const { width: containerWidth } = container.getBoundingClientRect();
  const fakeContainer = container.cloneNode() as HTMLDivElement;
  fakeContainer.style.cssText = `
    position: absolute;
    top: -9999px;
    left: -9999px;
    opacity: 0;
    pointer-events: none;
  `;
  container.parentNode?.appendChild(fakeContainer);

  let stopAt = list.length;
  let realWidth = 0;
  let safeMargin = SAFE_MARGIN;

  try {
    do {
      stopAt--;
      if (stopAt < list.length - 1) {
        safeMargin = DROPDOWN_TRIGGER_WIDTH;
      }

      // Use DocumentFragment for better performance
      const fragment = document.createDocumentFragment();
      list.forEach((item, index) => {
        if (index > stopAt) return;
        fragment.appendChild(itemRender(item));
      });

      fakeContainer.innerHTML = '';
      fakeContainer.appendChild(fragment);
      realWidth = fakeContainer.getBoundingClientRect().width;
    } while (stopAt > 0 && realWidth + safeMargin > containerWidth);

    return stopAt;
  } finally {
    // Clean up to prevent memory leaks
    container.parentNode?.removeChild(fakeContainer);
  }
}

export const TagFilter = forwardRef<TagFilterRef, TagFilterProps>((props, ref) => {
  const {
    options = [],
    defaultValue = '',
    value,
    dynamicItems,
    maxTagCount = 'responsive',
    onChange,
    className,
    style
  } = props;

  // 处理 dynamicItems 和 maxTagCount 的兼容性
  const effectiveMaxTagCount = useMemo(() => {
    // 如果明确设置了 maxTagCount，以它为准
    if (maxTagCount !== 'responsive' || props.maxTagCount !== undefined) {
      return maxTagCount;
    }

    // 兼容处理 dynamicItems
    if (dynamicItems === false) {
      return -1; // 相当于显示所有标签
    }

    return 'responsive'; // dynamicItems === true 或 undefined
  }, [maxTagCount, dynamicItems, props.maxTagCount]);

  const [selected, setSelected] = useState<string>(defaultValue);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [stopAt, setStopAt] = useState<number>(-1);

  // Memoize the item render function for better performance
  const itemRender = useCallback((item: TagOption) => {
    const div = document.createElement('div');
    div.className = 'otakus-tag-filter-item';
    div.innerText = getLabel(item);
    return div;
  }, []);

  const updateRenderList = useCallback((): number => {
    if (!options.length) {
      return -1;
    }

    // 如果 maxTagCount 为 -1，显示所有标签
    if (effectiveMaxTagCount === -1) {
      setStopAt(options.length - 1);
      return -1;
    }

    // 如果 maxTagCount 为正整数，直接使用该值
    if (typeof effectiveMaxTagCount === 'number' && effectiveMaxTagCount > 0) {
      const actualMaxCount = Math.min(effectiveMaxTagCount, options.length);
      setStopAt(actualMaxCount - 1);
      return -1;
    }

    // 如果 maxTagCount 为 'responsive'，使用动态计算
    setStopAt(-1);
    const rafId = requestAnimationFrame(() => {
      if (containerRef.current) {
        const newStopAt = dynamicRenderList({
          list: options,
          container: containerRef.current,
          itemRender
        });
        setStopAt(newStopAt);
      }
    });

    return rafId;
  }, [options, effectiveMaxTagCount, itemRender]);

  // Handle controlled component behavior
  useEffect(() => {
    if (value !== undefined) {
      setSelected(value);
    }
  }, [value]);

  // Initialize with defaultValue
  useEffect(() => {
    if (value === undefined && defaultValue !== undefined) {
      setSelected(defaultValue);
    }
  }, [defaultValue, value]);

  // Handle window resize (only when in responsive mode)
  useEffect(() => {
    if (effectiveMaxTagCount === 'responsive') {
      window.addEventListener('resize', updateRenderList);
      return () => {
        window.removeEventListener('resize', updateRenderList);
      };
    }
  }, [effectiveMaxTagCount, updateRenderList]);

  // Update render list when options or maxTagCount change
  useEffect(() => {
    const rafId = updateRenderList();
    return () => {
      cancelAnimationFrame(rafId);
    };
  }, [updateRenderList]);

  // Expose methods to parent component
  useImperativeHandle(
    ref,
    () => ({
      updateRenderList
    }),
    [updateRenderList]
  );

  // Handle item selection
  const handleItemSelect = useCallback(
    (itemValue: string, item: TagOption) => {
      if (value === undefined) {
        // Only update internal state for uncontrolled component
        setSelected(itemValue);
      }

      if (typeof onChange === 'function' && selected !== itemValue) {
        onChange(itemValue, item);
      }
    },
    [onChange, selected, value]
  );

  // Memoize dropdown menu items
  const dropdownItems = useMemo(() => {
    if (stopAt < 0 || stopAt >= options.length - 1) return [];

    return options.slice(stopAt + 1).map((item) => ({
      key: item.value || '',
      label: getLabel(item)
    }));
  }, [options, stopAt]);

  // Memoize the visible tag items
  const visibleTagItems = useMemo(() => {
    if (stopAt < 0) return [];
    return options.slice(0, stopAt + 1);
  }, [options, stopAt]);

  return (
    <div
      className={cls('otakus-tag-filter', className)}
      style={style}
      ref={containerRef}
      data-testid="tag-filter"
    >
      {visibleTagItems.map((item) => (
        <div
          key={item.value}
          role="button"
          tabIndex={0}
          aria-pressed={selected === item.value}
          className={cls(
            'otakus-tag-filter-item',
            selected === item.value && 'otakus-tag-filter-item--selected'
          )}
          onClick={() => handleItemSelect(item.value || '', item)}
          // onKeyDown={(e) => {
          //   if (e.key === 'Enter' || e.key === ' ') {
          //     e.preventDefault();
          //     handleItemSelect(item.value || '', item);
          //   }
          // }}
        >
          {getLabel(item)}
        </div>
      ))}

      {dropdownItems.length > 0 && (
        <Dropdown
          menu={{
            items: dropdownItems,
            selectable: true,
            selectedKeys: [selected],
            onSelect: ({ key }) => {
              const selectedItem = options.find((item) => item.value === key);
              if (selectedItem) {
                handleItemSelect(key, selectedItem);
              }
            },
            style: {
              maxHeight: MAX_DROPDOWN_HEIGHT,
              overflowX: 'auto'
            }
          }}
          placement="bottomRight"
          overlayClassName="otakus-tag-filter-dropdown"
        >
          <div role="button" tabIndex={0} aria-label="More options">
            <PlusCircleOutlined
              style={{
                padding: 8,
                fontSize: 16,
                color: 'var(--otakus-color-text-secondary, #3470FF)',
                cursor: 'pointer'
              }}
            />
          </div>
        </Dropdown>
      )}
    </div>
  );
});

TagFilter.displayName = 'TagFilter';

export default TagFilter;
