/**
 * description: 垂直带边框和背景颜色的列表。
 */
import React from 'react';
import { Badge, Descriptions } from '@otakus/design';
import type { DescriptionsProps } from '@otakus/design';

const items: DescriptionsProps['items'] = [
  {
    key: '1',
    label: 'Product',
    children: 'Cloud Database'
  },
  {
    key: '2',
    label: 'Billing Mode',
    children: 'Prepaid'
  },
  {
    key: '3',
    label: 'Automatic Renewal',
    children: 'YES'
  },
  {
    key: '4',
    label: 'Order time',
    children: '2018-04-24 18:00:00'
  },
  {
    key: '5',
    label: 'Usage Time',
    span: 2,
    children: '2019-04-24 18:00:00'
  },
  {
    key: '6',
    label: 'Status',
    span: 3,
    children: <Badge status="processing" text="Running" />
  },
  {
    key: '7',
    label: 'Negotiated Amount',
    children: '$80.00'
  },
  {
    key: '8',
    label: 'Discount',
    children: '$20.00'
  },
  {
    key: '9',
    label: 'Official Receipts',
    children: '$60.00'
  },
  {
    key: '10',
    label: 'Config Info',
    children: (
      <>
        Data disk type: MongoDB
        <br />
        Database version: 3.4
        <br />
        Package: dds.mongo.mid
        <br />
        Storage space: 10 GB
        <br />
        Replication factor: 3
        <br />
        Region: East China 1
        <br />
      </>
    )
  }
];

const App: React.FC = () => (
  <Descriptions title="User Info" layout="vertical" bordered items={items} />
);

export default App;
